---
description: 
globs: 
alwaysApply: true
---
{
  "projectRules": {
    "ruby_on_rails_microservice": {
      "description": "Ruby on Rails microservice with WhatsApp Business API integration",
      "rules": [
        "Follow Rails 6.1 conventions and best practices",
        "Use API-only controllers with versioning (V1:: namespace)",
        "All business logic must be in service objects inheriting from ApplicationService",
        "Use self.call(*args) pattern for service classes",
        "Services should be organized in namespaced directories (e.g., Message::, User::)",
        "Controllers should be skinny - only handle HTTP concerns and delegate to services",
        "Use strong parameters with permit_params method in controllers",
        "Convert camelCase parameters to snake_case using underscorize_params",
        "All models should include SoftDeletable concern for soft deletes",
        "Use multi-tenant architecture with tenant_id in all models",
        "Complex database queries should use query objects in app/queries/",
        "Use raw SQL for complex queries, ActiveRecord for simple ones",
        "All JSON responses must use Jbuilder with modular serializers",
        "Serializers should be organized in namespaced modules (e.g., MessageSerializer::)",
        "Use custom exception classes from ExceptionHandler module",
        "Error format: 'errorCode||message' with proper HTTP status codes",
        "Authentication uses JWT tokens with user context in Thread.current['user']",
        "Authorization based on granular permissions stored in Thread.current['auth']",
        "Background jobs must inherit from ApplicationJob and use Sidekiq",
        "Use FactoryBot for test data, RSpec for testing framework",
        "All services must have comprehensive test coverage",
        "Use DatabaseCleaner for test isolation",
        "Mock external HTTP calls with WebMock",
        "Use BunnyMock for message queue testing in specs",
        "Follow existing naming conventions: V1::Controllers, Service::NamespacedServices",
        "Use concerns for reusable cross-cutting functionality",
        "Implement proper error handling with handle_errors method in services",
        "Use will_paginate for pagination",
        "All API endpoints should be RESTful with proper HTTP methods",
        "Use conditional field masking in serializers for privacy",
        "Background jobs should have retry configuration with sidekiq_options",
        "Use raw SQL queries for complex joins and aggregations",
        "Implement proper logging with Rails.logger",
        "Use environment variables for configuration",
        "Follow existing file organization: app/services/, app/serializers/, spec/services/",
        "Use custom query objects for complex database operations",
        "Implement proper validation in models and services",
        "Use proper HTTP status codes (200, 201, 400, 401, 403, 404, 422, 500)",
        "All new code must have corresponding test coverage",
        "Use existing helper methods and utilities when available",
        "Follow existing error code patterns from ErrorCode model",
        "Use proper database indexes for performance",
        "Implement proper security measures for API endpoints",
        "Use existing authentication and authorization patterns",
        "Follow existing serialization patterns for API responses",
        "Use proper database transactions where needed",
        "Implement proper caching strategies when appropriate",
        "Use existing service patterns for external API integrations",
        "Follow existing webhook handling patterns",
        "Use proper database constraints and validations",
        "Implement proper audit trails where needed",
        "Use existing job queue patterns for background processing",
        "Follow existing file upload and storage patterns",
        "Use proper database migrations for schema changes",
        "Implement proper API versioning strategy",
        "Use existing monitoring and logging patterns",
        "Follow existing security and privacy patterns",
        "Use proper database connection pooling",
        "Implement proper rate limiting where needed",
        "Use existing notification and messaging patterns",
        "Follow existing data validation and sanitization patterns",
        "Use proper database backup and recovery strategies",
        "Implement proper error reporting and monitoring",
        "Use existing performance optimization patterns",
        "Follow existing code review and quality assurance processes"
      ],
      "filePatterns": {
        "controllers": "app/controllers/v1/*_controller.rb",
        "services": "app/services/**/*.rb",
        "models": "app/models/*.rb",
        "serializers": "app/serializers/**/*.rb",
        "specs": "spec/**/*_spec.rb",
        "factories": "spec/factories/*.rb",
        "jobs": "app/jobs/*.rb",
        "queries": "app/queries/*.rb"
      },
      "conventions": {
        "naming": {
          "controllers": "V1::ResourceController",
          "services": "Namespace::ServiceName",
          "serializers": "ResourceSerializer::Variant",
          "models": "Resource (singular)",
          "specs": "ResourceSpec",
          "factories": "resource"
        },
        "structure": {
          "service_method": "self.call(*args)",
          "error_format": "errorCode||message",
          "parameter_conversion": "camelCase to snake_case",
          "response_format": "JSON with Jbuilder",
          "test_framework": "RSpec with FactoryBot"
        }
      }
    }
  }
}
