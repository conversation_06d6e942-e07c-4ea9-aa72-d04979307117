GEM
  remote: https://rubygems.org/
  specs:
    actioncable (********)
      actionpack (= ********)
      activesupport (= ********)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      mail (>= 2.7.1)
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activesupport (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (********)
      actionpack (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      nokogiri (>= 1.8.5)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.6)
    activemodel (********)
      activesupport (= ********)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
    activestorage (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activesupport (= ********)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (********)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.5)
      public_suffix (>= 2.0.2, < 6.0)
    amq-protocol (2.3.2)
    aws-eventstream (1.2.0)
    aws-partitions (1.800.0)
    aws-sdk-core (3.180.2)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.5)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.71.0)
      aws-sdk-core (~> 3, >= 3.177.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.132.0)
      aws-sdk-core (~> 3, >= 3.179.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.6)
    aws-sigv4 (1.6.0)
      aws-eventstream (~> 1, >= 1.0.2)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.3.0)
    bunny (2.22.0)
      amq-protocol (~> 2.3, >= 2.3.1)
      sorted_set (~> 1, >= 1.0.2)
    bunny-mock (1.7.0)
      bunny (>= 1.7)
    byebug (11.1.3)
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    diff-lcs (1.5.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    erubi (1.13.0)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (3.2.0)
      i18n (>= 1.8.11, < 2)
    ffi (1.15.5)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.0.1)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json-schema (3.0.0)
      addressable (>= 2.8)
    jwt (2.7.1)
    listen (3.8.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    method_source (1.1.0)
    mime-types (3.5.0)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0808)
    mini_mime (1.1.5)
    minitest (5.25.4)
    msgpack (1.7.2)
    multi_json (1.15.0)
    net-imap (0.4.18)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.16.8-x86_64-linux)
      racc (~> 1.4)
    pg (1.5.3)
    phonelib (0.10.8)
    prometheus-client (4.2.1)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    public_suffix (5.0.3)
    puma (5.6.9)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.10)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (********)
      actioncable (= ********)
      actionmailbox (= ********)
      actionmailer (= ********)
      actionpack (= ********)
      actiontext (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      bundler (>= 1.15.0)
      railties (= ********)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.1)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rbtree (0.4.6)
    redis (4.8.1)
    redis-namespace (1.11.0)
      redis (>= 4)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.3.9)
    rspec-core (3.12.2)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.6)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (4.0.2)
      actionpack (>= 4.2)
      activesupport (>= 4.2)
      railties (>= 4.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.12.1)
    rswag-api (2.10.1)
      railties (>= 3.1, < 7.1)
    rswag-specs (2.10.1)
      activesupport (>= 3.1, < 7.1)
      json-schema (>= 2.2, < 4.0)
      railties (>= 3.1, < 7.1)
      rspec-core (>= 2.14)
    rswag-ui (2.10.1)
      actionpack (>= 3.1, < 7.1)
      railties (>= 3.1, < 7.1)
    set (1.0.3)
    shoulda-matchers (5.3.0)
      activesupport (>= 5.2.0)
    sidekiq (6.5.10)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    silencer (2.0.0)
    sorted_set (1.0.3)
      rbtree
      set (~> 1.0)
    spring (4.1.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    thor (1.3.2)
    timeout (0.4.2)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    webmock (3.18.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (4.0.0)
    zeitwerk (2.6.18)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3 (~> 1)
  bootsnap (>= 1.4.4)
  bunny
  bunny-mock
  byebug
  database_cleaner
  dotenv-rails
  factory_bot_rails
  faker
  jbuilder
  jwt
  listen (~> 3.3)
  multi_json
  pg (~> 1.1)
  phonelib
  prometheus-client
  pry
  puma (~> 5.6.8)
  rails (~> *******)
  redis-namespace
  rest-client
  rexml (~> 3.3.9)
  rspec-rails (~> 4.0.0)
  rswag-api
  rswag-specs
  rswag-ui
  shoulda-matchers
  sidekiq (= 6.5.10)
  silencer
  spring
  tzinfo-data
  webmock
  will_paginate

RUBY VERSION
   ruby 3.0.0p0

BUNDLED WITH
   2.2.14
