podTemplate(
  containers: [
    containerTemplate(name: 'ruby', alwaysPullImage: true, image: 'ruby:3.0.0', args: 'cat', ttyEnabled: true),
    containerTemplate(name: 'docker', image: 'docker', command: 'cat', ttyEnabled: true)
  ],
  volumes: [
    hostPathVolume(mountPath: '/var/run/docker.sock', hostPath: '/var/run/docker.sock')
  ],
  imagePullSecrets: ['registry-credentials']) {
  def webhookURL = "https://outlook.office.com/webhook/14bd0fd8-1bd7-43dd-bdf5-2f0e89c4ec50@46ecb5f0-1227-4755-a101-64d39a05e1c7/JenkinsCI/752745c8a19a45caae6f2bc8a2a9df07/ad82656b-8630-4724-81d2-b76ce5380247"

  node(POD_LABEL) {
    try {
      def gitBranch
      def gitCommit
      def targetBranch
      def dockerRegistry = 'nexus.sling-dev.com:8123'
      def message = "Starting Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})'"
      def status = "Starting"
      def color = "a0a0a0"

      container('ruby') {
        stage('Git Checkout') {
          def gitRepo = checkout scm
          gitCommit = gitRepo.GIT_COMMIT
          gitBranch = gitRepo.GIT_BRANCH
          targetBranch = env.CHANGE_TARGET
          currentBuild.description = "branch ${gitBranch}"
          sh "echo $gitCommit"
          sh "echo $gitBranch"
          sh "echo $env.CHANGE_TARGET"
        }
        stage('Bundle install') {
          sh 'bundle install'
        }
        stage('Test') {
          //sh 'bundle exec rspec'
        }
      }
      container('docker') {
        def imageName = "${dockerRegistry}/sling/sd-message"
        def dbMigrationImageName = "${dockerRegistry}/sling/sd-message/db-migration"
        def sidekiqImageName = "${dockerRegistry}/sling/sd-message/sidekiq"
        def gitTag = "${imageName}:${gitCommit}"
        def dbMigrationGitTag = "${dbMigrationImageName}:${gitCommit}"
        def sidekiqGitTag = "${sidekiqImageName}:${gitCommit}"
        def pullRequestTag = "${imageName}:${gitBranch}"
        def dbMigrationPullRequestTag = "${dbMigrationImageName}:${gitBranch}"
        def sidekiqPullRequestTag = "${sidekiqImageName}:${gitBranch}"
        def latestTag = "${imageName}:latest"
        def dbMigrationLatestTag = "${dbMigrationImageName}:latest"
        def sidekiqLatestTag = "${sidekiqImageName}:latest"

        if (gitBranch.startsWith('PR-') && targetBranch == 'main') {
          pullRequestTag = "${imageName}:RC-${env.BUILD_ID}"
          dbMigrationPullRequestTag = "${dbMigrationImageName}:RC-${env.BUILD_ID}"
          sidekiqPullRequestTag = "${sidekiqImageName}:RC-${env.BUILD_ID}"
        }

        withCredentials([[$class          : 'UsernamePasswordMultiBinding',
                        credentialsId   : 'docker-sling-registry',
                        usernameVariable: 'DOCKER_USER',
                        passwordVariable: 'DOCKER_PASSWORD']]) {
          stage('Prepare DB migrations') {
            sh script: "docker login ${dockerRegistry} -u ${DOCKER_USER} -p ${DOCKER_PASSWORD}", label: "Docker Login"
            if (gitBranch.startsWith('PR-')) {
              sh script: "docker build -f ./Migration-Dockerfile -t ${dbMigrationPullRequestTag} .", label: "Build dbMigration migrations image with Pull Request tag"
              sh script: "docker push ${dbMigrationPullRequestTag}", label: "Push to registry"
            }
            if (gitBranch == 'dev') {
              sh script: "docker build -f ./Migration-Dockerfile -t ${dbMigrationGitTag} .", label: "Build dbMigration migrations image with git commit hash"

              sh script: "docker push ${dbMigrationGitTag}", label: "Push to registry"
            }
            if (gitBranch == 'main') {
              def dbMigrationReleaseCandidateTag = "${dbMigrationImageName}:Release-${env.BUILD_ID}"
              sh script: "docker build -f ./Migration-Dockerfile -t ${dbMigrationGitTag} .", label: "Build dbMigration migrations image with git commit hash"
              sh script: "docker tag ${dbMigrationGitTag} ${dbMigrationReleaseCandidateTag}", label: "Tag image as release candidate"
              sh script: "docker tag ${dbMigrationGitTag} ${dbMigrationLatestTag}", label: "Tag image as latest candidate"

              sh script: "docker push ${dbMigrationGitTag}", label: "Push to registry"
              sh script: "docker push ${dbMigrationReleaseCandidateTag}", label: "Push release candidate image to registry"
              sh script: "docker push ${dbMigrationLatestTag}", label: "Push latest image to registry"
            }
          }

          stage('Prepare Sidekiq Docker Image') {
            sh script: "docker login ${dockerRegistry} -u ${DOCKER_USER} -p ${DOCKER_PASSWORD}", label: "Docker Login"
            if (gitBranch.startsWith('PR-')) {
              sh script: "docker build -f ./Sidekiq-Dockerfile -t ${sidekiqPullRequestTag} .", label: "Build sidekiq image with Pull Request tag"
              sh script: "docker push ${sidekiqPullRequestTag}", label: "Push to registry"
            }
            if (gitBranch == 'dev') {
              sh script: "docker build -f ./Sidekiq-Dockerfile -t ${sidekiqGitTag} .", label: "Build sidekiq image with git commit hash"

              sh script: "docker push ${sidekiqGitTag}", label: "Push to registry"
            }
            if (gitBranch == 'main') {
              def sidekiqReleaseCandidateTag = "${sidekiqImageName}:Release-${env.BUILD_ID}"
              sh script: "docker build -f ./Sidekiq-Dockerfile -t ${sidekiqGitTag} .", label: "Build sidekiq image with git commit hash"
              sh script: "docker tag ${sidekiqGitTag} ${sidekiqReleaseCandidateTag}", label: "Tag image as release candidate"
              sh script: "docker tag ${sidekiqGitTag} ${sidekiqLatestTag}", label: "Tag image as latest candidate"

              sh script: "docker push ${sidekiqGitTag}", label: "Push to registry"
              sh script: "docker push ${sidekiqReleaseCandidateTag}", label: "Push release candidate image to registry"
              sh script: "docker push ${sidekiqLatestTag}", label: "Push latest image to registry"
            }
          }

          stage('Publish Docker Image') {
            sh script: "docker login ${dockerRegistry} -u ${DOCKER_USER} -p ${DOCKER_PASSWORD}", label: "Docker Login"
            if (gitBranch.startsWith('PR-')) {
              sh script: "docker build --build-arg DOCKER_USER=${DOCKER_USER} --build-arg DOCKER_PASSWORD=${DOCKER_PASSWORD} -t ${pullRequestTag} .", label: "Build image with Pull Request tag"
              sh script: "docker push ${pullRequestTag}", label: "Push to registry"
            }
            if (gitBranch == 'dev') {
              sh script: "docker build --build-arg DOCKER_USER=${DOCKER_USER} --build-arg DOCKER_PASSWORD=${DOCKER_PASSWORD} -t ${gitTag} .", label: "Build image with git commit hash"

              sh script: "docker push ${gitTag}", label: "Push to registry"
            }
            if (gitBranch == 'main') {
              def releaseCandidateTag = "${imageName}:Release-${env.BUILD_ID}"
              sh script: "docker build --build-arg DOCKER_USER=${DOCKER_USER} --build-arg DOCKER_PASSWORD=${DOCKER_PASSWORD} -t ${gitTag} .", label: "Build image with git commit hash"
              sh script: "docker tag ${gitTag} ${releaseCandidateTag}", label: "Tag image as release candidate"
              sh script: "docker tag ${gitTag} ${latestTag}", label: "Tag image as latest candidate"

              sh script: "docker push ${gitTag}", label: "Push to registry"
              sh script: "docker push ${releaseCandidateTag}", label: "Push release candidate image to registry"
              sh script: "docker push ${latestTag}", label: "Push latest image to registry"
            }
          }
        }
      }
      if (gitBranch.startsWith('PR-')) {
        try {
          if (targetBranch == 'main') {
            stage('Approval for Deployment') {
              userInput = input(id: 'confirm', message: 'Do you wish to deploy the PR to STAGE environment?',
                  parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current PR in STAGE environment', name: 'confirm']])
            }
            stage('Start Deployments') {
              build job: '../deploy-to-stage',
                  parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: "RC-${env.BUILD_ID}"],
                              [$class: 'StringParameterValue', name: 'branchName', value: gitBranch],
                              [$class: 'StringParameterValue', name: 'targetBranch', value: targetBranch]],
                  wait: false
            }
          }else{
            stage('Approval for Deployment') {
              userInput = input(id: 'confirm', message: 'Do you wish to deploy the PR to QA environment?',
                  parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current PR in QA environment', name: 'confirm']])
            }
            stage('Start Deployments') {
              build job: '../deploy-to-qa',
                  parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: gitBranch],
                              [$class: 'StringParameterValue', name: 'branchName', value: gitBranch],
                              [$class: 'StringParameterValue', name: 'targetBranch', value: targetBranch]],
                  wait: false
            }
          }
        } catch (err) {
          def user = err.getCauses()[0].getUser()
          userInput = false
          echo "Aborted by: [${user}]"
        }
      }
      if (gitBranch == 'dev') {
        stage('Start Deployments') {
          build job: '../deploy-to-qa',
              parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: gitCommit],
                           [$class: 'StringParameterValue', name: 'branchName', value: gitBranch]],
              wait: false
        }
      }

      if (gitBranch == 'main') {
        stage('Start Stage Deployment') {
          build job: '../deploy-to-stage',
              parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: "Release-${env.BUILD_ID}"],
                           [$class: 'StringParameterValue', name: 'triggeredByJob', value: "build-and-package-main : #${BUILD_NUMBER}"],
                           [$class: 'StringParameterValue', name: 'branchName', value: "main"]],
              wait: false
        }
      }
      if (currentBuild.currentResult == "SUCCESS") {
        status = "SUCCESS"
        color = "0bcc2c"
        message = "Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})' is Successful. Great job!"
      } else if (currentBuild.currentResult == "UNSTABLE") {
        status = "UNSTABLE"
        color = "d00000"
        message = "Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})' is UNSTABLE!"
      } else if (currentBuild.currentResult == "FAILURE") {
        status = "FAILURE"
        color = "d00000"
        message = "Build (${env.BUILD_ID}) for '${env.JOB_NAME} ({$BRANCH_NAME})' Failed!"
      }
      office365ConnectorSend message: message, status: status, webhookUrl: webhookURL, color: color
    } catch (exc) {
      office365ConnectorSend message: "Exception while Building", status: "FAILURE", webhookUrl: webhookURL,
        color: "d00000"
      throw exc
    }
  }
}

