class ApplicationController < ActionController::API
  include ExceptionHandler
  before_action :authenticate, :underscorize_params, except: :health

  def health
    head :ok
  end

  private

  def authenticate
    User::Authenticate.authenticate(request.headers)
  end

  def underscorize_params(skip_nested_keys: false)
    permitted_params = permit_params
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data) unless permitted_params.permitted?
    UnderscorizeKeys.do(permitted_params.to_h, skip_nested_keys: skip_nested_keys).with_indifferent_access
  end
end
