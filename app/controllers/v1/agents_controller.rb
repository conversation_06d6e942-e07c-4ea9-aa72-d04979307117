# frozen_string_literal: true

module V1
  class AgentsController < ApplicationController
    def index
      render json: AgentsSerializer.new.serialize(AgentsService.new(underscorize_params).get_all)
    end

    def save
      AgentsService.new(underscorize_params).save
      render json: ''
    end

    private

    def permit_params
      case action_name
      when 'index'
        params.permit(:connected_account_id, :entity_type)
      when 'save'
        params.permit(:connected_account_id, :entity_type, agents: [:id, :name])
      end
    end
  end
end
