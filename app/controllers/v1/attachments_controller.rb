module V1
  class AttachmentsController < ApplicationController
    skip_before_action :underscorize_params

    def show
      attachment = Attachment.find_by(id: params[:id], message_id: params[:message_id])
      raise(ActiveRecord::RecordNotFound, ErrorCode.not_found) unless attachment.present?

      result = S3::GetPresignedUrl.new(attachment.file_name, attachment.extract_file_name, S3_ATTACHMENT_BUCKET).call
      json_response(result)
    end
  end
end
