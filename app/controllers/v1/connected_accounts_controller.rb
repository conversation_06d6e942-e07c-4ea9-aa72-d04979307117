# frozen_string_literal: true

module V1
  class ConnectedAccountsController < ApplicationController
    def index
      render json: ConnectedAccountsSerializer.new.serialize(ConnectedAccountService.new({}).get_all)
    end

    def show
      render json: ConnectedAccountSerializer.new.serialize(ConnectedAccountService.new(underscorize_params).show), status: :ok
    end

    def create
      connected_account = ConnectedAccountService.new(underscorize_params).create
      render json: ConnectedAccountSerializer.new.serialize(connected_account), status: :created
    end

    def update
      connected_account = ConnectedAccountService.new(underscorize_params).update
      render json: ConnectedAccountSerializer.new.serialize(connected_account)
    end

    def activate
      ConnectedAccountService.new(underscorize_params).activate
      render json: '', status: :ok
    end

    def deactivate
      ConnectedAccountService.new(underscorize_params).deactivate
      render json: '', status: :ok
    end

    def request_code
      ConnectedAccountService.new(underscorize_params).send_verification_code
      render json: '', status: :ok
    end

    def verify_code
      ConnectedAccountService.new(underscorize_params).verify_code
      render json: '', status: :ok
    end

    def lookup
      render json: ConnectedAccountsSerializer.new.lookup(ConnectedAccountService.new(underscorize_params).lookup)
    end

    def entity_phones
      render json: ConnectedAccountService.new(underscorize_params).entity_phones, status: :ok
    end

    private

    def permit_params
      case action_name
      when 'show', 'activate', 'deactivate'
        params.permit(:id)
      when 'create'
        params.permit(:phoneNumberId, :wabaId, :authCode, entitiesToCreate: []).tap { |parameters| parameters[:entitiesToCreate] ||= [] }
      when  'update'
        params.permit(:id, :displayName, entitiesToCreate: [])
      when 'request_code'
        params.permit(:id, :codeMethod, :language)
      when 'verify_code'
        params.permit(:id, :otpCode)
      when 'lookup'
        params.permit(:q, :entityType, :view)
      when 'entity_phones'
        params.permit(:id, :entity_type, :entity_id)
      else
        params.permit!
      end
    end
  end
end
