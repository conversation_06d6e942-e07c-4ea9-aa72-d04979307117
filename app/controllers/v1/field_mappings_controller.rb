# frozen_string_literal: true

module V1
  class FieldMappingsController < ApplicationController
    def index
      render json: FieldMappingSerializer.new.serialize(FieldMappingsService.new(underscorize_params).get)
    end

    def create
      render json: FieldMappingSerializer.new.serialize(FieldMappingsService.new(underscorize_params).create_or_update)
    end

    private

    def permit_params
      case action_name
      when 'index'
        params.permit(:connected_account_id, :entity_type)
      when 'create'
        params.permit(:connected_account_id, :entity_type, :campaign, :source, :subSource, :utmSource, :utmCampaign, :utmMedium, :utmContent, :utmTerm)
      end
    end
  end
end
