module V1
  class InteraktWebhooksController < ApplicationController
    skip_before_action :authenticate, only: [:handler, :challenge]
    skip_before_action :underscorize_params, only: [:handler, :challenge]

    def handler
      PublishEvent.call(Event::InteraktWebhook.new(handler_params.to_h))

      render status: :ok, json: ''
    end

    def challenge
      render plain: params['hub.challenge']
    end

    private

    def handler_params
      params.require(:interakt_webhook).permit!
    end
  end
end
