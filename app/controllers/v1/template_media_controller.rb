# frozen_string_literal: true

module V1
  class TemplateMediaController < ApplicationController
    def upload
      render json: TemplateMediaSerializer.new(TemplateMediaService.new(underscorize_params).upload).serialize, status: :created
    end

    def show
      render json: TemplateMediaSerializer.new(TemplateMediaService.new(underscorize_params).get).serialize_get
    end

    private

    def permit_params
      case action_name
      when 'upload'
        params.permit(:connected_account_id, :mediaFile)
      when 'show'
        params.permit(:connected_account_id, :id)
      end
    end
  end
end
