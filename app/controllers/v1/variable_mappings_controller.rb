# frozen_string_literal: true

module V1
  class VariableMappingsController < ApplicationController
    def index
      render json: VariableMappingsSerializer.new(VariableMappingsService.new(underscorize_params).get).serialize
    end

    def save
      render json: VariableMappingsSerializer.new(VariableMappingsService.new(underscorize_params).save).serialize
    end

    private

    def permit_params
      case action_name
      when 'index'
        params.permit(:id)
      when 'save'
        params.permit(:id, variableMappings: [:id, :componentType, :templateVariable, :entity, :internalName, :fallbackValue, :fieldType])
      end
    end
  end
end
