module V1
  class WebhooksController < ApplicationController
    skip_before_action :authenticate, only: [:handler, :challenge]
    skip_before_action :underscorize_params, only: [:handler, :challenge]

    def handler
      PublishEvent.call(Event::WhatsappWebhook.new(params.permit!.slice(:entry, :object).to_h))

      render status: :ok, json: ''
    end

    def challenge
      render plain: params['hub.challenge']
    end
  end
end
