# frozen_string_literal: true

module V1
  class WhatsappCreditsController < ApplicationController
    def summary
      render json: WhatsappCreditsSerializer.new.serialize_summary(WhatsappCreditsService.new.get_summary)
    end

    def history
      render json: WhatsappCreditsSerializer.new.serialize_history(WhatsappCreditsService.new.get_history(params))
    end

    def status
      render json: WhatsappCreditsSerializer.new.serialize_status(WhatsappCreditsService.new.get_status)
    end

    private

    def permit_params
      case action_name
      when 'summary'
        params.permit!
      when 'history'
        params.permit(:page, :size, jsonRule: {})
      when 'status'
        params.permit!
      end
    end
  end
end
