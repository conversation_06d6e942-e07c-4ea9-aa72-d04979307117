# frozen_string_literal: true

class Listeners::ListenForContactCreatedV2
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, CONTACT_CREATED_V2_EVENT, CONTACT_CREATED_V2_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload.dig('entity', 'id')} for #{CONTACT_CREATED_V2_EVENT}"

      params = {
        entity_type: LOOKUP_CONTACT,
        entity_id: payload.dig('entity', 'id'),
        owner_id: payload.dig('entity', 'ownerId', 'id'),
        tenant_id: payload.dig('entity', 'tenantId'),
        phone_numbers: payload.dig('entity', 'phoneNumbers'),
        entity_name: "#{payload.dig('entity', 'firstName')} #{payload.dig('entity', 'lastName')}".strip  
      }

      ConversationService.new(params).associate_conversations_by_given_phone_numbers_with_entity
    end
  end
end
