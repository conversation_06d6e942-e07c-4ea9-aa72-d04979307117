class Listeners::ListenForContactDelete
  include Listeners::DomainListeners
  include ActiveModel::Valida<PERSON>

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, CONTACT_DELETED_EVENT, CONTACT_DELETED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload['id']} | tenantId - #{payload['tenantId']} | userId - #{payload['userId']} for #{CONTACT_DELETED_EVENT}"
      data = {}
      data[:user_id] = payload["userId"]
      data[:tenant_id] = payload["tenantId"]
      data[:entity_id] = payload["id"]
      Rails.logger.info "Delete message after contact delete request from user: #{data[:user_id]} tenant id #{data[:tenant_id]} for contact: #{data[:entity_id]}"

      DeleteMessageForEntity.call(LOOKUP_CONTACT, data, false)
      DeleteConversationsForEntity.call(LOOKUP_CONTACT, data)

      deleted_count = ContactDealAssociation.where(
        tenant_id: payload['tenantId'],
        contact_id: payload['id']
      ).delete_all

      Rails.logger.info "Deleted #{deleted_count} contact deal associations for deleted contact #{payload['id']}"
    end
  end
end
