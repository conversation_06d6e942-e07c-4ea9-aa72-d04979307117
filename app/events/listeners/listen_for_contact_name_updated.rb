class Listeners::ListenForContactNameUpdated
  include ActiveModel::Validations
  include Listeners::DomainList<PERSON><PERSON>

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, CONTACT_NAME_UPDATED_EVENT, CONTACT_NAME_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload['contactId']} | tenantId - #{payload['tenantId']} for #{CONTACT_NAME_UPDATED_EVENT}"

      name = "#{payload['firstName']} #{payload['lastName']}".strip
      LookUp.where(tenant_id: payload['tenantId'], entity_type: LOOKUP_CONTACT, entity_id: payload['contactId']).update_all(name: name)
    end
  end
end
