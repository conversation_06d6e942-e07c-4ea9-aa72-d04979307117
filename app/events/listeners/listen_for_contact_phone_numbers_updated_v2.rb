# frozen_string_literal: true

class Listeners::ListenForContactPhoneNumbersUpdatedV2
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT, CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload.dig('entity', 'id')} for #{CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT}"

      params = {
        entity_type: LOOKUP_CONTACT,
        entity_id: payload.dig('entity', 'id'),
        tenant_id: payload.dig('entity', 'tenantId'),
        extract_phone_numbers: true,
        new_phone_numbers: payload.dig('entity', 'phoneNumbers'),
        old_phone_numbers: payload.dig('oldEntity', 'phoneNumbers'),
        entity_name: "#{payload.dig('entity', 'firstName')} #{payload.dig('entity', 'lastName')}".strip
      }

      ConversationService.new(params).associate_conversations_by_given_phone_numbers_with_entity
    end
  end
end
