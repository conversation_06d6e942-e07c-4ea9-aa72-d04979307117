# frozen_string_literal: true

class Listeners::ListenForContactReassign
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, CONTACT_OWNER_UPDATED_EVENT, CONTACT_OWNER_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{CONTACT_OWNER_UPDATED_EVENT} tenant id #{payload['tenantId']} entity id #{payload['entityId']} new owner id #{payload['newOwnerId']}"

      LookUp.where(tenant_id: payload['tenantId'], entity_type: LOOKUP_CONTACT, entity_id: payload['entityId']).update_all(owner_id: payload['newOwnerId'])
    end
  end
end
