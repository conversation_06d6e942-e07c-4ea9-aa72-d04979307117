# frozen_string_literal: true

class Listeners::ListenForDailyScheduler
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(SCHEDULER_EXCHANGE, SCHEDULER_3AM_EVENT, SCHEDULER_3AM_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{SCHEDULER_3AM_EVENT}"

      TemplateMediaService.new({}).delete_unused_media
      ConversationService.new({}).cleanup_soft_deleted_conversations

      Rails.logger.info "Starting cleanup of soft-deleted messages"
      Message.soft_deleted.find_each do |message|
        begin
          Message::DeleteMessageService.new(
            message.id,
            false,
            { user_id: message.owner_id, tenant_id: message.tenant_id },
            true
          ).call
        rescue => e
          Rails.logger.error "Failed to cleanup soft-deleted message #{message.id}: #{e.message}"
        end
      end
      Rails.logger.info "Completed cleanup of soft-deleted messages"
    end
  end
end
