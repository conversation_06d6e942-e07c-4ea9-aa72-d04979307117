# frozen_string_literal: true

class Listeners::ListenForDealCreatedV2
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_CREATED_V2_EVENT, DEAL_CREATED_V2_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{DEAL_CREATED_V2_EVENT} deal id #{payload.dig('entity', 'id')} tenant id #{payload.dig('metadata', 'tenantId')}"

      begin
        new(payload).process_contact_associations
      rescue => e
        Rails.logger.error "Error processing deal created v2 event: #{e.message}"
      end
    end
  end

  def initialize(payload)
    @payload = payload
    @deal_id = payload.dig('entity', 'id')
    @deal_name = payload.dig('entity', 'name')
    @tenant_id = payload.dig('metadata', 'tenantId')
    @associated_contacts = extract_contact_ids(payload.dig('entity', 'associatedContacts'))
  end

  def process_contact_associations
    return if @deal_id.blank? || @tenant_id.blank?
    return if @associated_contacts.blank?

    Rails.logger.info "Deal #{@deal_id}: Creating associations for contacts #{@associated_contacts}"

    ActiveRecord::Base.transaction do
      add_contact_associations(@associated_contacts)
    end

    Rails.logger.info "Successfully created contact deal associations for deal #{@deal_id}"
  end

  private

  def extract_contact_ids(associated_contacts)
    return [] if associated_contacts.blank?
    
    associated_contacts.map { |contact| contact['id'] }.compact
  end

  def add_contact_associations(contact_ids)
    return if contact_ids.blank?

    contact_ids.each do |contact_id|
      ContactDealAssociation.find_or_create_by(
        tenant_id: @tenant_id,
        contact_id: contact_id,
        deal_id: @deal_id
      ) do |association|
        association.deal_name = @deal_name
      end
    end

    Rails.logger.info "Added #{contact_ids.size} contact deal associations for deal #{@deal_id}"
  end
end
