class Listeners::ListenForDealDelete
  include ActiveModel::Validations
  include Listeners::DomainList<PERSON><PERSON>

  def self.listen
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_DELETED_EVENT, DEAL_DELETED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload['id']} | tenantId - #{payload['tenantId']} | userId - #{payload['userId']} for #{DEAL_DELETED_EVENT}"
      data = {}
      data[:user_id] = payload["userId"]
      data[:tenant_id] = payload["tenantId"]
      data[:entity_id] = payload["id"]
      Rails.logger.info "Delete messages after deal delete request from user: #{data[:user_id]} tenant id #{data[:tenant_id]} for deal: #{data[:entity_id]}"

      DeleteMessageForEntity.call(LOOKUP_DEAL, data)

        deleted_count = ContactDealAssociation.where(
          tenant_id: payload['tenantId'],
          deal_id: payload['id']
        ).delete_all

        Rails.logger.info "Deleted #{deleted_count} contact deal associations for deleted deal #{payload['id']}"
    end
  end
end
