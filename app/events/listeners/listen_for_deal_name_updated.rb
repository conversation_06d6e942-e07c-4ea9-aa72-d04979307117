# frozen_string_literal: true

class Listeners::ListenForDealNameUpdated
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_NAME_UPDATED_EVENT, DEAL_NAME_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{DEAL_NAME_UPDATED_EVENT} deal id #{payload['id']} tenant id #{payload['tenantId']} new name: #{payload['name']}"

      updated_count = ContactDealAssociation.where(
        tenant_id: payload['tenantId'], 
        deal_id: payload['id']
      ).update_all(deal_name: payload['name'])

      Rails.logger.info "Updated #{updated_count} contact deal associations for deal #{payload['id']} with new name: #{payload['name']}"
    end
  end
end
