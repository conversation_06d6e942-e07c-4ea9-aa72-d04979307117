# frozen_string_literal: true

class Listeners::ListenForDealReassign
  include Listeners::DomainListeners
  include ActiveModel::Valida<PERSON>

  def self.listen
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_OWNER_UPDATED_EVENT, DEAL_OWNER_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{DEAL_OWNER_UPDATED_EVENT} tenant id #{payload.dig('deal', 'tenantId')} entity id #{payload['id']} new owner id #{payload.dig('deal', 'ownerId')}"

      LookUp.where(tenant_id: payload.dig('deal', 'tenantId'), entity_type: LOOKUP_DEAL, entity_id: payload['id']).update_all(owner_id: payload.dig('deal', 'ownerId'))
    end
  end
end
