# frozen_string_literal: true

class Listeners::ListenForDealUpdatedV2
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(DEAL_EXCHANGE, DEAL_UPDATED_V2_EVENT, DEAL_UPDATED_V2_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{DEAL_UPDATED_V2_EVENT} deal id #{payload.dig('entity', 'id')} tenant id #{payload.dig('metadata', 'tenantId')}"

      begin
        new(payload).process_contact_associations
      rescue => e
        Rails.logger.error "Error processing deal updated v2 event: #{e.message}"
      end
    end
  end

  def initialize(payload)
    @payload = payload
    @deal_id = payload.dig('entity', 'id')
    @deal_name = payload.dig('entity', 'name')
    @tenant_id = payload.dig('metadata', 'tenantId')
    @current_contacts = extract_contact_ids(payload.dig('entity', 'associatedContacts'))
    @previous_contacts = extract_contact_ids(payload.dig('oldEntity', 'associatedContacts'))
  end

  def process_contact_associations
    return if @deal_id.blank? || @tenant_id.blank?

    added_contacts = @current_contacts - @previous_contacts
    removed_contacts = @previous_contacts - @current_contacts

    Rails.logger.info "Deal #{@deal_id}: Added contacts #{added_contacts}, Removed contacts #{removed_contacts}"

    ActiveRecord::Base.transaction do
      add_contact_associations(added_contacts)
      remove_contact_associations(removed_contacts)
    end

    Rails.logger.info "Successfully updated contact deal associations for deal #{@deal_id}"
  end

  private

  def extract_contact_ids(associated_contacts)
    return [] if associated_contacts.blank?
    
    associated_contacts.map { |contact| contact['id'] }.compact
  end

  def add_contact_associations(contact_ids)
    return if contact_ids.blank?

    contact_ids.each do |contact_id|
      ContactDealAssociation.find_or_create_by(
        tenant_id: @tenant_id,
        contact_id: contact_id,
        deal_id: @deal_id
      ) do |association|
        association.deal_name = @deal_name
      end
    end

    Rails.logger.info "Added #{contact_ids.size} contact deal associations for deal #{@deal_id}"
  end

  def remove_contact_associations(contact_ids)
    return if contact_ids.blank?

    deleted_count = ContactDealAssociation.where(
      tenant_id: @tenant_id,
      contact_id: contact_ids,
      deal_id: @deal_id
    ).delete_all

    Rails.logger.info "Removed #{deleted_count} contact deal associations for deal #{@deal_id}"
  end
end
