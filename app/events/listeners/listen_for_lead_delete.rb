class Listeners::ListenForLeadDelete
  include Listeners::DomainListeners
  include ActiveModel::Valida<PERSON>

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, LEAD_DELETED_EVENT, LEAD_DELETED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload['id']} | tenantId - #{payload['tenantId']} | userId - #{payload['userId']} for #{LEAD_DELETED_EVENT}"
      data = {}
      data[:user_id] = payload["userId"]
      data[:tenant_id] = payload["tenantId"]
      data[:entity_id] = payload["id"]
      Rails.logger.info "Delete message after lead delete request from user: #{data[:user_id]} tenant id #{data[:tenant_id]} for lead: #{data[:entity_id]}"

      DeleteMessageForEntity.call(LOOKUP_LEAD, data, false)
      DeleteConversationsForEntity.call(LOOKUP_LEAD, data)
    end
  end
end
