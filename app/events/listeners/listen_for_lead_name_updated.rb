class Listeners::ListenForLeadNameUpdated
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, LEAD_NAME_UPDATED_EVENT, LEAD_NAME_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Message::Received message entityId - #{payload['leadId']} | tenantId - #{payload['tenantId']} for #{LEAD_NAME_UPDATED_EVENT}"

      name = "#{payload['firstName']} #{payload['lastName']}".strip
      LookUp.where(tenant_id: payload['tenantId'], entity_type: LOOKUP_LEAD, entity_id: payload['leadId']).update_all(name: name)
    end
  end
end
