# frozen_string_literal: true

class Listeners::ListenForLeadReassign
  include Listeners::DomainListeners
  include ActiveModel::Valida<PERSON>

  def self.listen
    RabbitmqConnection.subscribe(SALES_EXCHANGE, LEAD_OWNER_UPDATED_EVENT, LEAD_OWNER_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message for #{LEAD_OWNER_UPDATED_EVENT} tenant id #{payload['tenantId']} entity id #{payload['entityId']} new owner id #{payload['newOwnerId']}"

      LookUp.where(tenant_id: payload['tenantId'], entity_type: LOOKUP_LEAD, entity_id: payload['entityId']).update_all(owner_id: payload['newOwnerId'])
    end
  end
end
