# frozen_string_literal: true

class Listeners::ListenForShareRuleCreatedV2
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(CONFIG_EXCHANGE, SHARE_RULE_CREATED_V2_EVENT, SHARE_RULE_CREATED_V2_QUEUE) do |payload|
      payload = JSON.parse(payload)
      Rails.logger.info "Received message for share rule id #{payload.dig('metadata', 'entityId')} for #{SHARE_RULE_CREATED_V2_EVENT}"

      return unless [LOOKUP_LEAD, LOOKUP_CONTACT, LO<PERSON>UP_DEAL].include?(payload.dig('entity', 'entityType')&.downcase).present?

      unless payload.dig('entity', 'actions', 'sms')
        Rails.logger.info "Message permission is not there in the given share rule id #{payload.dig('metadata', 'entityId')}, Tenant id: #{payload.dig('metadata', 'tenantId')}"
        return
      end

      ShareRules.new(payload).manage_share_rules
    end
  end
end
