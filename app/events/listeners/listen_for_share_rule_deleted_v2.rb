# frozen_string_literal: true

class Listeners::ListenForShareRuleDeletedV2
  include ActiveRecord::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(CONFIG_EXCHANGE, SHARE_RULE_DELETED_V2_EVENT, SHARE_RULE_DELETED_V2_QUEUE) do |payload|
      payload = JSON.parse(payload)

      Rails.logger.info "Received message for share rule id #{payload.dig('metadata', 'entityId')} for #{SHARE_RULE_DELETED_V2_EVENT}"

      return unless [LOOKUP_LEAD, LOOKUP_CONTACT, LO<PERSON>UP_DEAL].include?(payload.dig('oldEntity', 'entityType')&.downcase).present?

      ShareRules.new(payload).delete
    end
  end
end
