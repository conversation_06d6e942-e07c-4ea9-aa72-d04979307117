# frozen_string_literal: true

class Listeners::ListenForShareRuleUpdatedV2
  include ActiveRecord::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_V2_EVENT, SHARE_RULE_UPDATED_V2_QUEUE) do |payload|
      payload = JSON.parse(payload)

      Rails.logger.info "Received message for share rule id #{payload.dig('metadata', 'entityId')} for #{SHARE_RULE_UPDATED_V2_EVENT}"

      return unless [LOOKUP_LEAD, LOOKUP_CONTACT, LO<PERSON>UP_DEAL].include?(payload.dig('entity', 'entityType')&.downcase).present?

      ShareRules.new(payload).manage_share_rules
    end
  end
end
