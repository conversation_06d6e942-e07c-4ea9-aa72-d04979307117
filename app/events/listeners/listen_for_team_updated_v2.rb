# frozen_string_literal: true

class Listeners::ListenForTeamUpdatedV2
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(USER_EXCHANGE, TEAM_UPDATED_V2_EVENT, TEAM_UPDATED_V2_QUEUE) do |payload|
      payload = JSON.parse(payload)
      Rails.logger.info "Received message for team id #{payload.dig('metadata', 'entityId')} for #{TEAM_UPDATED_V2_EVENT}"
      user_ids = payload.dig('entity', 'users').to_a.map { |user_hash| user_hash['id'].to_i }
      Team.where(tenant_id: payload.dig('metadata', 'tenantId'), id: payload.dig('entity', 'id')).update(user_ids: user_ids, name: payload.dig('entity', 'name'))
    end
  end
end
