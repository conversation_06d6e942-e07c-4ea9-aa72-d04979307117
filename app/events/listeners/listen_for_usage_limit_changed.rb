# frozen_string_literal: true

class Listeners::ListenForUsageLimitChanged
  include ActiveModel::Validations
  include Listeners::DomainListeners

  def self.listen
    RabbitmqConnection.subscribe(USER_EXCHANGE, USAGE_LIMIT_CHANGED_EVENT, USAGE_LIMIT_CHANGED_QUEUE) do |payload|
      Rails.logger.info "Received message for #{USAGE_LIMIT_CHANGED_QUEUE}"

      payload = JSON.parse(payload)
      waba_addon = payload['usageEntityLimits']&.find { |usage_entity| usage_entity['usageEntity'] == 'WHATSAPP_BUSINESS' }
      if DOWNGRADED_PLANS.include?(payload['planId']) || waba_addon.nil? || waba_addon['limit'].to_i.zero?
        ConnectedAccountService.new(payload).deactivate_all_accounts
      end

      whatsapp_credits = payload['usageEntityLimits']&.find { |usage_entity| usage_entity['usageEntity'] == 'WHATSAPP_CREDITS' }

      unless whatsapp_credits.nil? || !whatsapp_credits['isCharged']
        WhatsappCreditsService.new.add(payload['tenantId'], whatsapp_credits['limit'])
      end
    end
  end
end
