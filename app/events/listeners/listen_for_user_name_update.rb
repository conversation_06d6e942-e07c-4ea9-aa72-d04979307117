class Listeners::ListenForUserNameUpdate
  include Listeners::DomainListeners
  include ActiveModel::Valida<PERSON>

  def self.listen
    RabbitmqConnection.subscribe(USER_EXCHANGE, USER_NAME_UPDATED_EVENT, USER_NAME_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.debug "Received message userId - #{payload['userId']} | tenantId - #{payload['tenantId']} for #{USER_NAME_UPDATED_EVENT}"
      id = payload["userId"]
      tenant_id = payload["tenantId"]
      name = "#{payload['firstName']} #{payload['lastName']}".strip

      User.where(id: id, tenant_id:tenant_id).update_all(name: name)
    end
  end
end
