class Listeners::ListenForUserPhoneUpdate
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def initialize
  end

  def self.listen
    RabbitmqConnection.subscribe(USER_EXCHANGE, USER_PHONE_UPDATED_EVENT, USER_PHONE_UPDATED_QUEUE) do |payload|
      payload = JSON(payload)
      Rails.logger.info "Received message userId - #{payload['userId']} | tenantId - #{payload['tenantId']} for #{USER_PHONE_UPDATED_EVENT}"
      id = payload["userId"]
      tenant_id = payload["tenantId"]
      phone_numbers = payload["phoneNumbers"].presence || []
      Rails.logger.info "Phone numbers :: #{phone_numbers}"
      if phone_numbers.present?
        phone_number = phone_numbers.find{ |e| e['primary'] == true }
        value = "#{phone_number['dialCode']}#{phone_number['value']}" if phone_number
        Rails.logger.info "Phone number to update:: #{value}"
        User.where(id: id, tenant_id: tenant_id).update_all(phone_number: value)
      end
    end
  end
end
