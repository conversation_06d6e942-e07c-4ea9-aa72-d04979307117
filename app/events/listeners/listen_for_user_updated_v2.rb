# frozen_string_literal: true

class Listeners::ListenForUserUpdatedV2
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def self.listen
    RabbitmqConnection.subscribe(USER_EXCHANGE, USER_UPDATED_V2_EVENT, USER_UPDATED_V2_QUEUE) do |payload|
      payload = JSON.parse(payload)
      Rails.logger.info "Received message for user id #{payload.dig('metadata', 'entityId')} for #{USER_UPDATED_V2_EVENT}"

      user = User.find_by(id: payload.dig('metadata', 'entityId'))

      if user.present?
        if (payload.dig('entity', 'timezone') != payload.dig('oldEntity', 'timezone'))
          Rails.logger.info "Updating user timezone to #{payload.dig('entity', 'timezone')}, User id: #{user.id}"

          user.timezone = payload.dig('entity', 'timezone')
        end
        
        if payload.dig('entity', 'dateFormat') != payload.dig('oldEntity', 'dateFormat')
          Rails.logger.info "Updating user dateFormat to #{payload.dig('entity', 'dateFormat')}, User id: #{user.id}"

          user.date_format = payload.dig('entity', 'dateFormat')
        end

        user.save
      end
    end
  end
end
