# frozen_string_literal: true

class Listeners::ListenForWorkflowSendWhatsappMessage
  include Listeners::DomainListeners
  include ActiveModel::Validations

  def initialize
  end

  def self.listen
    RabbitmqConnection.subscribe(WORKFLOW_EXCHANGE, WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT, WORKFLOW_SEND_WHATSAPP_MESSAGE_QUEUE) do |payload, metadata|
      payload = JSON(payload)
      metadata_headers = metadata.with_indifferent_access[:headers]
      Rails.logger.info "Received message #{payload} for #{WORKFLOW_SEND_WHATSAPP_MESSAGE_EVENT}"

      entity_phones = payload.dig('entity', 'phoneNumbers')
      phone_numbers_to_send_message = []

      payload.dig('messageDetail', 'to').each do |recipient|
        if recipient['type'] == 'RECORD_PRIMARY_PHONE_NUMBER'
          phone_numbers_to_send_message << entity_phones.find { |phone_number| phone_number['primary'] }
        elsif recipient['type'] == 'RECORD_ALL_PHONE_NUMBERS'
          phone_numbers_to_send_message = entity_phones
        end
      end

      admin_token = GenerateToken.new(payload.dig('entity', 'ownerId', 'id'), payload.dig('metadata', 'tenantId'), admin_permissions).call
      thread = Thread.current
      thread[:token] = admin_token
      thread[:auth] = User::TokenParser.parse(admin_token)

      entity_data = payload.dig('entity')
      entity_data['metaData'] = { 'idNameStore' => entity_data.delete('idNameStore') }

      response = {
        eventId: payload.dig('metadata', 'eventId'),
        status: SUCCESS,
        statusCode: 200,
        executionDetails: {
          statuses: []
        }
      }

      phone_numbers_to_send_message.each do |phone_number|
        result = ApplicationService.new.handle_errors do
          thread[:user] = User::GetUserDetails.call(payload.dig('entity', 'ownerId', 'id'), payload.dig('metadata', 'tenantId'))
          params = {
            id: payload.dig('messageDetail', 'templateId'),
            entity_type: payload.dig('metadata', 'entityType').downcase,
            entity_id: payload.dig('entity', 'id'),
            phone_number: phone_number,
            entity_data: entity_data.with_indifferent_access,
            metadata: {
              executedWorkflows: payload.dig('metadata', 'executedWorkflows') || []
            }
          }
          WhatsappTemplateService.new(params).send_bulk_message
          {
            error_details: nil
          }
        end
        
        if(result[:error_details].present?)
          response[:status] = FAILED
          response[:statusCode] = Rack::Utils.status_code(result[:status])

          response[:executionDetails][:statuses] << {
            phone_number: phone_number,
            errorCode: result.dig(:error_details, :error_code),
            errorMessage: result.dig(:error_details, :message),
            status: FAILED
          }
        else
          response[:executionDetails][:statuses] << {
            phone_number: phone_number,
            errorCode: nil,
            errorMessage: nil,
            status: SUCCESS
          }
        end
      end
      
      Publishers::WorkflowExecutionStatusUpdatePublisher.call(response, { reply_to_exchange: metadata_headers[:replyToExchange], reply_to_event: metadata_headers[:replyToEvent] })
    end
  end

  def self.admin_permissions
    [
      {
        id: 1,
        name:  LOOKUP_LEAD,
        description: 'has access to lead',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 2,
        name: LOOKUP_CONTACT,
        description: 'has access to contact',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 3,
        name: 'sms',
        description: 'has access to sms',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          sms: true,
          write: true
        }
      },
      {
        id: 4,
        name: LOOKUP_USER,
        description: 'has access to user',
        limits: -1,
        units: 'count',
        action: {
          read: true
        }
      },
      {
        id: 4,
        name: 'whatsappTemplate',
        description: 'has access to whatsapp template',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          readAll: true
        }
      }
    ]
  end
end
