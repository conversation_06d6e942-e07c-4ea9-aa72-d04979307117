# frozen_string_literal: true

class Publishers::MessageCreated < ApplicationService
  def self.call(message_id)
    Rails.logger.info 'Publishers::MessageCreated called'
    event = Event::MessageCreated.new(create_payload(message_id))
    PublishEvent.call(event)
    Rails.logger.info 'Published payload for message.created event'
  end

  def self.create_payload(message_id)
    message = Message.find_by(id: message_id)
    entity_data = MessageSerializer::Details.serialize(message)

    if message.content.blank? && message.message_type == WHATSAPP_BUSINESS && message.attachments.count > 0
      file_ext = File.extname(message.attachments.first.file_name)

      file_type = ALLOWED_FILE_TYPES.find do |k, v|
        v.include?(file_ext.downcase)
      end

      entity_data['content'] = "(#{file_type&.first || 'media'})"
    end

    {
      'entity' => entity_data,
      'oldEntity' => nil,
      'metadata' => {
        'tenantId' => message.tenant_id,
        'userId' => message.owner_id,
        'entityId' => message.id,
        'entityType' => 'MESSAGE',
        'entityAction' => 'CREATED'
      }
    }
  end
end
