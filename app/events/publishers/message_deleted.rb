# frozen_string_literal: true

class Publishers::MessageDeleted < ApplicationService
  def self.call(tenant_id, user_id, deleted_serialized_message)
    Rails.logger.info 'Publishers::MessageDeleted called'
    event = Event::MessageDeleted.new(delete_payload(tenant_id, user_id, deleted_serialized_message))
    PublishEvent.call(event)
    Rails.logger.info 'Published payload for message.deleted event'
  end

  def self.delete_payload(tenant_id, user_id, deleted_serialized_message)
    {
      'entity' => nil,
      'oldEntity' => deleted_serialized_message,
      'metadata' => {
        'tenantId' => tenant_id,
        'userId' => user_id,
        'entityId' => deleted_serialized_message['id'],
        'entityType' => 'MESSAGE',
        'entityAction' => 'DELETED'
      }
    }
  end
end
