class Publishers::MessageReceivedFromEntityPublisher < ApplicationService
  def self.call(message)
    Rails.logger.info "Message Service: Publishers::MessageReceivedFromEntityPublisher called"
    data = MessageSerializer::Details.serialize(message).except("attachments")
    event = Event::MessageReceivedFromEntity.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::MessageReceivedFromEntity data #{event.to_json}"
  end
end
