# frozen_string_literal: true

class Publishers::SendLowWhatsappCreditsEmailPublisher < ApplicationService
  def self.call(whatsapp_credit)
    Rails.logger.info "Message Service: Publishers::SendLowWhatsappCreditsEmailPublisher called"
    data = WhatsappCreditsSerializer.new.serialize_low_whatsapp_credits_email(whatsapp_credit)

    event = Event::WhatsappCreditsAboutToExpire.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::SendLowWhatsappCreditsEmailPublisher data #{event.to_json}"
  end
end
