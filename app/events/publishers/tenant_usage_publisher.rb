class Publishers::TenantUsagePublisher
  def self.call(tenant_id = nil)
    Rails.logger.info "Message Service: Tenant Usage publisher called"
    data = Message.usage_per_tenant(tenant_id) + Attachment.usage_per_tenant(tenant_id) + TemplateMedia.usage_per_tenant(tenant_id)
    data += WhatsappCredit.usage_per_tenant(tenant_id) if tenant_id.present?
    event = Event::TenantUsage.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::TenantUsagePublisher data #{event.to_json}"
  end
end
