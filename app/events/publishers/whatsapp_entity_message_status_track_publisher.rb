# frozen_string_literal: true

class Publishers::WhatsappEntityMessageStatusTrackPublisher < ApplicationService
  def self.call(message)
    Rails.logger.info "Message Service: Publishers::WhatsappEntityMessageStatusTrackPublisher called"

    data = MessageSerializer::Details.serialize_whatsapp_entity_message_status_track_payload(message)

    event = Event::WhatsappEntityMessageStatusTrack.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::WhatsappEntityMessageStatusTrack data #{event.to_json}"
  end
end
