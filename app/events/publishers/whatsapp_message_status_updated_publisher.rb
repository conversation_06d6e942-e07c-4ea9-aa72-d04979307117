# frozen_string_literal: true

class Publishers::WhatsappMessageStatusUpdatedPublisher < ApplicationService
  def self.call(message)
    Rails.logger.info "Message Service: Publishers::WhatsappMessageStatusUpdatedPublisher called"

    conversation = Conversation.find(message.conversation_id)

    data = {
      id: message.id,
      status: message.status,
      conversationId: message.conversation_id,
      relatedTo: conversation.look_ups.present? ? [LookUpSerializer::Details.serialize(conversation.look_ups.first)] : [],
      conversationOwnerId: conversation.owner_id,
      tenantId: message.tenant_id
    }

    event = Event::WhatsappMessageStatusUpdated.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::WhatsappMessageStatusUpdated data #{event.to_json}"
  end
end
