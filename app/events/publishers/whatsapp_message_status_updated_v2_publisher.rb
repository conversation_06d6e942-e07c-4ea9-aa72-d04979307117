# frozen_string_literal: true

class Publishers::WhatsappMessageStatusUpdatedV2Publisher < ApplicationService
  def self.call(message)
    Rails.logger.info "Message Service: Publishers::WhatsappMessageStatusUpdatedV2Publisher called"
  
    data = MessageSerializer::Details.serialize_whatsapp_message_status_update_v2_payload(message)

    event = Event::WhatsappMessageStatusUpdatedV2.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::WhatsappMessageStatusUpdatedV2 data #{event.to_json}"
  end
end
