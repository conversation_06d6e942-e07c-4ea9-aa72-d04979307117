# frozen_string_literal: true

class Publishers::WhatsappTemplateNameUpdatedPublisher < ApplicationService
  def self.call(whatsapp_template)
    Rails.logger.info "Message Service: Publishers::WhatsappTemplateNameUpdatedPublisher called"

    data = {
      id: whatsapp_template.id,
      name: whatsapp_template.name,
      tenantId: whatsapp_template.tenant_id
    }

    event = Event::WhatsappTemplateNameUpdated.new(data)
    PublishEvent.call(event)
    Rails.logger.info "Event::WhatsappMessageStatusUpdated data #{event.to_json}"
  end
end
