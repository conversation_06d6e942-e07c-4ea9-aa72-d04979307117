# frozen_string_literal: true

class Publishers::WorkflowExecutionStatusUpdatePublisher < ApplicationService
  def self.call(data, publishing_details)
    Rails.logger.info "Message Service: Publishers::WorkflowExecutionStatusUpdatePublisher called"

    event = Event::WorkflowExecutionStatusUpdate.new(data, publishing_details[:reply_to_event])
    PublishEvent.call(event, publishing_details[:reply_to_exchange], 'workflow')
    Rails.logger.info "Event::WorkflowExecutionStatusUpdate eventId #{data[:eventId]}"
  end
end
