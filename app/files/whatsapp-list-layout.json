{"leftNav": false, "pageConfig": {"actionConfig": {"search": true, "filter": true, "create": true}, "tableConfig": {"recordClickAction": "VIEW", "columns": [{"id": "status", "header": "Status", "fieldType": "PICK_LIST", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": true, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": {"id": 1, "displayName": "Status Type Picklist", "picklistValues": [{"id": 1, "displayName": "<PERSON><PERSON>", "name": "sent"}, {"id": 2, "displayName": "Read", "name": "read"}, {"id": 3, "displayName": "Failed", "name": "failed"}, {"id": 4, "displayName": "Delivered", "name": "delivered"}, {"id": 5, "displayName": "Received", "name": "received"}]}, "primaryField": null}, {"id": "sent<PERSON><PERSON><PERSON><PERSON>unt", "header": "Sent from Account", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": {"entity": "MESSAGE", "lookupUrl": "/messages/connected-accounts/lookup?q="}, "picklist": null, "primaryField": null}, {"id": "messageBody", "header": "Message Body", "fieldType": "TEXT_FIELD", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "direction", "header": "Direction", "fieldType": "PICK_LIST", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": true, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": {"id": 1, "displayName": "Direction Picklist", "picklistValues": [{"id": 1, "displayName": "Outgoing", "name": "outgoing", "systemDefault": true}, {"id": 2, "displayName": "Incoming", "name": "incomig", "systemDefault": true}]}, "primaryField": null}, {"id": "sentAt", "header": "<PERSON><PERSON>", "fieldType": "DATETIME_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "readAt", "header": "Read At", "fieldType": "DATETIME_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "receivedAt", "header": "Received At", "fieldType": "DATETIME_PICKER", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": false, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": null, "primaryField": null}, {"id": "templateType", "header": "Template Type", "fieldType": "PICK_LIST", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": true, "isRequired": true, "active": true, "showDefaultOptions": true, "lookup": null, "picklist": {"id": 1, "displayName": "Template Type Picklist", "picklistValues": [{"id": 1, "displayName": "Text", "name": "text"}, {"id": 2, "displayName": "Media", "name": "media"}, {"id": 3, "displayName": "None", "name": "none"}]}, "primaryField": null}, {"id": "template", "header": "Template", "fieldType": "LOOK_UP", "isStandard": true, "isSortable": false, "isFilterable": true, "isInternal": false, "isRequired": true, "multiValue": false, "isColumnSelector": false, "active": true, "showDefaultOptions": true, "lookup": {"entity": "MESSAGE", "lookupUrl": "/messages/whatsapp-templates/lookup?q="}, "picklist": null, "primaryField": null}]}}, "defaultConfig": {"fields": []}}