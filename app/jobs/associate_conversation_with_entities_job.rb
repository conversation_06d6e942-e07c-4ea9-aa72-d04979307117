# frozen_string_literal: true

class AssociateConversationWithEntitiesJob < ApplicationJob
  queue_as :default

  MAX_PAGE_SIZE = 1000

  def perform(conversation_id, entity_type, phone)
    total_pages = 1
    current_page = 0

    conversation = Conversation.find(conversation_id)

    while current_page < total_pages
      query_params = "page=#{current_page}&size=#{MAX_PAGE_SIZE}&sort=createdAt,asc"

      permissions = BuildPermissionsForToken.call(lead_contact_read_all_permissions)

      thread = Thread.current
      user = User.find(conversation.owner_id)
      
      thread[:user] = user
      thread[:token] = GenerateToken.new(user.id, user.tenant_id, permissions, true).call
      
      entities_response = EntityService.new({ entity_type: entity_type, phone_number: phone[:value], query_params: query_params, fields: %w[id phoneNumbers] }).get_by_json_rule

      Rails.logger.info("entity_response for conversation_id: #{conversation_id} entity_type: #{entity_type} #{entities_response}")

      entities_response['content'].each { |entity_details| associate_conversation_with_entity(conversation_id, entity_type, phone, entity_details) }

      if(total_pages == 1)
        total_pages = entities_response['totalPages']
      end

      current_page += 1
    end
  end

  def associate_conversation_with_entity(conversation_id, entity_type, phone, entity_details)
    look_up = GetLookUpWithOwner.call({ id: entity_details['id'], entity: entity_type, phone_number: "#{phone[:dialCode]}#{phone[:value]}", name: "#{entity_details['firstName']} #{entity_details['lastName']}".strip, owner_id: entity_details['ownerId'] })
    look_up.conversation_look_ups.find_or_create_by(conversation_id: conversation_id)
    Rails.logger.info "Associated conversation with entity conversation_id: #{conversation_id} entity_id: #{entity_details['id']} entity_type: #{entity_type}"
  end

  def lead_contact_read_all_permissions
    [
      {
        id: 1,
        name: 'lead',
        displayName: nil,
        description: nil,
        actions: { read: true, readAll: true }
      }.with_indifferent_access,
      {
        id: 2,
        name: 'deal',
        displayName: nil,
        description: nil,
        actions: {}
      }.with_indifferent_access,
      {
        id: 3,
        name: 'contact',
        displayName: nil,
        description: nil,
        actions: { read: true, readAll: true }
      }.with_indifferent_access
    ]
  end
end
