# frozen_string_literal: true

class CopyTemplateMediaToMessageJob < ApplicationJob
  queue_as :default

  def perform(message_id, template_media_id)
    message = Message.find_by(id: message_id)
    return unless message.present?

    template_media = TemplateMedia.find_by(id: template_media_id)
    return unless template_media.present?

    begin
      original_file_name = template_media.extract_file_name

      return if original_file_name.blank? || message.attachments.blank?

      file_name = original_file_name.split('.').first
      file_ext = original_file_name.split('.').last

      name = "#{message.id}_#{file_name}_#{DateTime.now.to_i}.#{file_ext}"
      destination_key = "tenant_#{message.tenant_id}/user_#{message.owner_id}/attachments/#{name}"

      S3::CopyAttachmentOnS3.new(template_media.file_name, destination_key).call
      file_size = template_media.file_size
      Rails.logger.info "CopyTemplateMediaToMessageJob | Copy template media to message attachment completed, Time: #{Time.now}, Message id: #{message.id}, Template media id: #{template_media.id}, Destination key: #{destination_key}, File size: #{file_size}"
      attachment = message.attachments.first
      if attachment.present?
        attachment.update!(
          file_name: destination_key,
          size: file_size
        )
      end
    rescue StandardError => e
      Rails.logger.error "CopyTemplateMediaToMessageJob | Error copying template media to message attachment: #{e.message}"
    end
  end
end
