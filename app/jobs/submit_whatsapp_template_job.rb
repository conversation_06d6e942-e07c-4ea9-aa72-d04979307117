# frozen_string_literal: true

class SubmitWhatsappTemplateJob < ApplicationJob
  queue_as :default

  def perform(whatsapp_template_id)
    whatsapp_template = WhatsappTemplate.find_by(id: whatsapp_template_id)

    unless whatsapp_template.present?
      Rails.logger.error "Invalid Whatsapp Template Id - #{whatsapp_template_id} in SubmitWhatsappTemplateJob"
      return
    end

    unless whatsapp_template.status == SUBMITTING
      Rails.logger.error "Cannot submit template with status - #{whatsapp_template.status} for template Id - #{whatsapp_template.id}"
      return
    end

    header_component = whatsapp_template.components.find_by(component_type: HEADER)
    if header_component.present? && header_component.component_format != TEXT
      TemplateMediaService.new({ template_media: header_component.template_media, connected_account: whatsapp_template.connected_account }).upload_media_to_facebook
    end

    begin
      Facebook::WhatsappTemplate.new(whatsapp_template.connected_account).save(whatsapp_template)
    rescue ExceptionHandler::ThirdPartyAPIError => e
      whatsapp_template.status = REJECTED      
      whatsapp_template.reason = e.message.split('||')&.second
    end
    whatsapp_template.save!
  end
end
