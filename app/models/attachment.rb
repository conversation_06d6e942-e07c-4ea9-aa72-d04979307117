class Attachment < ApplicationRecord
  belongs_to :message


  def extract_file_name
    regex = Regexp.new(/[^>]*\/[^>]*\/[0-9]+_([^>]*)_[^>]*\.([^>]*)/)
    file_name.scan(regex).flatten.join(".") rescue file_name
  end

  def self.usage_per_tenant(tenant_id = nil)
    data = []

    attachment_data = joins(:message).group(:tenant_id).select("tenant_id, sum(size) as count")
    attachment_data = attachment_data.where("messages.tenant_id = ?", tenant_id) if tenant_id.present?
    attachment_data = attachment_data.as_json

    attachment_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'].to_i, usageEntity: 'STORAGE_MESSAGE_ATTACHMENT'}
    end
    data << { tenantId: tenant_id, count: 0, usageEntity: 'STORAGE_MESSAGE_ATTACHMENT'} if (!attachment_data.present? && tenant_id.present?)
    data
  end
end
