class Auth::PermissionAction
  include ActiveModel::Model

  attr_accessor(
    :read,
    :write,
    :update,
    :delete,
    :delete_all,
    :email,
    :call,
    :sms,
    :task,
    :note,
    :read_all,
    :update_all
  )

  def initialize options = {}
    UnderscorizeKeys.do(options)
    valid_options = permit(options)
    super(valid_options)
  end

  validates :read, :write, :update, :delete, :delete_all, :email, :call, :sms, :task, :note, :read_all, :update_all, inclusion: { in: [ true, false ] }

  private
  def permit options
    options = ActionController::Parameters.new(options)
    options.permit(
      :read,
      :write,
      :update,
      :delete,
      :delete_all,
      :email,
      :call,
      :sms,
      :task,
      :note,
      :read_all,
      :update_all
    ).to_hash
  end
end
