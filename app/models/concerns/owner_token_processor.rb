module OwnerTokenProcessor
  def set_owner_token
    owner_id = @params[:owner_id]
    profile_details = User::GetUserProfile.call(owner_id)
    if owner_id.present?
      @owner_profile_permissions = profile_details['permissions'].map do |permission|
        permission['actions'] = permission.delete('action')
        permission
      end
    else
      get_owner_permissions(profile_details['profileId'])
    end

    @owner_user = User::GetUserDetails.call(owner_id, @tenant_id)
    permissions = BuildPermissionsForToken.call(@owner_profile_permissions)
    @owner_token = GenerateToken.call(owner_id, @tenant_id, permissions)
  rescue => e
    Rails.logger.info "Couldn't set owner id on message #{e.message}"
    @owner_user = @owner_token = @owner_profile_permissions = @owner_id = nil
  end

  def get_owner_permissions(profile_id)
    data = User::GetPermissionsForProfile.call(profile_id)

    @owner_profile_permissions = data['permission']
  end
end
