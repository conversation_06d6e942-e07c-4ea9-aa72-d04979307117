module RelatedEntityProcessor
  private

  def get_related_to(validate_lookup = true)
    related_to = []
    entities = @params[:related_to].reject{ |en| en[:_destroy] == true }
    return [] unless entities.present?

    related_to = entities.map do |entity|
      entity&.merge!(tenant_id: @tenant_id) unless entity.has_key?(:tenant_id)
      look_up = GetLookUp.call(entity)
      look_up = validate_look_up(look_up) if validate_lookup
      look_up.name = entity[:name] if entity[:name]
      look_up.owner_id = entity[:owner_id] if entity[:owner_id]
      look_up.save!
      look_up
    end
    related_to
  end

  def validate_look_up(look_up)
    case look_up.entity_type
    when LOOKUP_LEAD
      look_up = validate_lead(look_up)
    when LOOKUP_DEAL
      look_up = validate_deal(look_up)
    when LOOKUP_CONTACT
      look_up = validate_contact(look_up)
    else
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
    look_up
  end

  def validate_lead(look_up)
    begin
      look_up = ValidateLead.call(look_up, @owner_token)
    rescue => e
      Rails.logger.info "Exception: #{e.message}"
      look_up = ValidateLead.call(look_up)
      @message.owner = User::GetUserDetails.call(@current_user.id, @tenant_id)
      @message.save!
    end
    look_up
  end

  # TODO: We are unable to store deal owner id on deal lookups as we currently have no way to get owner in existing api responses
  def validate_deal(look_up)
    contact = get_related_contact_from_params
    contact = @message.related_to.where("entity_type = ?", LOOKUP_CONTACT).first unless contact
    contact = GetLookUp.call(contact&.merge(tenant_id: @tenant_id)) if contact_received_in_parameters?(contact)

    begin
      deal, contact = ValidateDealWithContact.call(look_up, contact, @owner_token)
    rescue => e
      Rails.logger.info "Exception: #{e.message}"
      deal, contact = ValidateDealWithContact.call(look_up, contact)
      @message.owner = User::GetUserDetails.call(@current_user.id, @tenant_id)
      @message.save!
    end
    deal
  end

  def contact_received_in_parameters?(contact)
    contact&.kind_of?(Hash)
  end

  def get_related_contact_from_params
    @params[:related_to]&.find{|a| a[:entity] == LOOKUP_CONTACT }
  end

  def validate_contact(look_up)
    begin
      look_up = ValidateContact.call(look_up, @owner_token)
    rescue => e
      Rails.logger.info "Exception: #{e.message}"
      look_up = ValidateContact.call(look_up)
      @message.owner = User::GetUserDetails.call(@current_user.id, @tenant_id)
      @message.save!
    end
    look_up
  end

  def has_message_permission_on_profile
    entity_type = get_related_entity_type_from_params
    return unless entity_type

    permission = @owner_profile_permissions.find{ |p| p['name'] == entity_type }
    return permission['actions']['read'] && permission['actions']['sms']
  end

  def get_related_entity_type_from_params
    return unless @params[:related_to].present?
    return @params[:related_to].first[:entity]
  end

  def has_message_permission_on_related_entity_type
    entity_type = get_related_entity_type_from_params
    return unless entity_type

    auth_data = Thread.current[:auth]
    permission = auth_data.permissions.find{ |p| p.name == entity_type }
    permission&.action&.sms
  end

  def has_sms_write_permission_on_profile?
    auth_data = Thread.current[:auth]
    permission = auth_data.permissions.find{ |p| p.name == SMS_ACTION }
    permission&.action&.write
  end
end
