# frozen_string_literal: true

module UserAccess

  def can_read_all_templates?
    auth_data.can_access?('whatsappTemplate', 'read_all')
  end

  def can_read_templates?
    auth_data.can_access?('whatsappTemplate', 'read')
  end

  def can_read_template?(template)
    (
      can_read_all_templates? ||
      (
        can_read_templates? &&
          (
            self.template_creator?(template) ||
            self.connected_account_agent?(template.connected_account_id)
          )
      )
    )
  end

  def can_create_templates?
    auth_data.can_access?('whatsappTemplate', 'write')
  end

  def can_update_all_templates?
    auth_data.can_access?('whatsappTemplate', 'update_all')
  end

  def can_update_templates?
    auth_data.can_access?('whatsappTemplate', 'update')
  end

  def can_update_template?(template)
    (
      can_update_all_templates? ||
      (
        can_update_templates? && self.template_creator?(template)
      )
    )
  end

  def template_creator?(template)
    template.created_by == self
  end

  def connected_account_agent?(connected_account_id)
    AgentUser.where(tenant_id: self.tenant_id, user_id: self.id, connected_account_id: connected_account_id).exists?
  end

  def can_send_message?(entity_type)
    auth_data.can_access?(entity_type, 'sms')
  end

  def can_read_conversation?(entity_type)
    auth_data.can_access?(entity_type, 'read') && auth_data.can_access?(entity_type, 'sms')
  end

  def can_create_conversation?
    auth_data.can_access?('sms', 'write')
  end

  def can_read_all_conversation?
    auth_data.can_access?('sms', 'read_all')
  end

  def can_read_all_entity?(entity_type)
    auth_data.can_access?(entity_type, 'read_all')
  end

  def auth_data
    @auth_data ||= Thread.current[:auth]
  end

  def shared_entity_can_read_conversation?(entity_id, entity_type, entity_owner_id)
    share_rule_scope = ShareRule.where(tenant_id: self.tenant_id, to_id: self.id, to_type: 'USER')

    user_team_ids = Team.where(tenant_id: self.tenant_id).where("#{self.id} = ANY (user_ids)").pluck(:id)

    if user_team_ids.present?
      share_rule_scope = share_rule_scope.or(
        ShareRule.where(tenant_id: self.tenant_id, to_id: user_team_ids, to_type: 'TEAM')
      )
    end

    share_rule_scope = share_rule_scope.where(entity_id: entity_id, entity_type: entity_type).or(share_rule_scope.where(entity_type: entity_type, share_all_records: true, from_id: entity_owner_id))

    share_rule_scope.present? ? true : false
  end

  def can_user_read_conversation?(entity_id, entity_type, entity_owner_id, conversation_owners)
    (
      (self.can_read_all_conversation?) ||
      (entity_owner_id == self.id && self.can_read_conversation?(entity_type)) ||
      (conversation_owners.include?(self.id)) ||
      (shared_entity_can_read_conversation?(entity_id, entity_type.upcase, entity_owner_id) && self.can_read_conversation?(entity_type))
    )
  end

  def can_send_conversation_message?(entity_id, entity_type, entity_owner_id)
    (
      self.can_read_conversation?(entity_type) &&
      self.can_create_conversation? &&
      (
        (self.can_read_all_conversation? && self.can_read_all_entity?(entity_type)) ||
        ((entity_owner_id == self.id) || shared_entity_can_read_conversation?(entity_id, entity_type.upcase, entity_owner_id))
      )
    )
  end

  def get_shared_entity_records(entity_types)
    share_rule_scope = ShareRule.where(tenant_id: self.tenant_id, to_id: self.id, to_type: 'USER').where(entity_type: entity_types)

    user_team_ids = Team.where(tenant_id: self.tenant_id).where("#{self.id} = ANY (user_ids)").pluck(:id)

    if user_team_ids.present?
      share_rule_scope = share_rule_scope.or(
        ShareRule.where(tenant_id: self.tenant_id, to_id: user_team_ids, to_type: 'TEAM')
      )
    end

    entity_ids = share_rule_scope.where(share_all_records: false)
    .pluck(:entity_id, :entity_type)
    .group_by { |entity_id, entity_type| entity_type }
    .transform_values { |values| values.map(&:first) }

    entity_owner_ids = share_rule_scope.where(share_all_records: true)
    .pluck(:from_id, :entity_type)
    .group_by { |from_id, entity_type| entity_type }
    .transform_values { |values| values.map(&:first) }

    result = {}

    entity_types.each do |entity_type|
      result[entity_type] = {
        entity_ids: (entity_ids.dig(entity_type) || []),
        entity_owner_ids: (entity_owner_ids.dig(entity_type) || [])
      }
    end
    result
  end

  def can_user_read_conversation_unrelated_to_entity?(conversation_owner_id)
    (
      self.can_read_all_conversation? ||
      (conversation_owner_id == self.id)
    )
  end

  def can_send_conversation_message_unrelated_to_entity?(conversation_owner_id)
    (
      self.can_user_read_conversation_unrelated_to_entity?(conversation_owner_id) &&
      self.can_create_conversation?
    )
  end
end
