# frozen_string_literal: true

class ConnectedAccount < ApplicationRecord
  has_many :lead_agent_users, -> { where entity_type: <PERSON>OOKUP_LEAD }, class_name: '<PERSON><PERSON><PERSON>'
  has_many :lead_agents, through: :lead_agent_users, source: :user

  has_many :contact_agent_users, -> { where entity_type: LOOKUP_CONTACT }, class_name: 'AgentUser'
  has_many :contact_agents, through: :contact_agent_users, source: :user

  has_one :lead_field_mapping, -> { where(entity_type: LO<PERSON>UP_LEAD) }, class_name: 'FieldMapping'
  has_one :contact_field_mapping, -> { where(entity_type: LOOKUP_CONTACT) }, class_name: 'FieldMapping'

  belongs_to :created_by, class_name: 'User'
  belongs_to :updated_by, class_name: 'User'

  validates :phone_number_id, presence: true, uniqueness: true
  validates :entities_to_create, inclusion: { in: ['lead', 'contact', []], message: '%{value} is not included in the list.' }
  validates :status, inclusion: { in: [DRAFT, PENDING, ACTIVE, INACTIVE], message: '%{value} is invalid' }
  validates :interakt_onboarding_status, inclusion: { in: [DRAFT_STATUS, REQUEST_SENT, WABA_ONBOARDED, WABA_ONBOARDING_FAILED], message: '%{value} is invalid' }

  def self.encrypt(access_token)
    cipher = OpenSSL::Cipher::AES.new(128, :CBC)
    cipher.encrypt
    cipher.padding = 1
    cipher.key = ENV['MESSAGE_CREDENTIAL_ENCRYPTION_SECRET']
    cipher.iv = ENV['MESSAGE_CREDENTIAL_ENCRYPTION_IV']
    Base64.encode64(cipher.update(access_token) + cipher.final)
  end

  def self.decrypt(encrypted_token)
    decipher = OpenSSL::Cipher::AES.new(128, :CBC)
    decipher.decrypt
    decipher.padding = 1
    decipher.key = ENV['MESSAGE_CREDENTIAL_ENCRYPTION_SECRET']
    decipher.iv = ENV['MESSAGE_CREDENTIAL_ENCRYPTION_IV']
    decipher.update(Base64.decode64(encrypted_token)) + decipher.final
  end
end
