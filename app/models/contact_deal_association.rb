# frozen_string_literal: true

class ContactDealAssociation < ApplicationRecord
  validates :contact_id, presence: true
  validates :deal_id, presence: true
  validates :tenant_id, presence: true
  validates :deal_name, length: { maximum: 255 }

  validates :contact_id, uniqueness: { scope: [:deal_id, :tenant_id] }

  scope :by_tenant, ->(tenant_id) { where(tenant_id: tenant_id) }
  scope :by_contact_ids, ->(contact_ids) { where(contact_id: contact_ids) }

  scope :for_tenant_contacts, ->(tenant_id, contact_ids) {
    by_tenant(tenant_id).by_contact_ids(contact_ids)
  }
end