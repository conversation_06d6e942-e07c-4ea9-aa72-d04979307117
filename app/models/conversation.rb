class Conversation < ApplicationRecord
  include SoftDeletable

  has_many :conversation_look_ups, dependent: :destroy
  has_many :look_ups, through: :conversation_look_ups
  has_many :sub_conversations, dependent: :destroy

  validates :phone_number, uniqueness: { scope: %i[tenant_id connected_account_id], conditions: -> { where(deleted_at: nil) } }
  # We are adding phone number value with country code
end
