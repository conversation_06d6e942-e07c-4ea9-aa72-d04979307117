class Event::WhatsappTemplateStatusUpdated
  include ActiveModel::Model

  def initialize(whatsapp_template)
    @whatsapp_template = whatsapp_template
  end

  def routing_key
    WHATSAPP_TEMPLATE_STATUS_UPDATED
  end

  def to_json
    {
      id: @whatsapp_template.id,
      name: @whatsapp_template.name,
      status: @whatsapp_template.status,
      connectedAccount: @whatsapp_template.connected_account.display_name,
      reason: @whatsapp_template.reason,
      additionalInfo: @whatsapp_template.additional_info,
      userId: @whatsapp_template.created_by_id,
      tenantId: @whatsapp_template.tenant_id
    }.to_json
  end
end
