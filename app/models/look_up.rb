# frozen_string_literal: true

class LookUp < ApplicationRecord
  has_many :message_look_ups, dependent: :destroy
  has_many :messages, through: :message_look_ups

  has_many :conversation_look_ups, dependent: :destroy
  has_many :conversations, through: :conversation_look_ups

  validates :entity_type, inclusion: { in: [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT, LOOKUP_USER] }

  def entity
    "#{entity_type}_#{entity_id}"
  end
end
