class Message < ApplicationRecord
  include SoftDeletable
  
  belongs_to :owner, class_name: 'User', foreign_key: 'owner_id'
  belongs_to :connected_account, optional: true

  has_many :recipient_look_ups, -> { where recipient: true }, class_name: 'MessageLookUp', dependent: :destroy
  has_many :related_look_ups, -> { where related: true }, class_name: 'MessageLookUp', dependent: :destroy

  has_many :recipients, through: :recipient_look_ups, source: :look_up
  has_many :related_to, through: :related_look_ups, source: :look_up

  has_many :attachments, dependent: :destroy

  enum direction: { incoming: 0, outgoing: 1 }
  enum status: { sent: 0, delivered: 1, read: 2, failed: 3, sending: 4, received: 5 }
  validates :message_type, inclusion: { in: [SMS, WHATSAPP, CHAT, WHATSAPP_BUSINESS] }, allow_blank: true

  validates :connected_account_id, presence: true, if: Proc.new { message_type == WHATSAPP_BUSINESS }

  before_create :save_content_as_plain_text, if: Proc.new { message_type == WHATSAPP_BUSINESS }

  #after_destroy :publish_tenant_usage_event

  def publish_tenant_usage_event
    Publishers::TenantUsagePublisher.call(tenant_id)
  end

  def self.usage_per_tenant(tenant_id = nil)
    message_data = group(:tenant_id).select("tenant_id, count(*)")
    message_data = message_data.where(tenant_id: tenant_id) if tenant_id
    message_data = message_data.as_json
    data = []
    message_data.each do |d|
      data << { tenantId: d['tenant_id'], count: d['count'], usageEntity: 'MESSAGE'}
    end
    data << { tenantId: tenant_id, count: 0, usageEntity: 'MESSAGE'} if (!message_data.present? && tenant_id.present?)
    data
  end

  def save_content_as_plain_text
    self.plain_text_content = Message::ParseWhatsappMessageToPlainText.new(content).call if content.present?
  end
end
