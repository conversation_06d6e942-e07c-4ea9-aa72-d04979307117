class TemplateMedia < ApplicationRecord
  belongs_to :whatsapp_template_component, optional: true

  validates :file_name, presence: true
  validates :file_size, presence: true
  validates :tenant_id, presence: true
  validates :file_type, inclusion: { in: TEMPLATE_MEDIA_FILE_TYPES }

  def media_url(send_content_type = false)
    content_type = send_content_type ? file_type : nil
    S3::GetPresignedUrl.new(file_name, file_name.split('/', 3)[-1], S3_ATTACHMENT_BUCKET, content_type).call
  end

  def self.usage_per_tenant(tenant_id = nil)
    data = []
    template_media_data = group(:tenant_id).select("tenant_id, sum(file_size) as count")
    template_media_data = template_media_data.where(tenant_id: tenant_id) if tenant_id.present?

    template_media_data.each do |media_data|
      data << { tenantId: media_data['tenant_id'].to_i, count: media_data['count'].to_i, usageEntity: 'STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT' }
    end

    data
  end

  def extract_file_name
    regex = Regexp.new(/[^>]*\/[^>]*\/([^>]+)_[^>]*\.([^>]*)/)
    file_name.scan(regex).flatten.join(".") rescue file_name
  end
end
