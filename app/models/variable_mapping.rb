class VariableMapping < ApplicationRecord
  validates :tenant_id, presence: true
  validates :component_type, inclusion: [HEADER, BODY, BUTTON_URL, BUTTON_COPY_CODE]
  validates :parent_entity, inclusion: [<PERSON><PERSON><PERSON><PERSON>_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL]
  validates_inclusion_of :entity,
    in: Proc.new { |mapping| [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].include?(mapping.parent_entity) ? "#{mapping.parent_entity.upcase}_VARIABLE_ENTITIES".constantize : [] }, on: :update
  validates :field_type, inclusion: VARIABLE_FIELD_TYPES, on: :update

  belongs_to :whatsapp_template
end
