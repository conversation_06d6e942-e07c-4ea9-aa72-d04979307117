# frozen_string_literal: true

class WhatsappCredit < ApplicationRecord
  validates :tenant_id, presence: true
  validates :consumed, numericality: { greater_than_or_equal_to: 0 }
  validates :parked, numericality: { greater_than_or_equal_to: 0 }

  def self.usage_per_tenant(tenant_id)
    whatsapp_credit = WhatsappCredit.find_by(tenant_id: tenant_id)
    data = []

    if whatsapp_credit.present?
      data <<
        {
          tenantId: tenant_id,
          count: whatsapp_credit.consumed,
          usageEntity: 'WHATSAPP_CREDITS'
        }
    end

    data
  end

  def has_whatsapp_credits?
    return (total.to_f - consumed.to_f - parked) > 0
  end

  def self.has_whatsapp_credits?(tenant_id)
    tenant_whatsapp_credit = WhatsappCredit.find_by(tenant_id: tenant_id)

    return false unless tenant_whatsapp_credit.present?

    tenant_whatsapp_credit.has_whatsapp_credits?
  end

  def has_whatsapp_credits_for_bulk?
    balance = total - consumed - parked
    return balance > MIN_BALANCE_FOR_BULK
  end

  def self.has_whatsapp_credits_for_bulk?(tenant_id)
    tenant_whatsapp_credit = WhatsappCredit.find_by(tenant_id: tenant_id)
    return false unless tenant_whatsapp_credit.present?

   tenant_whatsapp_credit.has_whatsapp_credits_for_bulk?
  end
end
