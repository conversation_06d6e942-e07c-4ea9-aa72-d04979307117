class WhatsappTemplateComponent < ApplicationRecord
  validates :tenant_id, presence: true
  validates :component_type, inclusion: [HEADER, BODY, FOOTER, BUTTON]
  validates :component_text, presence: true, unless: :is_header_component_media?
  validates_inclusion_of :component_format,
    in: Proc.new { |comp| [HEADER, BUTTON].include?(comp.component_type) ? "#{comp.component_type}_COMPONENT_FORMATS".constantize : [TEXT] }
    validates_inclusion_of :media_type,
    in: ['STATIC', 'DYNAMIC'],
    if: Proc.new { |comp| comp.component_type == HEADER && [IMAGE, VIDEO, DOCUMENT].include?(comp.component_format) }
  validate :component_specific_validations
  validate :template_media_presence, if: :is_header_component_media?

  belongs_to :whatsapp_template
  has_one :template_media
  after_create :attach_media, if: :is_header_component_media?
  after_update :attach_media, if: :is_header_component_media?

  before_save :generate_component_content

  before_destroy :detach_media, if: :is_header_component_media?

  def is_header_component_media?
    component_type == HEADER && [IMAGE, VIDEO, DOCUMENT].include?(component_format)
  end

  private

  def generate_component_content
    case component_type
    when HEADER
      self.content = { "header_text" => ['a'] } if component_text&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).present?
    when BODY
      body_variables_count = component_text.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).map { |var_text| var_text.tr('{}', '').to_i }.uniq.count
      self.content = { "body_text" => [body_variables_count.times.map { 'a' }] } if body_variables_count.positive?
    when BUTTON
      if component_format == URL && component_value.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).present?
        self.content = [component_value.gsub('{{1}}', 'a').gsub('{{2}}', 'a')]
      end
    end
  end

  def component_specific_validations
    [HEADER, BODY, FOOTER, BUTTON].include?(component_type) ? send("validate_#{component_type.downcase}_component") : nil
  end

  def validate_header_component
    if component_text&.size.to_i > 60
      errors.add(:base, I18n.t('error.invalid_template.header_length_exceeded'))
    else
      header_variables = component_text&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX)
      if header_variables.present? && (header_variables.count > 1 || header_variables.first.tr('{}', '') != '1')
        errors.add(:base, I18n.t('error.invalid_template.header_variable_invalid'))
      end
    end
  end

  def validate_body_component
    if component_text.present? && component_text.gsub(WHATSAPP_TEMPLATE_VARIABLE_REGEX, 'a').size > 1024
      errors.add(:base, I18n.t('error.invalid_template.body_length_exceeded'))
    end
  end

  def validate_footer_component
    if component_text&.size.to_i > 60
      errors.add(:base, I18n.t('error.invalid_template.footer_length_exceeded'))
    elsif component_text&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).present?
      errors.add(:base, I18n.t('error.invalid_template.footer_variable_not_allowed'))
    end
  end

  def validate_button_component
    case component_format
    when PHONE_NUMBER
      if component_text&.size.to_i > 25 || component_value&.size.to_i > 20
        errors.add(:base, I18n.t('error.invalid_template.incorrect_phone_button_length'))
      elsif component_value.present? && (!component_value.starts_with?('+') || !Phonelib.parse(component_value).valid?)
        errors.add(:base, I18n.t('error.invalid_template.invalid_phone_number'))
      end
    when  URL
      if component_text&.size.to_i > 25 || component_value&.size.to_i > 2000
        errors.add(:base, I18n.t('error.invalid_template.incorrect_url_button_length'))
      elsif !(component_value =~ URI::regexp)
        errors.add(:base, I18n.t('error.invalid_template.invalid_url_text'))
      else
        url_variables = component_value&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX)
        if url_variables.present? && (url_variables.count > 1 || !(component_value&.ends_with?('{{1}}') || component_value&.ends_with?('{{2}}')))
          errors.add(:base, I18n.t('error.invalid_template.invalid_url_variable'))
        end
      end
    when COPY_CODE
      if component_text&.size.to_i > 15
        errors.add(:base, I18n.t('error.invalid_template.incorrect_copy_code_button_length'))
      elsif component_text&.scan(/[^a-zA-Z0-9]/).present?
        errors.add(:base, I18n.t('error.invalid_template.copy_code_invalid_text'))
      end
    when  QUICK_REPLY
      if component_text&.size.to_i > 25
        errors.add(:base, I18n.t('error.invalid_template.incorrect_quick_reply_button_length'))
      elsif component_text&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).present?
        errors.add(:base, I18n.t('error.invalid_template.quick_reply_variable_not_allowed'))
      end
    end
  end

  def template_media_presence
    unless TemplateMedia.where(tenant_id: tenant_id, id: component_value).exists?
      errors.add(:template_media, I18n.t('error.invalid_template.media_must_be_present'))
    end
  end

  def attach_media
    TemplateMedia.where(tenant_id: tenant_id, id: component_value).update(whatsapp_template_component_id: id)
  end

  def detach_media
    TemplateMedia.where(tenant_id: tenant_id, whatsapp_template_component_id: id).update(whatsapp_template_component_id: nil)
  end
end
