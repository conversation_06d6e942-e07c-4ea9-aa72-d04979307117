# frozen_string_literal: true

class ConnectedAccountSerializer
  def serialize(connected_account)
    JSON(
      Jbuilder.new do |ca|
        ca.call(connected_account, :id, :name, :display_name, :waba_number, :entities_to_create, :status, :is_verified)
        ca.createdBy UserSerializer::Details.serialize(connected_account.created_by)
        ca.updatedBy UserSerializer::Details.serialize(connected_account.updated_by)
      end
      .target!
    )
  end

  def lookup(connected_account)
    JSON(
      Jbuilder.new do |ca|
        ca.id connected_account.id
        ca.name connected_account.display_name
        ca.businessName connected_account.name
      end.target!
    )
  end
end
