# frozen_string_literal: true

class ConnectedAccountsSerializer
  def serialize(connected_accounts)
    json = Jbuilder.new
    json.body do
      json.content(connected_accounts) do |connected_account|
        json.call(connected_account, :id, :waba_number, :display_name, :name, :entities_to_create, :status, :is_verified)
        json.onboardingStatus connected_account.interakt_onboarding_status
        json.createdBy UserSerializer::Details.serialize(connected_account.created_by)
        json.updatedBy UserSerializer::Details.serialize(connected_account.updated_by)
      end
    end
  end

  def lookup(connected_accounts)
    json = Jbuilder.new
    json.body do
      json.content(connected_accounts) do |connected_account|
        json.id connected_account.id
        json.name connected_account.display_name
        json.businessName connected_account.name
      end
    end
  end
end
