# frozen_string_literal: true

module ConversationSerializer
  class ByEntity
    extend MessageSerializer::Helper

    def self.serialize(data)
      conversation = data[:conversation]
      entity_data = data[:entity_data]
      conversation_lookups = conversation.look_ups.where(entity_id: entity_data[:id], entity_type: entity_data[:entity_type])
      related_entities = conversation_lookups.map do |related_entity|
        LookUpSerializer::Details.serialize(related_entity)
      end

      masking_enabled_entity = ConversationSerializer::SearchResult.should_mask_phone_number?(related_entities)
      last_message_received_at = conversation.last_message_received_at

      {
        conversationId: conversation.id,
        entityId: entity_data[:id],
        entityName: entity_data[:entity_name],
        entityType: entity_data[:entity_type],
        connectedAccountId: conversation.connected_account_id,
        relatedTo: related_entities,
        phoneNumber: {
          value: masking_enabled_entity ? mask_number(conversation.phone_number) : conversation.phone_number,
          session: last_message_received_at.present? && last_message_received_at > 24.hours.ago ? ACTIVE : INACTIVE
        }
      }
    end
  end
end
