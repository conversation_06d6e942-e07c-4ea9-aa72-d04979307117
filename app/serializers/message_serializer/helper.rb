# frozen_string_literal: true

module MessageSerializer::Helper
  def mask_details(message)
    if message.direction == 'incoming'
      message.sender_number = mask_number(message.sender_number)
    else
      message.recipient_number = mask_number(message.recipient_number)
    end

    message
  end

  def mask_number(phone_number, with_country_code: true)
    parsed_phone = Phonelib.parse(phone_number)
    with_country_code ?  "+#{parsed_phone.country_code}#{'*' * 4}#{phone_number[-3, 3]}" :  "#{'*' * 4}#{phone_number[-3, 3]}"
  end
end
