# frozen_string_literal: true

module MessageSerializer
  class SearchResult
    extend MessageSerializer::Helper

    def self.serialize(messages, related_entities_for_masking = [], related_to = nil, entity_permissions = nil)
      return nil unless messages

      masking_enabled_entity = false
      related_entities_for_masking.each do |entity|
        if [<PERSON>OOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].include?(entity)
          entity = LOOKUP_CONTACT if entity == LOOKUP_DEAL

          masked_fields = GetMaskedFields.new(entity).call
          phone_field_masked = masked_fields.select { |field| field['name'] == 'phoneNumbers' }
          if phone_field_masked.present?
            masking_enabled_entity = true
          end
        end
      end

      json = Jbuilder.new
      json.body do
        json.content(messages) do |message|
          related_entities = related_to.present? ? related_to : message.related_to
          message = mask_details(message) if masking_enabled_entity
          json.(message, :id, :content, :medium, :direction, :sent_at, :recipient_number, :sender_number, :message_type, :status_message)
          json.status message.status&.capitalize
          json.owner UserSerializer::Details.serialize(message.owner)
          json.relatedTo related_entities.collect { |r| LookUpSerializer::Details.serialize(r) }
          json.recipients message.recipients.collect { |r| LookUpSerializer::Details.serialize(r) }
          json.attachments message.attachments.collect { |a| AttachmentSerializer::Details.serialize(a) }
        end

        json.page do
          json.no messages.current_page.to_i
          json.size messages.per_page
        end

        json.entityPermissions entity_permissions
        json.totalElements messages.total_entries
        json.totalPages messages.total_pages
        json.first messages.previous_page.nil?
        json.last messages.next_page.nil?
      end
    end
  end
end
