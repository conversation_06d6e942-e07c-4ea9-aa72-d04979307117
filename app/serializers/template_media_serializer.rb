# frozen_string_literal: true

class TemplateMediaSerializer
  def initialize(template_media)
    @template_media = template_media
  end

  def serialize
    JSON(
      Jbuilder.new do |template_media|
        template_media.call(@template_media, :whatsapp_file_handle, :id, :file_size, :file_type)
        template_media.fileName @template_media.extract_file_name
      end.target!
    )
  end

  def serialize_get
    JSON(
      Jbuilder.new do |template_media|
        template_media.call(@template_media, :whatsapp_file_handle, :id, :file_size, :file_type, :media_url)
        template_media.fileName @template_media.extract_file_name
      end.target!
    )
  end
end
