class VariableMappingsSerializer
  def initialize(variable_mappings)
    @variable_mappings = variable_mappings
  end

  def serialize
    json = Jbuilder.new
    json.body do
      json.variableMappings(@variable_mappings) do |variable_mapping|
        json.call(
          variable_mapping,
          :id,
          :component_type,
          :template_variable,
          :entity,
          :internal_name,
          :fallback_value,
          :field_type
        )
      end
    end
  end
end
