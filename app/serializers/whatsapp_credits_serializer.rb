# frozen_string_literal: true

class WhatsappCreditsSerializer
  def serialize_summary(whatsapp_credit)
    JSON(
      Jbuilder.new do |summary|
        summary.used whatsapp_credit.consumed.to_f.round(2)
        summary.total whatsapp_credit.total.to_f.round(2)
        summary.parked whatsapp_credit.parked.to_f.round(2)
        summary.credits_revised_at whatsapp_credit.credits_revised_at.present? ? Time.at(whatsapp_credit.credits_revised_at).strftime("%FT%T.%LZ") : nil
      end
      .target!
    )
  end

  def serialize_status(credits_status)
    JSON(
      Jbuilder.new do |json|
        json.availableForBulkMessages credits_status
      end
      .target!
    )
  end

  def serialize_history(params)
    whatsapp_credit_histories, usage_per_conversation_category = params

    auth_data = Thread.current[:auth]

    connected_accounts = ConnectedAccount.select(:id, :display_name).where(tenant_id: auth_data.tenant_id).index_by(&:id)

    json = Jbuilder.new
    json.body do
      json.content(whatsapp_credit_histories) do |whatsapp_credit_history|
        json.call(whatsapp_credit_history, :balance, :entry_type)
        json.value [CREDITS_ADDED, CREDITS_UNPARKED].include?(whatsapp_credit_history.entry_type) ? whatsapp_credit_history.value : -whatsapp_credit_history.value
        json.category whatsapp_credit_history.conversation_category
        json.start_time Time.at(whatsapp_credit_history.start_time).strftime("%FT%T.%LZ")
        if(whatsapp_credit_history.connected_account_id)
          json.connectedAccount do
            json.id whatsapp_credit_history.connected_account_id
            json.name connected_accounts[whatsapp_credit_history.connected_account_id].display_name
          end
        end
      end

      json.page do
        json.no whatsapp_credit_histories.current_page.to_i
        json.size whatsapp_credit_histories.per_page
      end

      json.templateUsage do
        json.key_format! ->(key) { key.upcase }
        json.MARKETING usage_per_conversation_category[MARKETING].to_f.round(2) || 0
        json.UTILITY usage_per_conversation_category[UTILITY].to_f.round(2) || 0
        json.SERVICE usage_per_conversation_category[SERVICE].to_f.round(2) || 0
      end

      json.totalElements whatsapp_credit_histories.total_entries
      json.totalPages whatsapp_credit_histories.total_pages
      json.first whatsapp_credit_histories.previous_page.nil?
      json.last whatsapp_credit_histories.next_page.nil?
    end
  end

  def serialize_low_whatsapp_credits_email(whatsapp_credit)
    connected_account_created_by_users = ConnectedAccount.select(:created_by_id).where(tenant_id: whatsapp_credit.tenant_id).distinct.map do |acccount|
      UserSerializer::Details.serialize(acccount.created_by)
    end

    remainingCredits = (whatsapp_credit.total - whatsapp_credit.consumed - whatsapp_credit.parked).round(2)
    usedCreditsPercentage = whatsapp_credit.total == 0 ? 100 : (((whatsapp_credit.consumed + whatsapp_credit.parked) / whatsapp_credit.total) * 100).round(0)

    JSON(
      Jbuilder.new do |payload|
        payload.tenantId whatsapp_credit.tenant_id
        payload.users connected_account_created_by_users
        payload.entityGroup "Whatsapp Credits"
        payload.remainingCredits remainingCredits
        payload.usedCreditsPercentage usedCreditsPercentage
        payload.parkedCredits whatsapp_credit.parked.round(2)
      end
      .target!
    )
  end
end
