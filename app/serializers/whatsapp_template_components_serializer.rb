class WhatsappTemplateComponentsSerializer
  def serialize(components)
    json = Jbuilder.new
    json.array!(components) do |component|
      json.call(component, :id, :position)
      json.type component.component_type
      json.format component.component_format
      json.text component.component_text
      json.value component.component_value
      json.mediaType component.media_type if component.component_type == HEADER && [IMAGE, VIDEO, DOCUMENT].include?(component.component_format)
    end
  end
end
