class WhatsappTemplatesSerializer
  def serialize(whatsapp_templates)
    json = Jbuilder.new
    json.body do
      json.content(whatsapp_templates) do |whatsapp_template|
        json.call(whatsapp_template, :id, :name, :entity_type, :category, :language, :status, :created_at, :updated_at)
        json.connectedAccount ConnectedAccountSerializer.new.lookup(whatsapp_template.connected_account)
        json.createdBy UserSerializer::Details.serialize(whatsapp_template.created_by)
        json.updatedBy UserSerializer::Details.serialize(whatsapp_template.updated_by)
        json.recordActions record_actions_for(whatsapp_template)
      end

      json.page do
        json.no whatsapp_templates.current_page.to_i
        json.size whatsapp_templates.per_page
      end
      json.totalElements whatsapp_templates.total_entries
      json.totalPages whatsapp_templates.total_pages
      json.first whatsapp_templates.previous_page.nil?
      json.last whatsapp_templates.next_page.nil?
    end
  end

  def serialize_single(whatsapp_template)
    JSON(
      Jbuilder.new do |wt|
        wt.call(whatsapp_template, :id, :name, :entity_type, :category, :language, :status, :created_at, :updated_at)
        wt.components WhatsappTemplateComponentsSerializer.new.serialize(whatsapp_template.components)
        wt.connectedAccount ConnectedAccountSerializer.new.lookup(whatsapp_template.connected_account)
        wt.createdBy UserSerializer::Details.serialize(whatsapp_template.created_by)
        wt.updatedBy UserSerializer::Details.serialize(whatsapp_template.updated_by)
        wt.recordActions record_actions_for(whatsapp_template)
      end
      .target!
    )
  end

  def serialize_lookup(whatsapp_templates)
    json = Jbuilder.new
    json.body do
      json.content(whatsapp_templates) do |whatsapp_template|
        json.id whatsapp_template.id
        json.name whatsapp_template.name
      end
    end
  end

  private

  def record_actions_for(whatsapp_template)
    {
      read: true,
      update: Thread.current[:user].can_update_template?(whatsapp_template)
    }
  end
end
