# frozen_string_literal: true

class AgentsService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
    @token = Thread.current[:token]
  end

  def get_all
    unless @auth_data.can_access?('whatsappBusiness', 'read_all')
      Rails.logger.error "User doesn't have permission to view connected account agents"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    unless [LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_entity_type')}")
    end

    connected_account = ConnectedAccount.where(tenant_id: @auth_data.tenant_id, id: @params[:connected_account_id]).first
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}")
    end

    connected_account.send("#{@params[:entity_type]}_agents")
  end

  def save
    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.error "User doesn't have permission to update connected account agents"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    unless [LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_entity_type')}")
    end

    connected_account = ConnectedAccount.where(tenant_id: @auth_data.tenant_id, id: @params[:connected_account_id]).first
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}")
    end

    summary_response = User::Summary.new(@params[:agents].map { |agent_hash| agent_hash[:id] }, @token).get
    user_agents = summary_response.map { |user_hash| User::GetUserDetails.call(user_hash[:id], @current_user.tenant_id, false, user_hash) }

    begin
      connected_account.send("#{@params[:entity_type]}_agents=", user_agents)
      AgentUser.where(connected_account_id: connected_account.id, entity_type: @params[:entity_type]).update_all(tenant_id: @auth_data.tenant_id)
      connected_account.update(updated_by: @current_user)
    rescue StandardError => e
      Rails.logger.error "AgentsService Error while saving agents #{e.message}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end

    connected_account.reload.send("#{@params[:entity_type]}_agents")
  end
end
