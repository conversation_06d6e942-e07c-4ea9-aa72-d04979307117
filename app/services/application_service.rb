class ApplicationService
  def self.call(*args, &block)
    new(*args, &block).call
  end

  def handle_errors
    yield

  rescue ExceptionHandler::AuthenticationError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.unauthorized,
        message: message
      },
      status: :unauthorized
    }

  rescue ExceptionHandler::InternalServerError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.internal_error,
        message: message
      },
      status: :internal_server_error
    }

  rescue ExceptionHandler::InvalidDataError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid_data,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::NotFound => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.not_found,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::MessageNotAllowedError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.message_not_allowed,
        message: message
      },
      status: :forbidden
    }

  rescue ExceptionHandler::DeleteNotAllowedError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.DeleteNotAllowedError,
        message: message
      },
      status: :forbidden
    }

  rescue ExceptionHandler::AttachmentUrlDownloadError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.attachment_url_download_failed,
        message: message
      },
      status: :forbidden
    }

  rescue ActiveRecord::RecordNotFound => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.not_found,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::InvalidLeadError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid_lead,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::RecipientNotPresentError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.recipient_not_present,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::RemoveRecipientNotAllowedError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.remove_recipient_not_allowed,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::InvalidProfileError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid_profile,
        message: message
      },
      status: :not_found
    }

  rescue ExceptionHandler::InvalidLeadPhoneError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.invalid_lead_phone,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::PhoneNumbersNotMatchedError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code || ErrorCode.phone_numbers_not_match,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::ThirdPartyAPIError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::AccountNotConnectedError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::InsufficientWhatsappCreditsBalance => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code,
        message: message
      },
      status: :unprocessable_entity
    }

  rescue ExceptionHandler::ConversationNotFoundError => e
    error_code, message = e.message&.split('||')
    
    {
      error_details: {
        error_code: error_code,
        message: message
      },
      status: :unprocessable_entity
    }
  rescue ExceptionHandler::InactiveWhatsappTemplate => e
    error_code, message = e.message&.split('||')

    {
      error_details: {
        error_code: error_code,
        message: message
      },
      status: :unprocessable_entity
    }
  rescue StandardError => e    
    {
      error_details: {
        error_code: nil,
        message: e.message
      },
      status: :internal_server_error
    }
  end
end
