require 'open-uri'

class Attachment::CreateService

  def initialize(message, files)
    @message = message
    @files = files
  end

  def call
    return unless @message && @files

    @files.each do |file|
      @file = file
      file_name = @file['file_name'].split('.').first
      file_ext = @file['file_name'].split('.').last
      name = "#{@message.id}_#{file_name}_#{DateTime.now.to_i}.#{file_ext}"
      file_name = "tenant_#{@message.tenant_id}/user_#{@message.owner_id}/attachments/#{name}"

      if @file[:url].present?
        @file[:data] = get_data_from_url name
        file_size = open(@file[:data]).size
      else
        file_size = @file['data'].try(:size)
      end

      if @file['data'].present?
        file_path = @file['path'] || @file['data'].path
        Rails.logger.info "File path: #{file_path}"
        Rails.logger.info "File name: #{file_name}"
        S3::UploadFile.new(file_path, file_name, S3_ATTACHMENT_BUCKET, @file['skip_delete']).call
      end

      @message.attachments << Attachment.create(file_name: file_name, size: file_size.to_i)
    end
  end

  def get_data_from_url name
    begin
      open("#{Rails.root}/tmp/#{name}", 'wb') do |file|
        file << URI.open(@file[:url]).read
        return file
      end
    rescue Exception => e
      Rails.logger.info "Exception while saving file from #{@file[:url]} for #{@message.tenant_id} | #{e.to_s}"
      raise(ExceptionHandler::AttachmentUrlDownloadError, ErrorCode.attachment_url_download_failed)
    end
  end
end
