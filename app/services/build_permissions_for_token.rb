class BuildPermissionsForToken < ApplicationService
  def initialize(permissions)
    @permissions = permissions
  end

  def call
    permissions = []
    %w(lead deal contact).each do |m|
      p_data = {}
      permission = @permissions.find{ |p| p['name'] == m }
      actions = permission['actions']
      p_data[:name] = permission['name']
      p_data['description'] = "has access to #{m} resource"
      p_data['limits'] = -1
      p_data['units'] = 'count'
      p_data['action'] = {}
      p_data['action']['read'] = actions['read'] if [true, false].include?(actions['read'])
      p_data['action']['write'] = actions['write'] if [true, false].include?(actions['write'])
      p_data['action']['update'] = actions['update'] if [true, false].include?(actions['update'])
      p_data['action']['delete'] = actions['delete'] if [true, false].include?(actions['delete'])
      p_data['action']['email'] = actions['email'] if [true, false].include?(actions['email'])
      p_data['action']['call'] = actions['call'] if [true, false].include?(actions['call'])
      p_data['action']['sms'] = actions['sms'] if [true, false].include?(actions['sms'])
      p_data['action']['task'] = actions['task'] if [true, false].include?(actions['task'])
      p_data['action']['note'] = actions['note'] if [true, false].include?(actions['note'])
      p_data['action']['meeting'] = actions['meeting'] if [true, false].include?(actions['meeting'])
      p_data['action']['readAll'] = actions['readAll'] if [true, false].include?(actions['readAll'])
      p_data['action']['updateAll'] = actions['updateAll'] if [true, false].include?(actions['updateAll'])
      p_data['action']['deleteAll'] = actions['deleteAll'] if [true, false].include?(actions['deleteAll'])
      permissions << p_data
    end
    permissions
  end
end