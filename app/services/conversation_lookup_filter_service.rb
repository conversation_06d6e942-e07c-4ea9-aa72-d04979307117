class ConversationLookupFilterService
  def initialize(current_user)
    @current_user = current_user
    @tenant_id = current_user.tenant_id
  end

  def filter_all_lookups(conversation_ids)
    if conversation_ids.blank?
      return LookUp.none
    end

    can_read_lead = @current_user.can_read_all_entity?(LOOKUP_LEAD) || @current_user.can_read_conversation?(LOOKUP_LEAD)
    can_read_contact = @current_user.can_read_all_entity?(LOOKUP_CONTACT) || @current_user.can_read_conversation?(LOOKUP_CONTACT)
    can_sms_lead = @current_user.can_send_message?(LOOKUP_LEAD)
    can_sms_contact = @current_user.can_send_message?(LOOKUP_CONTACT)
    
    unless (can_read_lead && can_sms_lead) || (can_read_contact && can_sms_contact)
      return LookUp.none
    end

    if @current_user.can_read_all_entity?(LOOKUP_LEAD) && @current_user.can_read_all_entity?(LOOKUP_CONTACT) && 
       can_sms_lead && can_sms_contact && @current_user.can_read_all_conversation?
      return LookUp.joins(:conversation_look_ups)
        .where(conversation_look_ups: { conversation_id: conversation_ids })
        .select('look_ups.*, conversation_look_ups.conversation_id')
        .distinct
    end

    shared_entities = @current_user.get_shared_entity_records(['LEAD', 'CONTACT'])

    conditions = []
    conditions << "look_ups.owner_id = #{@current_user.id}"
    
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      unless @current_user.can_send_message?(entity_type)
        next
      end

      if @current_user.can_read_all_entity?(entity_type) && @current_user.can_read_all_conversation?
        conditions << "look_ups.entity_type = '#{entity_type}'"
      else
        if shared_entities[entity_type.upcase][:entity_ids].present?
          conditions << "(look_ups.entity_type = '#{entity_type}' AND look_ups.entity_id IN (#{shared_entities[entity_type.upcase][:entity_ids].join(',')}))"
        end
        
        if shared_entities[entity_type.upcase][:entity_owner_ids].present?
          conditions << "(look_ups.entity_type = '#{entity_type}' AND look_ups.owner_id IN (#{shared_entities[entity_type.upcase][:entity_owner_ids].join(',')}))"
        end
      end
    end

    LookUp.joins(:conversation_look_ups)
      .where(conversation_look_ups: { conversation_id: conversation_ids })
      .where(conditions.join(' OR '))
      .select('look_ups.*, conversation_look_ups.conversation_id')
      .distinct
  end
end 