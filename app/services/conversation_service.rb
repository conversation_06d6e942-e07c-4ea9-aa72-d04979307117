# frozen_string_literal: true

class ConversationService
  def initialize(params)
    @current_user = Thread.current[:user]
    @auth_data = Thread.current[:auth]
    @params = params
  end

  def fetch_messages_and_related_to
    phone_number_conversation = Conversation.find_by(id: @params[:id], tenant_id: @auth_data.tenant_id)
    
    unless phone_number_conversation.present?
      Rails.logger.info "Conversation not found for the given id #{@params[:id]}}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
    end

    entity_id, entity_type = get_filter_rule_value('related_to')
    is_entity_accessible = false
    is_phone_number_present = false

    if phone_number_conversation.conversation_look_ups.present?
      if entity_id.present? && entity_type.present?
        begin
          entity_data = EntityService.new({ entity_id: entity_id, entity_type: entity_type }).get_by_id
          is_entity_accessible = true

          if entity_data.present?
            is_phone_number_present = entity_data['phoneNumbers']&.any? do |phone|
              "#{phone['dialCode']}#{phone['value']}" == phone_number_conversation.phone_number
            end
          end

          unless @current_user.can_user_read_conversation?(entity_id, entity_type, entity_data['ownerId'], [phone_number_conversation.owner_id])
            Rails.logger.info "User does not have conversation permission on #{entity_type}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
            raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
          end
        rescue ExceptionHandler::InvalidDataError, ExceptionHandler::NotFound
          check_basic_conversation_permissions(phone_number_conversation)
        end
      else
        is_phone_number_present = true
        check_basic_conversation_permissions(phone_number_conversation)
      end
    else
      is_phone_number_present = true
      check_basic_conversation_permissions(phone_number_conversation)
    end

    messages = Message.where(conversation_id: phone_number_conversation.id, tenant_id: @auth_data.tenant_id)
    related_to = phone_number_conversation.look_ups

    messages =
      messages.order(created_at: :desc).paginate(
        page: @params[:page] || 1,
        per_page: @params[:size] || 10
      )

    [messages, related_to, { isEntityAccessible: is_entity_accessible, isPhoneNumberPresent: is_phone_number_present || false }]
  end

  def associate_conversations_by_given_phone_numbers_with_entity
    phone_numbers = @params[:phone_numbers]

    if @params[:extract_phone_numbers]
      new_phone_numbers = @params[:new_phone_numbers] || []
      old_phone_numbers = @params[:old_phone_numbers] || []
      phone_numbers = new_phone_numbers - old_phone_numbers
    end

    return unless phone_numbers.present?

    phone_numbers.each do |phone_number|
      conversations = Conversation.where(tenant_id: @params[:tenant_id], phone_number: "#{phone_number['dialCode']}#{phone_number['value']}")

      conversations.each do |conversation|
        look_up = GetLookUpWithOwner.call({ tenant_id: @params[:tenant_id], id: @params[:entity_id], entity: @params[:entity_type], phone_number: "#{phone_number['dialCode']}#{phone_number['value']}", name: @params[:entity_name], owner_id: @params[:owner_id] })
        look_up.conversation_look_ups.find_or_create_by(conversation_id: conversation.id)
        Rails.logger.info "Associated conversation on #{@params[:entity_type]} with conversation_id: #{conversation.id} entity_id: #{@params[:entity_id]}"
      end
    end
  end

  def fetch_conversations
    unless @auth_data.can_access?('sms', 'read')
      Rails.logger.error "User doesn't have permission to read messages"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    FilterConversationsQuery.new(@auth_data, @params).call
  end

  def delete_conversation
    unless @params[:skip_permission_check] || @auth_data.can_access?('sms', 'delete_all')
      Rails.logger.info "User does not have permission to delete conversations, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed)
    end

    tenant_id = @params[:tenant_id] || @auth_data.tenant_id
    user_id = @params[:user_id] || @current_user.id

    conversation = Conversation.find_by(id: @params[:id], tenant_id: tenant_id)
    
    unless conversation.present?
      Rails.logger.info "Conversation not found for id: #{@params[:id]}, Tenant id: #{tenant_id}"
      raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
    end

    conversation.soft_delete!
    
    Message.where(conversation_id: conversation.id).find_each do |message|
      serialized_message_data = MessageSerializer::Details.serialize(message, fetch_deleted_by(user_id, tenant_id))
      Publishers::MessageDeleted.call(tenant_id, user_id, serialized_message_data)
    end

    Rails.logger.info "Conversation id: #{conversation.id} is soft deleted by tenant_id #{tenant_id}, user_id #{user_id}"
  end

  def cleanup_soft_deleted_conversations
    Rails.logger.info "Starting cleanup of soft-deleted conversations"
  
    Conversation.soft_deleted.find_each do |conversation|
      begin
        tenant_id = conversation.tenant_id
        user_id = conversation.owner_id
  
        Message.where(conversation_id: conversation.id).find_each do |message|
          Message::DeleteMessageService.new(message.id, false, { user_id: user_id, tenant_id: tenant_id }, true).call
        end
  
        conversation.destroy!
        Rails.logger.info "Hard deleted conversation id: #{conversation.id}, tenant_id: #{tenant_id}"
      rescue => e
        Rails.logger.error "Error hard deleting conversation id: #{conversation.id}: #{e.message}"
      end
    end
  
  
    Rails.logger.info "Completed cleanup of soft-deleted conversations"
  end
  
  def fetch_conversation_permissions
    conversation = Conversation.find_by(id: @params[:id], tenant_id: @auth_data.tenant_id)
    
    unless conversation.present?
      Rails.logger.info "Conversation not found for the given id #{@params[:conversation_id]}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
    end
    
    entity_type = @params[:entity_type]
    entity_id = @params[:entity_id]

    unless entity_type.present? && entity_id.present?
      Rails.logger.info "Entity details not provided, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(
        ExceptionHandler::InvalidDataError,
        "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Entity details are required')}"
      )
    end
    
    is_entity_accessible = false
    is_phone_number_present = false
    
    begin
      entity_data = EntityService.new({ entity_id: entity_id, entity_type: entity_type }).get_by_id
      is_entity_accessible = true
      
      if entity_data.present?
        is_phone_number_present = entity_data['phoneNumbers']&.any? do |phone|
          "#{phone['dialCode']}#{phone['value']}" == conversation.phone_number
        end
      end
    rescue ExceptionHandler::InvalidDataError, ExceptionHandler::NotFound
    end

    { isEntityAccessible: is_entity_accessible, isPhoneNumberPresent: is_phone_number_present || false }
  end

  def create_or_fetch_conversation
    connected_account_id = @params[:connected_account_id]
    entity_id = @params[:entity_id]
    entity_type = @params[:entity_type]
    last_conversation_id = @params[:last_conversation_id]
    tenant_id = @auth_data.tenant_id
    current_user = @current_user
    phone_number = nil
    entity_data = nil

    unless last_conversation_id.present?
      Rails.logger.info "last_conversation_id param is required. user_id: #{current_user.id}, tenant_id: #{tenant_id}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'last_conversation_id is required')}")
    end
    last_conversation = Conversation.find_by(id: last_conversation_id, tenant_id: tenant_id)
    
    unless last_conversation.present?
      Rails.logger.info "Conversation not found for last_conversation_id: #{last_conversation_id}, user_id: #{current_user.id}, tenant_id: #{tenant_id}"
      raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
    end

    connected_account_exists = ConnectedAccount.where(
      id: connected_account_id,
      tenant_id: tenant_id,
      status: ACTIVE,
      is_verified: true
    ).exists?
    unless connected_account_exists
      Rails.logger.info "Connected account not found or inactive/invalid. connected_account_id: #{connected_account_id}, user_id: #{current_user.id}, tenant_id: #{tenant_id}"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected account not found or inactive/invalid')}")
    end

    phone_number = last_conversation.phone_number
    
    conversation = Conversation.find_by(tenant_id: tenant_id, connected_account_id: connected_account_id, phone_number: phone_number)
    if conversation.present?
      return conversation_session_response(conversation)
    end

    if entity_id.present? && entity_type.present?
      entity_data = EntityService.new({ entity_id: entity_id, entity_type: entity_type }).get_by_id
      phone_found = entity_data['phoneNumbers']&.any? { |p| "#{p['dialCode']}#{p['value']}" == phone_number }
      
      unless phone_found
        Rails.logger.info "Message not allowed: phone number #{phone_number} not found on entity_id: #{entity_id}, entity_type: #{entity_type}, user_id: #{current_user.id}, tenant_id: #{tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, "#{ErrorCode.message_not_allowed}||#{I18n.t('error.message_not_allowed')}")
      end
      unless current_user.can_send_conversation_message?(entity_id, entity_type, entity_data['ownerId'])
        Rails.logger.info "User does not have conversation create permission, User id: #{current_user.id}, Tenant id: #{tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, "#{ErrorCode.message_not_allowed}||#{I18n.t('error.message_not_allowed')}")
      end
      conversation = create_and_associate_conversation(tenant_id, connected_account_id, phone_number, current_user.id, entity_id, entity_type, entity_data)
    else
      unless current_user.can_create_conversation?
        Rails.logger.info "Message not allowed: permission denied for unrelated entity. user_id: #{current_user.id}, tenant_id: #{tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, "#{ErrorCode.message_not_allowed}||#{I18n.t('error.message_not_allowed')}")
      end
      conversation = create_and_associate_conversation(tenant_id, connected_account_id, phone_number, current_user.id)
    end
    conversation_session_response(conversation)
  end

  def create_and_associate_conversation(tenant_id, connected_account_id, phone_number, owner_id, entity_id = nil, entity_type = nil, entity_data = nil)
    conversation = Conversation.create!(
      tenant_id: tenant_id,
      connected_account_id: connected_account_id,
      phone_number: phone_number,
      owner_id: owner_id
    )
    SubConversation.create!(
      tenant_id: tenant_id,
      connected_account_id: connected_account_id,
      conversation_id: conversation.id
    )
    if entity_id.present? && entity_type.present? && entity_data.present?
      look_up = GetLookUpWithOwner.call({ id: entity_id, entity: entity_type, phone_number: phone_number, name: "#{entity_data['firstName']} #{entity_data['lastName']}", owner_id: entity_data['ownerId'] })
      look_up.conversations << conversation
    end
    parsed_phone = Phonelib.parse(phone_number)
    dial_code = "+#{parsed_phone.country_code}"
    value = parsed_phone.raw_national
    AssociateConversationWithEntitiesJob.perform_later(conversation.id, LOOKUP_LEAD, { dialCode: dial_code, value: value })
    AssociateConversationWithEntitiesJob.perform_later(conversation.id, LOOKUP_CONTACT, { dialCode: dial_code, value: value })
    conversation
  end

  def get_conversation_by_entity
    entity_id = @params[:entity_id]
    entity_type = @params[:entity_type]
    entity_name = @params[:entity_name]
    phone_id = @params[:phone_id]
    tenant_id = @auth_data.tenant_id

    entity_data = EntityService.new({ entity_id: entity_id, entity_type: entity_type }).get_by_id
    
    unless entity_data.present?
      Rails.logger.info "Entity not found for id: #{entity_id}, type: #{entity_type}, user_id: #{@current_user.id}, tenant_id: #{tenant_id}"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Entity')}")
    end

    phone_number = nil
    
    if phone_id.present?
      selected_phone = entity_data['phoneNumbers']&.find { |phone| phone['id'] == phone_id.to_i }
      unless selected_phone.present?
        Rails.logger.info "Phone number not found for id: #{phone_id}, entity_id: #{entity_id}, user_id: #{@current_user.id}, tenant_id: #{tenant_id}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_phone_number}||#{I18n.t('error.invalid_phone_number_for_conversation')}")
      end
      phone_number = "#{selected_phone['dialCode']}#{selected_phone['value']}"
    end

    conversations = Conversation.where(tenant_id: tenant_id)
    
    if phone_number.present?
      conversations = conversations.where(phone_number: phone_number)
    else
      conversations = conversations.joins(conversation_look_ups: :look_up)
                  .where(look_ups: { entity_id: entity_id, entity_type: entity_type })
    end

    conversation = nil
    conversations.order(last_activity_at: :desc).find do |conv|
      if @current_user.can_user_read_conversation?(entity_id, entity_type, entity_data['ownerId'], [conv.owner_id])
        conversation = conv
        break
      end
    end

    if conversation.present?
      return { conversation: conversation, entity_data: { id: entity_id, entity_type: entity_type, entity_name: entity_name } }
    end

    if !phone_id.present?
      Rails.logger.info "No accessible conversation found for entity_id: #{entity_id}, entity_type: #{entity_type}, user_id: #{@current_user.id}, tenant_id: #{tenant_id}"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Conversation')}")
    end

    connected_account = ConnectedAccount.where(
      tenant_id: tenant_id,
      status: ACTIVE,
      is_verified: true
    ).first

    unless connected_account.present?
      Rails.logger.info "No active connected account found for tenant_id: #{tenant_id}"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected account')}")
    end

    unless @current_user.can_send_conversation_message?(entity_id, entity_type, entity_data['ownerId'])
      Rails.logger.info "User does not have permission to create conversation, User id: #{@current_user.id}, tenant_id: #{tenant_id}"
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    conversation = create_and_associate_conversation(
      tenant_id,
      connected_account.id,
      phone_number,
      @current_user.id,
      entity_id,
      entity_type,
      entity_data
    )

    return { conversation: conversation, entity_data: { id: entity_id, entity_type: entity_type, entity_name: entity_name } }
  end

  private

  def get_filter_rule_value(rule)
    return unless @params[:json_rule].present?

    json_rule= @params[:json_rule]

    if json_rule[:rules].present?
      rule = json_rule[:rules].find { |r| r['field'] == rule }
    end

    return [rule.dig('value', 'id'), rule.dig('value', 'entity')] if rule
  end

  def fetch_deleted_by(user_id, tenant_id)
    user = User.find_by(id: user_id, tenant_id: tenant_id)

    return user if user.present?

    permissions = [
      {
        "name": 'user',
        "description": 'has access to user resource',
        "limits": -1,
        "units": 'count',
        "action": {
          "read": true,
          "readAll": true
        }
      }
    ]
    token = GenerateToken.call(user_id, tenant_id, permissions)
    User::GetUserProfileDetails.call(user_id, tenant_id, token)
  end

  def check_basic_conversation_permissions(phone_number_conversation)
    unless @current_user.can_read_all_conversation? || phone_number_conversation.owner_id == @current_user.id
      Rails.logger.info "User does not have conversation permission conversationId: #{phone_number_conversation.id}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end
  end

  def conversation_session_response(conversation)
    last_message_received_at = conversation.last_message_received_at
    twenty_four_hours_ago = 24.hours.ago
    session_status = INACTIVE
    last_contacted_at = nil
    if last_message_received_at.present?
      session_status = last_message_received_at < twenty_four_hours_ago ? INACTIVE : ACTIVE
      last_contacted_at = last_message_received_at
    end
    { id: conversation.id, session: session_status, lastContactedAt: last_contacted_at }
  end
end
