class DeleteConversationsForEntity < ApplicationService
  def initialize(entity_type, data)
    @entity_type = entity_type
    @entity_id = data[:entity_id]
    @tenant_id = data[:tenant_id]
    @data = data
  end

  def call
    LookUp.where(tenant_id: @tenant_id, entity_type: @entity_type, entity_id: @entity_id).each do |look_up|
      @look_up = look_up
      @look_up.conversations.each do |conversation|
        if is_present_on_other_entities?(conversation)
          remove_entity_from_conversation(conversation)
        else
          ConversationService.new({id: conversation.id, tenant_id: @tenant_id, user_id: @data[:user_id], skip_permission_check: true}).delete_conversation
        end
      end
      @look_up.destroy!
    end
  end

  private

  def remove_entity_from_conversation(conversation)
    look_up_ids = conversation.look_ups.where(entity_type: @entity_type, entity_id: @entity_id).pluck(&:id)

    Rails.logger.info "Removing #{@entity_type} #{@entity_id} from tenant_id #{@tenant_id} conversation #{conversation.id} as associated entity is deleted"
    conversation.conversation_look_ups.where(look_up_id: look_up_ids).destroy_all
  end

  def is_present_on_other_entities?(conversation)
    conversation.look_ups.where.not(id: @look_up.id).exists?
  end
end
