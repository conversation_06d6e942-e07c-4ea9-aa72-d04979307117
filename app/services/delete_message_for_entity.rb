class DeleteMessageForEntity < ApplicationService
  def initialize(entity_type, data, destroy_look_ups = true)
    @entity_type = entity_type
    @entity_id = data[:entity_id]
    @tenant_id = data[:tenant_id]
    @data = data
    @destroy_look_ups = destroy_look_ups
  end

  def call
    LookUp.where(tenant_id: @tenant_id, entity_type: @entity_type, entity_id: @entity_id).each do |look_up|
      @look_up = look_up

      @look_up.messages.where(conversation_id: nil).each do |m|
        if is_related_to_other_entities?(m)
          remove_entity_from_recipients(m)
        else
          Message::DeleteMessageService.new(m.id, false, @data).soft_delete
        end
      end
      @look_up.destroy if @destroy_look_ups
    end
  end

  private

  def remove_entity_from_recipients(message)
    related_look_up_ids = message.related_to.where(entity_type: @entity_type, entity_id: @entity_id).pluck(&:id)

    Rails.logger.info "Removing #{@entity_type} #{@entity_id} from related to and recipients for message #{message.id} as associated #{@entity_type} #{@entity_id} is deleted"
    message.related_look_ups.where(look_up_id: related_look_up_ids).destroy_all
  end

  def is_related_to_other_entities?(message)
    has_other_recipient_entity = message.related_to.where.not(id: @look_up.id).where.not(entity_type: LOOKUP_USER).exists?

    has_other_recipient_entity
  end
end
