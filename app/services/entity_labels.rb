# frozen_string_literal: true

require 'rest-client'

class Entity<PERSON><PERSON><PERSON>
  def initialize(entity)
    @entity = entity
  end

  def call
    token = Thread.current[:token]

    begin
      response = RestClient.get(
        SERVICE_CONFIG + '/v1/entities/label',
        {
          Authorization: "Bearer #{token}"
        }
      )

      return JSON(response.body).with_indifferent_access[@entity.upcase]
    rescue RestClient::NotFound
      Rails.logger.error "EntityLabels Error while fetching label for #{@entity} - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

    rescue RestClient::InternalServerError
      Rails.logger.error "EntityLabels Error while fetching label for #{@entity} - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)

    rescue RestClient::BadRequest
      Rails.logger.error "EntityLabels Error while fetching label for #{@entity} - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end
