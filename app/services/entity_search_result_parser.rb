class EntitySearchResultParser
  def initialize(phone_numbers, data, entity_type, tenant_id)
    @phone_numbers = phone_numbers
    @data = data
    @entity_type = entity_type
    @tenant_id = tenant_id
  end

  def call
    matched = []
    unmatched = []

    @phone_numbers.each do |phone_number|
      @data['content'].each do |entity|
        if entity['phoneNumbers'].find{ |p_n| phone_number.raw_national == p_n['value'].strip && "+#{phone_number.country_code}" == "#{p_n['dialCode'].strip}" }
          # Prepare matched
          matched << {
            entity: @entity_type,
            id: entity['id'],
            phone_number: phone_number.original,
            name: entity['name'],
            tenant_id: @tenant_id,
            owner_id: entity['ownerId']
          }
          @phone_numbers.delete(phone_number)
        end
      end
    end

    # Prepare unmatched
    @phone_numbers.each{ |phone_number| unmatched << phone_number.original }
    Rails.logger.info "matched: #{matched} || unmatched: #{unmatched} || phone_numbers: #{@phone_numbers} || @data: #{@data}"
    return { matched: matched, unmatched: unmatched }
  end
end
