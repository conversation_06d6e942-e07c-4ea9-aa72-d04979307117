# frozen_string_literal: true

require 'rest-client'

class EntityService
  def initialize(params)
    @params = params
  end

  def get_by_id
    return {} unless @params[:entity_id].present?

    permissions = Thread.current[:auth].permissions.as_json.map do |permission|
      permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
      permission
    end

    token = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

    payload = {
      fields: ((@params[:fields] || %w[phoneNumbers id]) + fixed_fields_in_payload).uniq,
      jsonRule: {
        rules: json_rules,
        condition: 'AND',
        valid: true
      }
    }

    begin
      response = RestClient.post(
        entity_url,
        payload.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )

      return JSON(response.body)['content'][0] if JSON(response.body)['content'].any?

      Rails.logger.error "EntityService get_by_id - invalid response"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: @params[:entity_type])}")
    rescue RestClient::NotFound
      Rails.logger.error "EntityService get_by_id - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "EntityService get_by_id - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "EntityService get_by_id - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end

  def get_by_json_rule
    return {} unless (@params[:phone_number].present? || @params[:entity_id].present?)

    if @params[:generate_token]
      permissions = Thread.current[:auth].permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      token = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
    else
      token = Thread.current[:token]
    end

    payload = {
      fields: (@params[:fields] + fixed_fields_in_payload).uniq,
      jsonRule: {
        rules: json_rules,
        condition: 'AND',
        valid: true
      }
    }

    begin
      response = RestClient.post(
        entity_url,
        payload.to_json,
        {
          Authorization: "Bearer #{token}",
          content_type: :json,
          accept: :json
        }
      )

      return JSON(response.body)

      Rails.logger.error "EntityService get_by_json_rule #{entity_url} - invalid response"
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: @params[:entity_type])}")
    rescue RestClient::NotFound
      Rails.logger.error "EntityService get_by_json_rule  #{entity_url} - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "EntityService get_by_json_rule #{entity_url} - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "EntityService get_by_json_rule #{entity_url} - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end

  def create
    unless @params[:entity_type].present? && @params[:payload].present?
      Rails.logger.error "Incorrect Payload in EntityService create - params: #{@params.to_s}"
      return
    end

    unless [LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])
      Rails.logger.error "Invalid entity type in EntityService create - params: #{@params}"
      return
    end

    token = Thread.current[:token]

    response = RestClient.post(
      "#{SERVICE_SALES}/v1/#{@params[:entity_type].pluralize}",
      @params[:payload].to_json,
      {
        Authorization: "Bearer #{token}",
        content_type: :json,
        accept: :json
      }
    )

    JSON(response.body)
  rescue RestClient::NotFound
    Rails.logger.error "EntityService create - 404"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
  rescue RestClient::InternalServerError
    Rails.logger.error "EntityService create - 500"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  rescue RestClient::BadRequest
    Rails.logger.error "EntityService create - 400"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
  rescue StandardError => e
    Rails.logger.error "EntityService create - 500 - error #{e.message}"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  end

  private

  def entity_url
    case @params[:entity_type]
    when LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL
      url = "#{SERVICE_SEARCH}/v1/search/#{@params[:entity_type]}"
      return url unless @params[:query_params].present?
      "#{url}?#{@params[:query_params]}"
    when LOOKUP_USER
      "#{SERVICE_IAM}/v1/users/search"
    end
  end

  def fixed_fields_in_payload
    case @params[:entity_type]
    when LOOKUP_LEAD, LOOKUP_CONTACT
      %w[firstName lastName ownerId]
    else
      %w[firstName lastName]
    end
  end

  def json_rules
    if @params[:phone_number].present?
      [
        {
          operator: 'equal',
          id: 'phoneNumbers',
          field: 'phoneNumbers',
          type: 'string',
          value: @params[:phone_number]
        }
      ]
    elsif @params[:entity_id].is_a?(Array)
      [
        {
          operator: 'in',
          id: 'id',
          field: 'id',
          type: 'double',
          value: @params[:entity_id].join(',')
        }
      ]
    else
      [
        {
          operator: 'equal',
          id: 'id',
          field: 'id',
          type: 'double',
          value: @params[:entity_id].to_i
        }
      ]
    end
  end
end
