# frozen_string_literal: true

class EntityVariables
  def initialize(entity)
    @entity = entity
  end

  def get
    token = Thread.current[:token]

    if [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].include?(@entity)
      entity_fields_response = "Fields::#{@entity.classify}".constantize.new(token).fetch

      @entity_fields = entity_fields_response.inject([]) do |arr, f|
        arr << {
          'id' => f["id"],
          'displayName' => f['displayName'],
          'internalName' => f["name"],
          'standard' => f['standard'],
          'type' => f['type'],
          'entity' => @entity
        }
      end

      return (allowed_entity_variables + cross_entity_variables).reject { |field| EXCLUDED_VARIABLE_FIELD_TYPES.include?(field['type']) }
    else
      Rails.logger.error "EntityVariables - Invalid entity #{@entity}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid Entity')}")
    end
  end

  private

  def allowed_entity_variables
    excluded_fields = "#{@entity.upcase}_EXCLUDED_FIELDS".constantize
    modify_variables(
      @entity_fields.reject { |field| excluded_fields.include?(field['internalName']) || EXCLUDED_VARIABLE_FIELD_TYPES.include?(field['type']) },
      EntityLabels.new(@entity).call[:displayName],
      @entity
    )
  end

  def cross_entity_variables
    tenant_variables + user_variables
  end

  def tenant_variables
    JSON.parse(File.read("#{Rails.root}/config/tenant-variables.json"))
  end

  def user_variables
    variables = JSON.parse(File.read("#{Rails.root}/config/user-variables.json"))
    "#{@entity.upcase}_USER_LOOKUP_FIELDS".constantize.inject([]) do |arr, user_field|
      entity_label = @entity_fields.find { |f| f['internalName'] == user_field }['displayName']
      arr += modify_variables(variables.clone.map(&:clone), entity_label, user_field)
      arr
    end
  end

  def modify_variables(fields, entity_label, entity)
    fields.map do |hash|
      hash['displayName'] = "#{entity_label} - #{hash['displayName']}"
      hash['entity'] = entity
      hash['groupBy'] = entity_label

      hash
    end
  end
end
