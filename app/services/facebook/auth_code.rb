# frozen_string_literal: true

module Facebook
  class AuthCode
    def initialize(auth_code)
      @auth_code = auth_code
    end

    def exchange
      url = URI("#{FACEBOOK_HOST}/oauth/access_token?client_id=#{ENV['FACEBOOK_CLIENT_ID']}&client_secret=#{ENV['FACEBOOK_CLIENT_SECRET']}&code=#{@auth_code}")
      https = Net::HTTP.new(url.host, url.port)
      https.use_ssl = true
      request = Net::HTTP::Get.new(url)
      response = https.request(request)
      if response.code == '200'
        Facebook::Response.new(status_code: response.code, body: JSON.parse(response.body))
      else
        Rails.logger.error "Error in Facebook::Code.exchange - #{response.code} - #{response.body}"
        payload = JSON.parse(response.body)
        raise(ExceptionHandler::ThirdPartyAPIError, "#{ErrorCode.third_party_api_error}||#{payload.dig('error', 'message')}")
      end
    end
  end
end
