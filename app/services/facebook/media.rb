# frozen_string_literal: true

module Facebook
  class Media
    def initialize(connected_account)
      @connected_account = connected_account
    end

    def upload(file)
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account.phone_number_id}/media",
        request_type: :post,
        file: file.path,
        type: get_mime_type(File.extname(file.original_filename).downcase),
        messaging_product: 'whatsapp',
        content_type: 'form-data',
        body: file.path
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    private
    def get_mime_type(file_extension)
      case file_extension
      when '.aac'
        'audio/aac'
      when '.amr'
        'audio/amr'
      when '.m4a'
        'audio/mp4'
      else
        Rack::Mime.mime_type(file_extension)
      end
    end
  end
end
