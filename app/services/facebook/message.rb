# frozen_string_litera: true

module Facebook
  class Message
    def initialize(connected_account, phone_number)
      @connected_account = connected_account
      @phone_number = phone_number
    end

    def send_template_message(whatsapp_template, template_payload)
      payload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: @phone_number,
        type: 'template',
        template: {
          name: whatsapp_template.whatsapp_template_namespace,
          language: {
            code: whatsapp_template.language
          },
          components: template_payload
        }
      }

      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}/messages",
        request_type: :post,
        body: payload
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    def send_session_message(message_type, message_payload)
      payload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: @phone_number,
        type: message_type,
        message_type => message_payload
      }

      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}/messages",
        request_type: :post,
        body: payload
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end
  end
end
