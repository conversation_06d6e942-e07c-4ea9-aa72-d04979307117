# frozen_string_literal: true

module Facebook
  class Phone<PERSON>umber
    def initialize(connected_account)
      @connected_account = connected_account
    end

    def find
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}",
        request_type: :get
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    def enable2FA(pin)
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}",
        request_type: :post,
        body: {
          pin: pin
        }
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    def register(pin)
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}/register",
        request_type: :post,
        body: {
          messaging_product: 'whatsapp',
          pin: pin
        }
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    def deregister
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}/deregister",
        request_type: :post
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    def request_code(code_method, language)
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}/request_code",
        request_type: :post,
        body: {
          code_method: code_method,
          language: language
        }
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end

    def verify_code(otp_code)
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account&.phone_number_id}/verify_code",
        request_type: :post,
        body: {
          code: otp_code
        }
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end
  end
end
