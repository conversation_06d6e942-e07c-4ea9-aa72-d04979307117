# frozen_string_literal: true

class Facebook::Request
  def self.process(request_parameters, connected_account = nil)
    if connected_account.blank?
      raise(
        ExceptionHandler::AccountNotConnectedError,
        "#{ErrorCode.account_not_connected}||#{I18n.t('error.account_not_connected')}"
      )
    end

    uri = URI(request_parameters[:url])
    https = Net::HTTP.new(uri.host, uri.port)
    https.use_ssl = true
    request = "Net::HTTP::#{request_parameters[:request_type].to_s.titleize}".constantize.new(uri)
    if request_parameters[:body].present?
      if request_parameters[:content_type].presence == 'file'
        request.content_type = 'multipart/formdata'
        request['Authorization'] = "OAuth #{ConnectedAccount.decrypt(connected_account.access_token)}"
        request['file_offset'] = '0'
        request.body = File.open(request_parameters[:body], 'rb').read
      elsif request_parameters[:content_type].presence == 'form-data'
        form_data = [
          ['file', File.open(request_parameters[:file], 'rb').read, { filename: File.basename(request_parameters[:file]), content_type: request_parameters[:type] }],
          ['type', request_parameters[:type]],
          ['messaging_product', 'whatsapp']
        ]

        request.set_form form_data, 'multipart/form-data'
      else
        request.body = request_parameters[:body].to_json
        request['Content-Type'] = 'application/json'
      end
    else
      request['Content-Type'] = 'application/json'
    end

    request['Authorization'] = "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}" unless request['Authorization'].present?
    response = https.request(request)
    parsed_body =
      begin
        JSON.parse(response.body)
      rescue JSON::ParserError, TypeError
        response.body
      end

    response = Facebook::Response.new(status_code: response.code, body: parsed_body)
    if response.status_200?
      # TODO Remove this before final release
      Rails.logger.info "Facebook response for request url #{request_parameters[:url]} body #{request_parameters[:body]} #{response.body}"
      response
    else
      if connected_account.persisted? && parsed_body.dig('error', 'code') == 133006
        connected_account.update_column(:is_verified, false)
      end

      Rails.logger.error "Error in Facebook Request - #{response.status_code} - #{response.body}"
      raise(ExceptionHandler::ThirdPartyAPIError, "#{ErrorCode.third_party_api_error}||#{error_message(parsed_body)}")
    end
  end

  private

  def self.error_message(parsed_body)
    if parsed_body.dig('error', 'error_user_title').present?
      "#{parsed_body.dig('error', 'message')}. #{parsed_body.dig('error', 'error_user_title')}, #{parsed_body.dig('error', 'error_user_msg')}"
    else
      parsed_body.dig('error', 'message')
    end
  end
end
