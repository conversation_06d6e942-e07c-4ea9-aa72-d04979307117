# frozen_string_literal: true

module Facebook
  class UploadSession
    def initialize(template_media_params, connected_account)
      @template_media_params = template_media_params
      @connected_account = connected_account
    end

    def start
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{ENV['FACEBOOK_CLIENT_ID']}/uploads?file_name=#{@template_media_params[:file_name]}&file_length=#{@template_media_params[:file_size]}&file_type=#{@template_media_params[:file_type]}",
        request_type: :post,
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end
  end
end
