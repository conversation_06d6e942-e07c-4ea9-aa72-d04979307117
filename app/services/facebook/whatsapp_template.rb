# frozen_string_literal: true

module Facebook
  class WhatsappTemplate
    include WhatsappT<PERSON><PERSON><PERSON><PERSON><PERSON>

    def initialize(connected_account)
      @connected_account = connected_account
    end

    def save(whatsapp_template)
      request_parameters = {
        url: template_save_url(whatsapp_template.whatsapp_template_id),
        request_type: :post,
        body: generate_template_body(whatsapp_template)
      }

      facebook_response = Facebook::Request.process(request_parameters, @connected_account)
      whatsapp_template.status = facebook_response.body['status'].presence || PENDING_STATUS
      whatsapp_template.category = facebook_response.body['category'].presence || whatsapp_template.category
      whatsapp_template.whatsapp_template_id = facebook_response.body['id'] unless whatsapp_template.whatsapp_template_id.present?
      whatsapp_template.reason = nil
      whatsapp_template.additional_info = nil

      whatsapp_template
    end

    private

    def template_save_url(template_id)
      if template_id.present?
        "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{template_id}"
      else
        "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@connected_account.waba_id}/message_templates"
      end
    end
  end
end
