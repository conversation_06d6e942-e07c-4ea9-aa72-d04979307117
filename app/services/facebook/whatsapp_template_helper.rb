# frozen_string_literal: true

module Facebook
  module WhatsappTem<PERSON><PERSON><PERSON><PERSON>
    def generate_template_body(whatsapp_template)
      {
        name: whatsapp_template.whatsapp_template_namespace,
        language: whatsapp_template.language,
        category: whatsapp_template.category,
        components: generate_components_body(whatsapp_template.components)
    }.with_indifferent_access
    end

    def generate_components_body(components)
      button_components, other_components = components.partition { |component| component.component_type == BUTTON }

      other_components.inject([]) do |components_array, component|
        components_array << send("generate_#{component.component_type.downcase}_#{component.component_format.downcase}_component", component)
      end.concat(
        button_components.present? ? [generate_buttons_body(button_components)] : []
      )
    end

    def generate_header_text_component(component)
      {
        type: component.component_type,
        format: component.component_format,
        text: component.component_text
      }.merge(component.content.present? ? { example: component.content } : {})
    end

    def generate_header_image_component(component)
      {
        type: component.component_type,
        format: component.component_format,
        example: {
          header_handle: [ component.template_media.whatsapp_file_handle ]
        }
      }
    end

    def generate_body_text_component(component)
      {
        type: component.component_type,
        text: component.component_text
      }.merge(component.content.present? ? { example: component.content } : {})
    end

    def generate_footer_text_component(component)
      {
        type: component.component_type,
        text: component.component_text
      }
    end

    def generate_buttons_body(button_components)
      {
        type: "BUTTONS",
        buttons: generate_buttons_array(button_components.sort_by(&:position))
      }
    end

    def generate_buttons_array(sorted_button_components)
      sorted_button_components.inject([]) do |buttons_array, component|
        buttons_array << send("generate_#{component.component_type.downcase}_#{component.component_format.downcase}_component", component)
      end
    end

    def generate_button_quick_reply_component(component)
      {
        type: component.component_format,
        text: component.component_text
      }
    end

    def generate_button_phone_number_component(component)
      {
        type: component.component_format,
        text: component.component_text,
        phone_number: component.component_value
      }
    end

    def generate_button_url_component(component)
      {
        type: component.component_format,
        text: component.component_text,
        url: component.component_value.gsub('{{2}}', '{{1}}')
      }.merge(component.content.present? ? { example: component.content } : {})
    end


    def generate_button_copy_code_component(component)
      {
        type: component.component_format,
        example: component.component_text
      }
    end

    alias generate_header_video_component generate_header_image_component
    alias generate_header_document_component generate_header_image_component
  end
end
