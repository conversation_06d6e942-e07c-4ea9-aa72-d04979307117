# frozen_string_literal: true

module Facebook
  class WhatsappTemplateMedia
    def initialize(file_path, mime_type, upload_session_id, connected_account)
      @file_path = file_path
      @mime_type = mime_type
      @upload_session_id = upload_session_id
      @connected_account = connected_account
    end

    def start_upload
      request_parameters = {
        url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{@upload_session_id}",
        request_type: :post,
        body: @file_path,
        content_type: 'file',
        mime_type: @mime_type
      }

      Facebook::Request.process(request_parameters, @connected_account)
    end
  end
end
