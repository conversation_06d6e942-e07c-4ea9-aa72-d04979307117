# frozen_string_literal: true

require 'rest-client'

module Fields
  class Base
    def initialize(token)
      @token = token
    end

    def fetch
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless @token

      begin
        response = RestClient.get(url, { Authorization: "Bearer #{@token}" })

        return JSON(response.body) unless response.nil? || response == ''

        Rails.logger.error "Fields Error while fetching fields for #{entity_type} - invalid response"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

      rescue RestClient::NotFound
        Rails.logger.error "Fields Error while fetching fields for #{entity_type} - 404"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

      rescue RestClient::InternalServerError
        Rails.logger.error "Fields Error while fetching fields for #{entity_type} - 500"
        raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)

      rescue RestClient::BadRequest
        Rails.logger.error "Fields Error while fetching fields for #{entity_type} - 400"
        raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
      end
    end
  end
end
