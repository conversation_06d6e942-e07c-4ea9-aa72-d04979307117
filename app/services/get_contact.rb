require 'rest-client'

class GetContact < ApplicationService
  def initialize(contact_id, token = nil)
    @contact_id = contact_id
    @token = token
  end

  def call
    Rails.logger.info "Getting contact #{@contact_id}"

    unless @token
      @token = Thread.current[:token]
      raise(ExceptionHandler::Authentication<PERSON>rror, ErrorCode.unauthorized) unless @token
    end

    begin
      response = RestClient.get(
        SERVICE_SALES + "/v1/contacts/#{@contact_id}",
        {
          'Authorization': "Bearer #{@token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "Get Contact - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::NotFound
      Rails.logger.error "Get Contact - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get Contact - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get Contact - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end