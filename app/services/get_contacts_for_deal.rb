require 'rest-client'

class GetContactsForDeal < ApplicationService
  attr_accessor :deal_id

  def initialize(deal_id, token = nil)
    @deal_id = deal_id
    @token = token
  end

  def call
    unless @token
      @token = Thread.current[:token]
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless @token
    end

    begin
      url = SERVICE_SEARCH + '/v1/search/contact?page=0&size=30'
      response = RestClient.post(url, payload(deal_id), header)

      deal_contacts = JSON(response.body)
      return [] unless deal_contacts
      Rails.logger.info "Data fetched from search service:  #{deal_contacts}"
      return deal_contacts['content']
    rescue RestClient::NotFound
      Rails.logger.error 'Get associated contacts on Deal - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Get associated contacts on Deal 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error 'Get associated contacts on Deal 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end

  private

  def payload(deal_id)
    {
      "fields": ['id', "phoneNumbers"],
      "jsonRule": {
        "condition": 'AND',
        "rules": [
          {
            "operator": 'equal',
            "id": 'associatedDeals',
            "field": 'associatedDeals',
            "type": 'long',
            "value": deal_id
          }
        ],
        "valid": true
      }
    }.to_json
  end

  def header
    {
      :Authorization => "Bearer #{ @token }",
      :content_type => 'application/json'
    }
  end
end
