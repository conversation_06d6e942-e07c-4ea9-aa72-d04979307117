# frozen_string_literal: true

class GetDealsForContacts < ApplicationService
  attr_accessor :contact_ids, :tenant_id

  def initialize(contact_ids, tenant_id = nil)
    @contact_ids = Array(contact_ids).compact
    @tenant_id = tenant_id || current_tenant_id
  end

  def call
    return {} if @contact_ids.blank? || @tenant_id.blank?

    contact_deal_associations = ContactDealAssociation.for_tenant_contacts(@tenant_id, @contact_ids)
                                                        .select(:contact_id, :deal_id, :deal_name)

    contact_deal_associations.group_by(&:contact_id).transform_values do |deals|
      deals.map { |deal| { id: deal.deal_id, name: deal.deal_name } }
    end
  end

  private

  def current_tenant_id
    Thread.current[:user].tenant_id
  end
end