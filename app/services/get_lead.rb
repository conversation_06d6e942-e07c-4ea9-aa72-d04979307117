require 'rest-client'

class GetLead < ApplicationService
  def initialize(lead_id, token = nil)
    @lead_id = lead_id
    @token = token
  end

  def call
    unless @token
      Rails.logger.info "Getting token for current user"
      @token = Thread.current[:token]
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless @token
    end

    Rails.logger.info "Getting lead #{@lead_id}"

    begin
      response = RestClient.get(
        SERVICE_SALES + "/v1/leads/#{@lead_id}",
        {
          'Authorization': "Bearer #{@token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "Get Lead - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::NotFound => err
      Rails.logger.info "Set_owner:: Get lead 404 error::#{err.response&.body&.inspect}"
      Rails.logger.error "Get Lead - 404"
      raise(ExceptionHandler::InvalidLeadError, ErrorCode.invalid_lead)
    rescue RestClient::InternalServerError => err
      Rails.logger.info "Set_owner:: Get lead 500 error::#{err.response.inspect}"
      Rails.logger.error "Get Lead - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest => err
      Rails.logger.info "Set_owner:: Get lead 400 error::#{err.response.inspect}"
      Rails.logger.error "Get Lead - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end