class GetLookUp

  def self.initialize_data look_up_data
    @data = look_up_data
    current_user = Thread.current[:user]
    @tenant_id = look_up_data[:tenant_id] || current_user.tenant_id
  end

  def self.call look_up_data
    initialize_data look_up_data
    if @data[:id].blank?
      raise ExceptionHandler::InvalidDataError, ErrorCode.invalid_data
    elsif @tenant_id.blank?
        raise ExceptionHandler::InvalidDataError, ErrorCode.invalid_data
    elsif @data[:entity].blank?
      raise ExceptionHandler::InvalidDataError, ErrorCode.invalid_data
    else
      look_up = LookUp.find_or_initialize_by(
        tenant_id: @tenant_id,
        entity_type: @data[:entity],
        entity_id: @data[:id],
        phone_number: "#{@data[:phone_number]}"
      )
      if look_up.new_record?
        look_up.name = @data[:name]
        look_up.phone_number = @data[:phone_number]
      end
      look_up
    end
  end
end

