class GetLookUp<PERSON><PERSON><PERSON>wner

  def self.initialize_data look_up_data
    @data = look_up_data
    current_user = Thread.current[:user]
    @tenant_id = look_up_data[:tenant_id] || current_user.tenant_id
  end

  def self.call look_up_data
    initialize_data look_up_data
    
    look_up = GetLookUp.call(look_up_data)

    if look_up.new_record?
      look_up.save!
    end

    if look_up.owner_id.present?
      return look_up
    end

    if look_up_data[:owner_id].present?
      look_up.owner_id = look_up_data[:owner_id]
      look_up.save!
      return look_up
    end

    admin_token = get_admin_token(look_up.entity_type, look_up.entity_id, @tenant_id)

    if look_up.entity_type == LOOKUP_LEAD
      entity_data = GetLead.call(look_up.entity_id, admin_token)
      look_up.owner_id = entity_data['ownerId']
    elsif look_up.entity_type == LOOKUP_CONTACT
      entity_data = GetContact.call(look_up.entity_id, admin_token)
      look_up.owner_id = entity_data['ownerId']
    end

    look_up.save!
    look_up
  end

  def self.get_admin_token(entity, user_id, tenant_id)
    permissions = [{
      "name": entity,
      "description": "has access to #{entity} resource",
      "limits": -1,
      "units": 'count',
      "action": {
        "read": true,
        "readAll": true
      }
    }]

    admin_token = GenerateToken.call(user_id, tenant_id, permissions)
    admin_token
  end
end
