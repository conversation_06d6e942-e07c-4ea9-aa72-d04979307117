# frozen_string_literal: true

require 'rest-client'

class GetMaskedFields < ApplicationService
  def initialize(entity_type)
    @entity_type = entity_type
    @token = Thread.current[:token]
  end

  def call
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless @token

    begin
      response = RestClient.get(
        "#{SERVICE_CONFIG}/v1/entities/#{@entity_type}/masked-fields",
        {
          'Authorization': "Bearer #{@token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error 'Get Masked Fields - invalid response'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

    rescue RestClient::NotFound
      Rails.logger.error 'Get Masked Fields - 404'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)

    rescue RestClient::InternalServerError
      Rails.logger.error 'Get Masked Fields - 500'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)

    rescue RestClient::BadRequest
      Rails.logger.error 'Get Masked Fields - 400'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end
