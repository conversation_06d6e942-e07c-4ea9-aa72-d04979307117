# frozen_string_literal: true

class GetSharedEntities
  def initialize(entity, action = 'read')
    @entity = entity
    @action = action
  end

  def call
    token = Thread.current[:token]

    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless token.present?

    return {} unless [<PERSON><PERSON><PERSON><PERSON>_LEAD, LO<PERSON>UP_CONTACT, LOOKUP_DEAL].include?(@entity)

    begin
      Rails.logger.info "GetSharedEntities: Entity #{@entity} Action #{@action}"
      response = RestClient.get(
        SERVICE_CONFIG + "/v1/internal/share/access/#{@entity.upcase}/#{@action.upcase}",
        {
          'Authorization': "Bearer #{token}"
        }
      )

      return JSON(response.body) unless response.nil?
    rescue RestClient::NotFound
      Rails.logger.info "GetSharedEntities: Not Found 404"
      raise(ExceptionHandler::RecordNotFound, ErrorCode.not_found)
    rescue RestClient::InternalServerError
      Rails.logger.info "GetSharedEntities: Internal Server Error 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.info "GetSharedEntities: Bad Request 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
