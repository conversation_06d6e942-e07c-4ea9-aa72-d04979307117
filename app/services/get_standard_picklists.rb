# frozen_string_literal: true

require 'rest-client'

class GetStandardPicklists < ApplicationService
  # local caching of standard picklist
  # Refer: activesupport-6.0.3/lib/active_support/core_ext/module/attribute_accessors_per_thread.rb
  thread_cattr_accessor :standard_picklists

  def initialize
    @token = Thread.current[:token]
  end
  
  def call
    return standard_picklists if standard_picklists.present?
    begin
      response = RestClient.get(
        SERVICE_CONFIG + "/v1/picklists/standard",
        {
          'Authorization': "Bearer #{@token}"
        }
      )

      if response.present?
        self.standard_picklists = JSON(response.body)
        return self.standard_picklists
      end

      Rails.logger.error "Get Picklist - invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "Get Picklist - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "Get Picklist - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "Get Picklist - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
