# frozen_string_literal: true

require 'rest-client'

class GetTenant < ApplicationService
  def call
    begin
      response = RestClient.get(
        "#{SERVICE_IAM}/v1/tenants",
        {
          'Authorization': "Bearer #{Thread.current[:token]}"
        }
      )
      return JSON(response.body) unless response.nil?

      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::NotFound
      Rails.logger.error "GetTenant - 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "GetTenant - 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "GetTenant - 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end
