# frozen_string_literal: true

module Interakt
  class ConversationAnalytics
    def initialize(connected_account, start_time, end_time)
      @connected_account = connected_account
      @start_time = start_time
      @end_time = end_time
    end

    def fetch
      phone_number = @connected_account.waba_number
      phone_number = phone_number.start_with?('+') ? phone_number : "+#{phone_number}"
      phone_number = Phonelib.parse(phone_number)
      phone_number = "+#{phone_number.country_code}#{phone_number.raw_national}"

      request_parameters = {
        url: "#{INTERAKT_API_HOST}/api/#{INTERAKT_API_VERSION}/#{@connected_account.waba_id}?fields=pricing_analytics.start(#{@start_time}).end(#{@end_time}).granularity(DAILY).phone_numbers([\"#{phone_number}\"]).dimensions([\"PRICING_CATEGORY\",\"PRICING_TYPE\",\"COUNTRY\",\"PHONE\"])",
        request_type: :get,
      }

      Interakt::Request.process(request_parameters, @connected_account)
    end
  end
end
