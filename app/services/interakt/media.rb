# frozen_string_literal: true

module Interakt
  class Media
    def initialize(connected_account)
      @connected_account = connected_account
    end

    def get_media_url(media_id)
      request_parameters = {
        url: "#{INTERAKT_API_HOST}/api/#{INTERAKT_API_VERSION}/#{@connected_account&.phone_number_id}/media/#{media_id}",
        request_type: :get,
      }

      Interakt::Request.process(request_parameters, @connected_account)
    end

    def download_from_media_url(media_url)
      request_parameters = {
        url: "#{INTERAKT_API_HOST}/api/#{INTERAKT_API_VERSION}/#{@connected_account&.phone_number_id}/media?url=#{media_url}",
        request_type: :get,
      }

      Interakt::Request.process(request_parameters, @connected_account, unformatted_response = true)
    end
  end
end
