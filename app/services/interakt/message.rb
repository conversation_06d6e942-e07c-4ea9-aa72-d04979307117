# frozen_string_litera: true

module Interakt
  class Message
    def initialize(connected_account)
      @connected_account = connected_account
    end

    def send_template_message(whatsapp_template, template_payload, phone_number)
      payload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: phone_number,
        type: 'template',
        template: {
          name: whatsapp_template.whatsapp_template_namespace,
          language: {
            code: whatsapp_template.language
          },
          components: template_payload
        }
      }

      request_parameters = {
        url: "#{INTERAKT_API_HOST}/api/#{INTERAKT_API_VERSION}/#{@connected_account&.phone_number_id}/messages",
        request_type: :post,
        body: payload
      }

      Interakt::Request.process(request_parameters, @connected_account)
    end

    def send_session_message(message_type, message_payload, phone_number)
      payload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: phone_number,
        type: message_type,
        message_type => message_payload
      }

      request_parameters = {
        url: "#{INTERAKT_API_HOST}/api/#{INTERAKT_API_VERSION}/#{@connected_account&.phone_number_id}/messages",
        request_type: :post,
        body: payload
      }

      Interakt::Request.process(request_parameters, @connected_account)
    end

    def mark_message_as_read(incoming_whatsapp_message_id)
      request_parameters = {
        url: "#{INTERAKT_API_HOST}/api/#{INTERAKT_API_VERSION}/#{@connected_account&.phone_number_id}/messages",
        request_type: :post,
        body: {
          messaging_product: 'whatsapp',
          status: 'read',
          message_id: incoming_whatsapp_message_id
        }
      }

      Interakt::Request.process(request_parameters, @connected_account)
    end
  end
end
