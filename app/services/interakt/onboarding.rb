# frozen_string_literal: true

module Interakt
  class Onboarding
    def initialize(connected_account)
      @connected_account = connected_account
    end

    def signup
      uri = URI("#{INTERAKT_HOST}/v1/organizations/tp-signup/")
      https = Net::HTTP.new(uri.host, uri.port)
      https.use_ssl = true
      request = Net::HTTP::Post.new(uri)
      request.body = build_signup_body.to_json
      request['Content-Type'] = 'application/json'
      request['Authorization'] = ENV['INTERAKT_PARTNER_TOKEN']
      response = https.request(request)
      parsed_body =
        begin
          JSON.parse(response.body)
        rescue JSON::ParserError, TypeError
          response.body
        end

      response = Interakt::Response.new(status_code: response.code, body: parsed_body)
      if response.status_200?
        # TODO Remove this before final release
        Rails.logger.info "Interakt response for onboarding request body #{build_signup_body.inspect} #{response.body}"
        response
      else
        Rails.logger.error "Error in Interakt Request - #{response.status_code} - #{response.body}"
        raise(ExceptionHandler::ThirdPartyAPIError, "#{ErrorCode.third_party_api_error}||#{parsed_body['message']}")
      end
    end

    private

    def build_signup_body
      {
        entry: [
          {
            changes: [
              {
                value: {
                  event: 'PARTNER_ADDED',
                  waba_info: {
                    waba_id: @connected_account&.waba_id,
                    solution_id: ENV['INTERAKT_SOLUTION_ID'],
                    phone_number: @connected_account&.waba_number
                  }
                }
              }
            ]
          }
        ],
        object: 'tech_partner'
      }
    end
  end
end
