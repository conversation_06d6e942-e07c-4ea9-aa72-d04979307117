# frozen_string_literal: true

class Interakt::Request
  def self.process(request_parameters, connected_account = nil, unformatted_response = false)
    if connected_account.blank?
      raise(
        ExceptionHandler::AccountNotConnectedError,
        "#{ErrorCode.account_not_connected}||#{I18n.t('error.account_not_connected')}"
      )
    end

    uri = URI(request_parameters[:url])
    https = Net::HTTP.new(uri.host, uri.port)
    https.use_ssl = true
    request = "Net::HTTP::#{request_parameters[:request_type].to_s.titleize}".constantize.new(uri)
    if request_parameters[:body].present?
      request.body = request_parameters[:body].to_json
    end
    request['Content-Type'] = 'application/json'
    request['x-access-token'] = ENV['INTERAKT_PARTNER_TOKEN']
    request['x-waba-id'] = connected_account.waba_id
    response = https.request(request)

    return response if unformatted_response.present? && response.code == '200'

    parsed_body =
      begin
        JSON.parse(response.body)
      rescue JSON::ParserError, TypeError
        response.body
      end

    response = Interakt::Response.new(status_code: response.code, body: parsed_body)
    # TODO Remove this before final release
    Rails.logger.info "Interakt response for request url #{request_parameters[:url]} body #{request_parameters[:body]} #{response.body}"
    if response.status_200?
      response
    else
      Rails.logger.error "Error in Interakt Request - #{response.status_code} - #{response.body}"
      raise(ExceptionHandler::ThirdPartyAPIError, "#{ErrorCode.third_party_api_error}||#{error_message(parsed_body)}")
    end
  end

  private

  def self.error_message(parsed_body)
    if parsed_body.present? && parsed_body.is_a?(String)
      parsed_body
    elsif parsed_body.present? && parsed_body['error']&.is_a?(String)
      parsed_body['error']
    elsif parsed_body&.dig('error', 'error_user_title').present?
      "#{parsed_body.dig('error', 'message')}. #{parsed_body.dig('error', 'error_user_title')}, #{parsed_body.dig('error', 'error_user_msg')}"
    elsif parsed_body&.dig('error', 'error_data').present?
      "#{parsed_body.dig('error', 'message')}. #{parsed_body.dig('error', 'error_data', 'details')}"
    elsif parsed_body&.dig('error', 'message').present?
      parsed_body.dig('error', 'message')
    else
      I18n.t('error.something_went_wrong')
    end
  end
end
