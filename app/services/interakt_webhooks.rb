# frozen_string_literal: true

class InteraktWebhooks
  def initialize(payload)
    @payload = payload.with_indifferent_access
  end

  def process
    begin
      if [WABA_ONBOARDED, WABA_ONBOARDING_FAILED].include?(@payload.try(:[], :event))
        connected_account = ConnectedAccount.find_by(waba_id: @payload[:waba_id], phone_number_id: @payload[:phone_number_id], interakt_onboarding_status: REQUEST_SENT)
        if connected_account.present?
          if @payload[:event] == WABA_ONBOARDED
            connected_account.update!(
              interakt_onboarding_status: WABA_ONBOARDED,
              interakt_isv_token: ConnectedAccount.encrypt(@payload[:isv_name_token]),
              status: [DRAFT, PENDING, ACTIVE].include?(connected_account.status) ? ACTIVE : connected_account.status,
              deactivated_at: nil
            )
          elsif @payload[:event] == WABA_ONBOARDING_FAILED
            waba_error = @payload.try(:[], :error).try(:[], :error)
            connected_account.update!(
              interakt_onboarding_status: @payload[:event],
              interakt_isv_token: ConnectedAccount.encrypt(@payload[:isv_name_token]),
              interakt_message: "#{waba_error.try(:[], :error_subcode)} #{waba_error.try(:[], :error_user_msg)}",
              status: INACTIVE,
              deactivated_at: DateTime.now.in_time_zone('Asia/Calcutta').to_i
            )
          end
        else
          Rails.logger.error "InteraktWebhooks Connected Account Not Found for payload #{@payload.inspect}"
        end
      elsif @payload[:type] == 'message_template_status_update'
        template_status_data = @payload[:data]
        whatsapp_template =
          WhatsappTemplate.find_by(
            whatsapp_template_id: template_status_data[:message_template_id],
            connected_account_id: ConnectedAccount.where(waba_id: template_status_data[:waba_id])
          )
        if whatsapp_template.present?
          if whatsapp_template.update(
            status: template_status_data[:event],
            reason: template_status_data[:reason],
            additional_info: retrieve_additional_info(template_status_data)
          )
            PublishEvent.call(Event::WhatsappTemplateStatusUpdated.new(whatsapp_template))
          else
            Rails.logger.error "InteraktWebhooks Error while updating template status payload #{@payload.inspect} message #{whatsapp_template&.errors&.full_messages&.to_sentence}"
          end
        end
      else
        WhatsappWebhooks.new(@payload).process
      end
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "InteraktWebhooks ActiveRecord RecordInvalid message #{e.message} payload #{@payload.inspect}"
    rescue StandardError => e
      Rails.logger.error "InteraktWebhooks Something went wrong message #{e.message} payload #{@payload.inspect}"
    end
  end

  private

  def retrieve_additional_info(template_status_data)
    case template_status_data[:event]
    when APPROVED, REJECTED, PENDING_DELETION
      nil
    when PAUSED
      template_status_data[:other_info]
    when FLAGGED
      template_status_data[:disable_info]
    end
  end
end
