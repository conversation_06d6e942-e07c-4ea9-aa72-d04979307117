class Message::GetConversationMessageDetailsService < ApplicationService
  def initialize(params)
    @params = params
  end

  def call
    current_user = Thread.current['user']
    user_id = current_user.id
    tenant_id = current_user.tenant_id

    @message = Message.find_by(id: @params[:message_id], tenant_id: tenant_id, conversation_id: @params[:conversation_id])
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless @message

    conversation = Conversation.find_by(id: @params[:conversation_id], tenant_id: tenant_id)

    if @params[:entity_id].present? && @params[:entity_type].present?
      entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id
      return [@message, conversation.look_ups] if current_user.can_user_read_conversation?(@params[:entity_id], @params[:entity_type], entity_data['ownerId'], [conversation.owner_id])
    else
      return [@message, conversation.look_ups] if current_user.can_user_read_conversation_unrelated_to_entity?(conversation.owner_id)
    end

    Rails.logger.error "Insufficient permission: conversation message tenant_id: #{tenant_id} params#{@params}"
    raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
  end
end
