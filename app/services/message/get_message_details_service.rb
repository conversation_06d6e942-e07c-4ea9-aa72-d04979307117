class Message::GetMessageDetailsService < ApplicationService
  def initialize(id)
    @id = id
  end

  def call
    current_user = Thread.current['user']
    user_id = current_user.id
    tenant_id = current_user.tenant_id
    @message = Message.find_by(id: @id, tenant_id: tenant_id)
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless @message
    return @message if has_message_permission_on_related_entity_type && has_permission_on_message(user_id, tenant_id)
    Rails.logger.error 'Insufficient permission: message'
    raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
  end

  private

  def has_message_permission_on_related_entity_type
    auth_data = Thread.current['auth']
    related_to_type = @message.related_to.first.entity_type
    permission = auth_data.permissions.find{ |p| p.name == related_to_type }
    permission&.action&.sms
  end

  def has_permission_on_message(user_id, tenant_id)
    return true if @message.owner_id == user_id
    return true if @message.tenant_id == tenant_id
  end
end
