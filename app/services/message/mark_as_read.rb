# frozen_string_literal: true

class Message::<PERSON><PERSON><PERSON><PERSON>
  def initialize(message_id)
    @message_id = message_id
  end

  def call
    message = Message::GetMessageDetailsService.new(@message_id).call

    unless message.message_type == WHATSAPP_BUSINESS && message.direction == 'incoming'
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_action.cannot_mark_message_as_read')}")
    end

    connected_account = message.connected_account
    unless connected_account&.status == ACTIVE
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_action.inactive_connected_account')}")
    end

    Interakt::Message.new(connected_account).mark_message_as_read(message.remote_id)
    message.read_at = DateTime.now.utc
    message.status = 'read'
    message.save!

    message
  end
end
