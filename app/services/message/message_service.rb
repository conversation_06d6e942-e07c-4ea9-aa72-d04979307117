class Message::MessageService < ApplicationService
  include RelatedEntityProcessor
  include OwnerTokenProcessor

  def initialize(params, owner_token = nil, owner_profile_permissions = nil, owner_user = nil, skip_message_created_events = false)
    @params = params
    @owner_profile_permissions = owner_profile_permissions
    @owner_token = owner_token
    @owner_user = owner_user
    @skip_message_created_events = skip_message_created_events
  end

  def call
    owner_id = @params[:owner_id]
    @current_user = Thread.current[:user]
    @tenant_id = @current_user.tenant_id

    unless @owner_token
      if owner_id && @current_user.id != owner_id
        set_owner_token
      end

      unless @owner_token || has_message_permission_on_related_entity_type  || (@params[:message_type] == WHATSAPP_BUSINESS && has_sms_write_permission_on_profile?)
        Rails.logger.error 'Insufficient permission: Create Message'
        raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
      end
    end

    if @owner_token.present?
      unless has_message_permission_on_profile
        Rails.logger.info "Owner doesn't have permission. Couldn't set owner id on message"
        @owner_user = @owner_token = @owner_profile_permissions = @params[:owner_id] = nil
        raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed) unless has_message_permission_on_related_entity_type
      end
    end

    begin
      @message = Message.new(@params.except(:related_to, :recipients, :ownerId, :attachments, :validate_lookup))

      begin
        ActiveRecord::Base.transaction do
          @message.owner = @owner_user || @current_user
          @message.tenant_id = @tenant_id
          @message.save!
          save_look_ups(@params[:validate_lookup])
          set_phone_numbers
          Attachment::CreateService.new(@message, @params[:attachments]).call if @params[:attachments].present?
          Publishers::MessageReceivedFromEntityPublisher.call(@message) if message_received_from_lead_contact? && !@skip_message_created_events
          Publishers::MessageCreated.call(@message.id) unless @skip_message_created_events
          
          if @message.message_type == WHATSAPP_BUSINESS
            Conversation.find(@message.conversation_id).update_column(:last_activity_at, @message.sent_at)
          end

          @message.id
        end
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error "Error while creating Message : #{@message.errors.to_a.join(',')}"
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{@message.errors.to_a.join(',')}")
      end
    rescue ArgumentError => e
      Rails.logger.error "Error while creating Message: #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{e.message}")
    end
  end

  private

  def message_received_from_lead_contact?
    @message.direction == 'incoming'
  end

  def save_look_ups(validate_lookup)
    return if @params[:recipients].blank? && @params[:message_type] == WHATSAPP_BUSINESS

    raise(ExceptionHandler::RecipientNotPresentError, ErrorCode.recipient_not_present) unless @params[:recipients].present?

    related_entities = get_related_to([true, false].include?(validate_lookup) ? validate_lookup : true)
    @message.related_to = related_entities

    related_entities.each do |r_e|
      if @params[:recipients].find { |rec| rec[:entity] == r_e.entity_type }
        message_recipient = MessageLookUp.find_by(message_id: @message.id, look_up_id: r_e.id)
        message_recipient.recipient = true
        message_recipient.save!
      end
    end
  end

  def set_phone_numbers
    if @message.direction == 'incoming'
      @message.sender_number = @params[:recipients].first[:phone_number] unless @params[:sender_number].present?
      @message.recipient_number = User::GetUserDetails.call(@message.owner_id, @tenant_id).phone_number unless @params[:recipient_number].present?
    else
      @message.recipient_number = @params[:recipients].first[:phone_number] unless @params[:recipient_number].present?
      @message.sender_number = User::GetUserDetails.call(@message.owner_id, @tenant_id).phone_number unless @params[:sender_number].present?
    end
    @message.save!

    unless @message.sender_number.present? && @message.recipient_number.present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||Sender or recipient empty")
    end
  end
end
