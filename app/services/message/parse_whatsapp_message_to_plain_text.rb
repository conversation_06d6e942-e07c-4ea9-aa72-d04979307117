# frozen_string_literal: true

class Message::ParseWhatsappMessageToPlainText
  def initialize(message)
    @message = message
  end

  def call
    return @message unless @message.present?

    italic_regex = /(?<=^| |\\n)_([^ \\n].*?[^ \\n])_(?=\\n|$| )/
    bold_regex = /(?<=^| |\\n)\*([^ \\n].*?[^ \\n])\*(?=\\n|$| )/
    strikethrough_regex = /(?<=^| |\\n)~([^ \\n].*?[^ \\n])~(?=\\n|$| )/
    monospace_regex = /```(.*)```/m
    new_line_regex = /(?<!\\)\n/
    inline_code_regex = /(?<=^| |\\n)`([^ \\n].*?[^ \\n])`(?=\\n|$| )/

    @message = @message.gsub(bold_regex, '\1')
    @message = @message.gsub(italic_regex, '\1')
    @message = @message.gsub(strikethrough_regex, '\1')
    @message = @message.gsub(monospace_regex, '\1')
    @message = @message.gsub(inline_code_regex, '\1')
    @message = @message.gsub(new_line_regex, ' ')
    @message
  end
end
