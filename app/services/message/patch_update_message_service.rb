class Message::PatchUpdateMessageService < ApplicationService
  include RelatedEntityProcessor

  def initialize(params)
    @params = params
    @message = nil
  end

  def call
    @current_user = Thread.current['user']
    @tenant_id = @current_user.tenant_id

    @message = Message.find_by(id: @params[:id], tenant_id: @tenant_id)
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) unless @message

    # check message permission
    unless has_message_permission_on_related_entity_type || (@message.message_type == WHATSAPP_BUSINESS && has_sms_write_permission_on_profile?)
      Rails.logger.error 'Insufficient permission: Update message'
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    begin
      @message.assign_attributes(@params.except(:related_to, :recipients, :attachments))
      ActiveRecord::Base.transaction do
        @message.save!
        # Update recipients
        save_look_ups
        set_phone_numbers
        # Update attachments
        update_attachments
      end
      @message
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error e.message
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end

  private

  def has_message_permission_on_related_entity_type
    entity_type = get_related_entity_type_from_params || get_related_entity_type_from_message
    return unless entity_type

    auth_data = Thread.current['auth']
    permission = auth_data.permissions.find{ |p| p.name == entity_type }
    permission&.action&.sms
  end

  def get_related_entity_type_from_params
    return unless @params[:related_to].present?
    return @params[:related_to].first[:entity]
  end

  def get_related_entity_type_from_message
    return if @message.blank?
    return @message.related_to.first&.entity_type
  end

  def update_attachments
    return unless @params[:attachments].present?

    attachment_ids_to_delete = @params[:attachments].filter_map { |a| a[:id] if a[:_destroy] == true }
    @params[:attachments].reject! { |a| a[:_destroy] == true }

    attachments = @message.attachments.where(id: attachment_ids_to_delete)
    if attachments.present?
      file_names = attachments.map(&:file_name)
      attachments.destroy_all
    end

    if file_names
      S3::DeleteFileFromS3.call(file_names, S3_ATTACHMENT_BUCKET)
      Rails.logger.info "Delete|Message attachments: Files: #{file_names.inspect} is deleted by tenantId #{@tenant_id}, userId #{@current_user.id}"
    end

    Attachment::CreateService.new(@message, @params[:attachments]).call if @params[:attachments].present?
  end

  def save_look_ups
    if @params[:related_to].present?
      related_entities = get_related_to
      related_entities.each do |re|
        related_look_up = @message.related_look_ups.find_or_initialize_by({ look_up_id: re.id })
        @message.related_to << re if related_look_up.new_record?
      end

      @message.related_to.each do |r_e|
        if @params[:recipients].find { |rec| rec[:entity] == r_e.entity_type && rec[:id] == r_e.entity_id }
          message_recipient = MessageLookUp.find_by(message_id: @message.id, look_up_id: r_e.id)
          message_recipient.recipient = true
          message_recipient.save!
        end
      end
    end
    delete_related_to
  end

  def delete_related_to
    if @params[:related_to].present?
      @params[:related_to].delete_if do |rel|
        if rel[:_destroy]
          related_look_up_ids = @message.related_to.where(entity_type: rel[:entity], entity_id: rel[:id]).ids
          @message.related_look_ups.where(look_up_id: related_look_up_ids).destroy_all
          @message.reload
          raise(ExceptionHandler::RemoveRecipientNotAllowedError, ErrorCode.remove_recipient_not_allowed) unless @message.recipients.present?
          true
        else
          false
        end
      end
      # delete from recipients parameters
      @params[:recipients]&.delete_if { |re| re[:_destroy] == true }
    end
  end

  def set_phone_numbers
    if @message.direction == 'incoming'
      @message.sender_number = @message.recipients.first.phone_number if is_sender_number_nil?
      @message.recipient_number = User::GetUserDetails.call(@message.owner_id, @tenant_id).phone_number if is_recipient_number_nil?
    else
      @message.recipient_number = @message.recipients.first.phone_number if is_recipient_number_nil?
      @message.sender_number = User::GetUserDetails.call(@message.owner_id, @tenant_id).phone_number if is_sender_number_nil?
    end
    @message.save!

    unless @message.sender_number.present? && @message.recipient_number.present?
      Rails.logger.info "Couldn't set the sender or recipient phone number"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end

  def is_sender_number_nil?
    @params.key?(:sender_number) && @params[:sender_number].nil?
  end

  def is_recipient_number_nil?
    @params.key?(:recipient_number) && @params[:recipient_number].nil?
  end
end
