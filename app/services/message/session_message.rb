# frozen_string_literal: true

class Message::SessionMessage
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def send_text
    unless WhatsappCredit.has_whatsapp_credits?(@auth_data.tenant_id)
      Rails.logger.info "User does not have sufficient whatsapp credits balance, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance}||#{I18n.t('error.insufficient_whatsapp_credits')}")
    end

    validate_entity 

    unless @params[:message_type] == TEXT.downcase
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_type')}")
    end

    unless @params[:message_body].present? && @params[:message_body].size <= 4096
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_body')}")
    end

    connected_account = validate_connected_account
    validate_agent_user(connected_account)

    entity_data = {}
    if @params[:entity_id].present?
      entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id
    end

    selected_phone_number = get_selected_phone(entity_data)

    if @params[:entity_id].present? && !entity_data.dig('recordActions', 'sms')
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    entity_name = "#{entity_data['firstName']} #{entity_data['lastName']}".strip
    phone_number = "#{selected_phone_number['dialCode']}#{selected_phone_number['value']}"

    phone_number_conversation, phone_number_sub_conversation = fetch_conversation_based_on_permissions(connected_account, phone_number, entity_data['ownerId'])

    unless is_session_active?(phone_number_conversation)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.session_inactive')}")
    end

    update_conversation_owner(phone_number_conversation)

    response = Interakt::Message.new(connected_account).send_session_message(@params[:message_type], { body: @params[:message_body] }, phone_number)

    message_params = {
      content: @params[:message_body],
      direction: 'outgoing',
      medium: 'whatsapp',
      owner_id: @current_user.id,
      sent_at: DateTime.now,
      sender_number: connected_account.waba_number,
      recipient_number: phone_number,
      status: 'sending',
      message_type: WHATSAPP_BUSINESS,
      remote_id: response.body.dig('messages', 0)['id'],
      related_to: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
      recipients: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
      validate_lookup: false,
      connected_account: connected_account,
      conversation_id: phone_number_conversation.id,
      sub_conversation_id: phone_number_sub_conversation.id
    }.with_indifferent_access

    Message::MessageService.new(message_params).call
  end

  def send_media
    unless WhatsappCredit.has_whatsapp_credits?(@auth_data.tenant_id)
      Rails.logger.info "User does not have sufficient whatsapp credits balance, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance}||#{I18n.t('error.insufficient_whatsapp_credits')}")
    end

    validate_entity

    unless @params[:message_type] == 'media'
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_message_type')}")
    end

    @params[:media].map do |media|
      validate_media(media)
    end

    connected_account = validate_connected_account
    validate_agent_user(connected_account)

    entity_data = {}
    if @params[:entity_id].present?
      entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id
    end

    selected_phone_number = get_selected_phone(entity_data)
    phone_number = "#{selected_phone_number['dialCode']}#{selected_phone_number['value']}"
    entity_name = "#{entity_data['firstName']} #{entity_data['lastName']}".strip

    if @params[:entity_id].present? && !entity_data.dig('recordActions', 'sms')
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    phone_number_conversation, phone_number_sub_conversation = fetch_conversation_based_on_permissions(connected_account, phone_number, entity_data['ownerId'])

    unless is_session_active?(phone_number_conversation)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.session_inactive')}")
    end

    update_conversation_owner(phone_number_conversation)

    @params[:media].map do |media|
      message_params = {
        content: media['caption'] || 'Media Message',
        direction: 'outgoing',
        medium: 'whatsapp',
        owner_id: @current_user.id,
        sent_at: DateTime.now,
        sender_number: connected_account.waba_number,
        recipient_number: phone_number,
        status: 'sending',
        message_type: WHATSAPP_BUSINESS,
        related_to: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
        recipients: @params[:entity_id].present? ? [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: entity_data['ownerId'] }] : [],
        validate_lookup: false,
        connected_account: connected_account,
        attachments: [
          {
            data: media[:file],
            skip_delete: true,
            file_name: media[:file].original_filename
          }
        ],
        conversation_id: phone_number_conversation.id,
        sub_conversation_id: phone_number_sub_conversation.id
      }.with_indifferent_access

      message_id = Message::MessageService.new(message_params).call

      begin
        response = send("send_#{media[:type]}_message", media, connected_account, entity_data, phone_number)
        Message.where(id: message_id).update_all(remote_id: response.body.dig('messages', 0)['id'])
      rescue ExceptionHandler::ThirdPartyAPIError
        Message.where(id: message_id).update_all(status: 'failed')
      end

      File.delete(media[:file].path)
      message_id
    end
  end

  private

  def send_audio_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(AUDIO.downcase, { id: upload_response.body['id'] }, phone_number)
  end

  def send_document_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(DOCUMENT.downcase, { id: upload_response.body['id'], caption: media[:caption], filename: media[:file].original_filename }, phone_number)
  end

  def send_video_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(VIDEO.downcase, { id: upload_response.body['id'], caption: media[:caption] }, phone_number)
  end

  def send_image_message(media, connected_account, entity_data, phone_number)
    upload_response = Facebook::Media.new(connected_account).upload(media[:file])
    Interakt::Message.new(connected_account).send_session_message(IMAGE.downcase, { id: upload_response.body['id'], caption: media[:caption] }, phone_number)
  end

  def validate_media(media)
    unless ALLOWED_FILE_TYPES[media[:type]].include?(File.extname(media[:file].original_filename).downcase)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_media_type')}")
    end

    if media[:file].size > MAX_FILE_SIZE[media[:type]]
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_media_size')}")
    end

    validate_caption(media)
  end

  def validate_caption(media)
    if media[:type] == 'audio' && media[:caption].present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.caption_not_allowed')}")
    end
  end

  def validate_connected_account
    # TODO: check onboarded status
    connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, id: @params[:id])
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.invalid_message.connected_account_not_found')}")
    end

    unless connected_account.status == ACTIVE && connected_account.is_verified?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.inactive_or_unverified_account')}")
    end

    connected_account
  end

  def validate_agent_user(connected_account)
    # TODO: get agent user by entity
    unless AgentUser.where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id, connected_account_id: connected_account.id).exists?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_connected_account')}")
    end
  end

  def get_selected_phone(entity_data)
    selected_phone_number = nil

    if @params[:conversation_id].present?
      phone_number_conversation = Conversation.find_by(id: @params[:conversation_id], tenant_id: @auth_data.tenant_id)
      
      unless phone_number_conversation.present?
        Rails.logger.info "Conversation not found for id: #{@params[:conversation_id]}, Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
      end

      if entity_data.blank?
        parsed_phone = Phonelib.parse(phone_number_conversation.phone_number)

        return { 'dialCode' => "+#{parsed_phone.country_code}", 'value' => parsed_phone.raw_national }
      end

      selected_phone_number = entity_data['phoneNumbers']&.find { |phone| "#{phone['dialCode']}#{phone['value']}" == phone_number_conversation.phone_number }
    else
      selected_phone_number = entity_data['phoneNumbers']&.find { |phone| phone['id'] == @params[:phone_id].to_i }
    end

    unless selected_phone_number.present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_phone_number}||#{I18n.t('error.invalid_phone_number_for_conversation')}")
    end

    selected_phone_number
  end

  def validate_entity
    if (@params[:entity_type].present? && ![LOOKUP_LEAD, LOOKUP_CONTACT].include?(@params[:entity_type])) ||
      (@params[:entity_type].present? && !@params[:entity_id].present?)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.invalid_entity')}")
    end

    unless @params[:phone_id].present? || @params[:conversation_id].present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_message.missing_phone_id_or_conversation_id')}")
    end
  end

  def fetch_conversation_based_on_permissions(connected_account, phone_number, entity_owner_id, conversation_id = nil)
    phone_number_conversation = nil

    if conversation_id.present?
      phone_number_conversation = Conversation.find_by(id: conversation_id, tenant_id: @auth_data.tenant_id)
    else
      phone_number_conversation =
      Conversation.find_by(
        tenant_id: @auth_data.tenant_id,
        connected_account_id: connected_account.id,
        phone_number: phone_number
      )
    end

    unless phone_number_conversation.present?
      Rails.logger.info "Conversation not found for the phone number: #{phone_number}, connected account id: #{connected_account.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
    end

    if !entity_owner_id.present?
      unless @current_user.can_send_conversation_message_unrelated_to_entity?(phone_number_conversation.owner_id)
        Rails.logger.info "User does not have conversation permission on conversation_id: #{phone_number_conversation.id}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
      end
    else
      unless @current_user.can_send_conversation_message?(@params[:entity_id], @params[:entity_type], entity_owner_id)
        Rails.logger.info "User does not have conversation permission on #{@params[:entity_type]}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
      end
    end

    [phone_number_conversation, phone_number_conversation.sub_conversations.last]
  end

  def update_conversation_owner(conversation)
    if conversation.owner_id != @current_user.id
      conversation.update!(owner_id: @current_user.id)
    end
  end

  def is_session_active?(conversation)
    conversation.last_message_received_at.present? && conversation.last_message_received_at >= 24.hours.ago
  end
end
