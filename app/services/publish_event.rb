class PublishEvent < ApplicationService
  include ActiveModel::Validations

  def initialize(event, exchange = MESSAGE_EXCHANGE, routing_key = 'message')
    @event = event
    @exchange = exchange
    @routing_key = routing_key
  end

  def call
    exchange = RabbitmqConnection.get_exchange(@exchange, @routing_key)
    exchange.publish(
      @event.to_json,
      routing_key: @event.routing_key
    )
  end
end
