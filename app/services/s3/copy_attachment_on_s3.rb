class S3::CopyAttachmentOnS3 < ApplicationService
  def initialize(src_path, dest_path)
    @src_path = src_path
    @dest_path = dest_path
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    bucket_name = S3_ATTACHMENT_BUCKET
    @bucket = s3.bucket(bucket_name)
  end

  def call
    begin
      obj = @bucket.object(@src_path)
      obj.copy_to(bucket: S3_ATTACHMENT_BUCKET ,key: @dest_path)
    rescue StandardError => e
      Rails.logger.error "Email Service | Copy file to s3: #{e.message} | #{@src_path}"
    end
  end
end
