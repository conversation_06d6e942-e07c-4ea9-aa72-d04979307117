class S3::DeleteFileFromS3 < ApplicationService
  def initialize(files_path, bucket)
    @files_path = files_path
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(bucket)
  end

  def call
    begin
      objects_to_delete = build_objects_to_delete
      Rails.logger.info "Attachments to delete: #{objects_to_delete.inspect}"
      @bucket.delete_objects({ delete: { objects: objects_to_delete } }) if objects_to_delete.present?
    rescue StandardError => e
      Rails.logger.error "Message Service | Delete files from s3: #{e.message} | #{@name}"
    end
  end

  private

  def build_objects_to_delete
    return unless @files_path.present?
    return @files_path.map{|path| { key: path }}
  end
end
