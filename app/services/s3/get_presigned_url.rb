class S3::GetPresignedUrl
  def initialize(path, file_name, bucket, content_type = nil)
    
    @file_name = file_name
    @path = path
    @content_type = content_type
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(bucket)
  end

  def call
    return unless @file_name
    begin
      obj = @bucket.object(@path)

      presigned_url_params = {
        :response_content_disposition => "attachment; filename=#{@file_name}"
      }

      presigned_url_params[:response_content_type] = @content_type if @content_type

      url = obj.presigned_url(:get, expires_in: 300, **presigned_url_params)
      { url: url,  file_name: @file_name }
    rescue StandardError => e
      Rails.logger.error "Message Service | Failed to get signed URL of file from s3: #{e.message} | #{@file_name}"
    end
  end
end

