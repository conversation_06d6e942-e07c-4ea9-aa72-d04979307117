class S3::UploadFile
  def initialize(file_path, name, bucket, skip_delete = false)
    @file_path = file_path
    @name = name
    Aws.config.update(
      endpoint: ENV['AWS_ENDPOINT'],
      region: ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(ENV['AWS_ACCESS_KEY_ID'], ENV['AWS_SECRET_ACCESS_KEY'])
    )
    s3 = Aws::S3::Resource.new
    @bucket = s3.bucket(bucket)
    @skip_delete = skip_delete
  end

  def call
    begin
      Rails.logger.info "S3:UploadFile | file uploading started, Time: #{Time.now}, File path: #{@file_path}"
      obj = @bucket.object(@name)
      obj.upload_file(@file_path)
      File.delete(@file_path) unless @skip_delete
      Rails.logger.info "S3:UploadFile | file uploading completed, Time: #{Time.now}, File path: #{@file_path}"
    rescue StandardError => e
      Rails.logger.error "Message Service | Upload file to s3: #{e.message} | #{@name}"
    end
  end
end
