require 'rest-client'
class SearchContacts

  def initialize(phone_numbers, tenant_id, token = nil)
    @phone_numbers = phone_numbers
    @tenant_id = tenant_id
    @token = token
  end

  def call
    return [] if @phone_numbers.blank?
    @phone_numbers = get_parsed_phone_numbers
    EntitySearchResultParser.new(@phone_numbers, search_contacts, LOOKUP_CONTACT, @tenant_id).call
  end

  private

  def get_parsed_phone_numbers
    @phone_numbers.map {|pn| Phonelib.parse(pn) rescue nil }.reject(&:nil?)
  end

  def search_contacts
    unless @token
      @token = Thread.current[:token]
    end

    payload = { fields: ["id", "name", "phoneNumbers", "ownerId"] }
    rules = []
    @phone_numbers.each do |phone_number|
      rules << {
        "id": "multi_field",
        "field": "multi_field",
        "type": "multi_field",
        "input": "multi_field",
        "operator": "multi_field",
        "value": phone_number.raw_national
      }
    end

    payload[:jsonRule] = { rules: rules, "condition": "OR", "valid": true }
    Rails.logger.info "Payload to contacts search: #{payload.inspect}"

    begin
      response = RestClient.post(
        SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          :Authorization => "Bearer #{@token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchContacts invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::NotFound
      Rails.logger.error "SearchContacts 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchContacts 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchContacts 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end
end
