require 'rest-client'
class SearchLeads

  def initialize(phone_numbers, tenant_id, token = nil)
    @phone_numbers = phone_numbers
    @tenant_id = tenant_id
    @token = token
  end

  def call
    return [] if @phone_numbers.blank?
    @phone_numbers = get_parsed_phone_numbers
    EntitySearchResultParser.new(@phone_numbers, search_leads, LOOKUP_LEAD, @tenant_id).call
  end

  private

  def get_parsed_phone_numbers
    @phone_numbers.map {|pn| Phonelib.parse(pn.strip) rescue nil }.reject(&:nil?)
  end

  def search_leads
    unless @token
      @token = Thread.current[:token]
    end

    payload = { fields: ["id", "name", "phoneNumbers", "ownerId"] }
    rules = []
    @phone_numbers.each do |phone_number|
      rules << {
        "id": "multi_field",
        "field": "multi_field",
        "type": "multi_field",
        "input": "multi_field",
        "operator": "multi_field",
        "value": phone_number.raw_national
      }
    end

    payload[:jsonRule] = { rules: rules, "condition": "OR", "valid": true }
    Rails.logger.info "Payload to leads search: #{payload.inspect}"

    begin
      response = RestClient.post(
        SERVICE_SEARCH + "/v1/search/lead?sort=updatedAt,desc&page=0&size=100",
        payload.to_json,
        {
          :Authorization => "Bearer #{@token}",
          content_type: :json,
          accept: :json
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "SearchLeads invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::NotFound
      Rails.logger.error "SearchLeads 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "SearchLeads 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest
      Rails.logger.error "SearchLeads 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end
