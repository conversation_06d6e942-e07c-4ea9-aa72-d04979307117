# frozen_string_literal: true

class ShareRules
  def initialize(params)
    @params = params
  end

  def manage_share_rules
    begin
      ActiveRecord::Base.transaction do
        validate_user_and_team

        share_rule = ShareRule.find_or_initialize_by(
          share_rule_id: @params.dig('entity', 'id'),
          tenant_id: @params.dig('metadata', 'tenantId')
        )

        if @params.dig('metadata', 'entityAction') == 'UPDATED' && !@params.dig('entity', 'actions', 'sms')
          Rails.logger.info "Message permission is not there in the given share rule id #{@params.dig('metadata', 'entityId')}, Tenant id: #{@params.dig('metadata', 'tenantId')}"

          share_rule.destroy!
          return
        end

        share_rule.assign_attributes(share_rule_params)
        share_rule.save!
      end
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Error: #{e.message}, Share rule id: #{@params.dig('entity', 'id')}, Tenant id: #{@params.dig('metadata', 'tenantId')}"
      raise(
        ExceptionHandler::InvalidDataError,
        "#{ErrorCode.invalid_share_rule}||#{I18n.t('error.invalid_share_rule', error: e.message.gsub(VALIDATION_FAILED_ERROR, ''))}"
      )
    end
  end

  def delete
    share_rule = ShareRule.find_by(
      share_rule_id: @params.dig('metadata', 'entityId'),
      tenant_id: @params.dig('metadata', 'tenantId')
    )

    return unless share_rule.present?

    begin
      share_rule.destroy!
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Error: #{e.message}, Share rule id: #{@params.dig('metadata', 'entityId')}, Tenant id: #{@params.dig('metadata', 'tenantId')}"
      raise(
        ExceptionHandler::InvalidDataError,
        "#{ErrorCode.invalid_share_rule}||#{I18n.t('error.invalid_share_rule', error: e.message.gsub(VALIDATION_FAILED_ERROR, ''))}"
      )
    end
  end

  private

  def validate_user_and_team
    User::GetUserDetails.call(@params.dig('entity', 'from', 'id'), @params.dig('metadata', 'tenantId'))

    if @params.dig('entity', 'to', 'type') == USER
      User::GetUserDetails.call(@params.dig('entity', 'to', 'id'), @params.dig('metadata', 'tenantId'))
    else
      Team::GetDetails.call(@params.dig('entity', 'createdBy', 'id'), @params.dig('entity', 'to', 'id'), @params.dig('metadata', 'tenantId'))
    end
  end

  def share_rule_params
    share_rule_data = @params['entity']

    {
      name: share_rule_data['name'],
      description: share_rule_data['description'],
      entity_id: share_rule_data['entityId'],
      entity_type: share_rule_data['entityType'],
      from_id: share_rule_data.dig('from', 'id'),
      from_type: share_rule_data.dig('from', 'type'),
      to_id: share_rule_data.dig('to', 'id'),
      to_type: share_rule_data.dig('to', 'type'),
      share_all_records: share_rule_data['shareAllRecords'],
      actions: share_rule_data['actions'],
      created_by_id: share_rule_data.dig('createdBy', 'id'),
      updated_by_id: share_rule_data.dig('updatedBy', 'id'),
    }
  end
end
