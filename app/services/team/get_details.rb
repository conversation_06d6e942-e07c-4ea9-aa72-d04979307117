# frozen_string_literal: true

class Team::GetDetails < ApplicationService
  def initialize(user_id, team_id, tenant_id)
    @user_id = user_id
    @team_id = team_id
    @tenant_id = tenant_id
  end

  def call
    team = Team.find_or_initialize_by(
      id: @team_id,
      tenant_id: @tenant_id
    )

    if team.new_record?
      team = Team::ValidateTeams.new([team], @user_id, @tenant_id).call.first
    end

    if team.save
      team
    else
      raise(ExceptionHandler::AuthenticationError, ErrorCode.not_found("team: #{@team_id}"))
    end
  end
end
