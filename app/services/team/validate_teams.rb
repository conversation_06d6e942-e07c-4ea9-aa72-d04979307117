# frozen_string_literal: true

require 'rest-client'

class Team::ValidateTeams < ApplicationService
  def initialize(teams, user_id, tenant_id)
    @teams = teams
    @user_id = user_id
    @tenant_id = tenant_id
  end

  def call
    token = GenerateToken.call(@user_id, @tenant_id, permissions)

    teams_response = fetch_teams(token)

    @teams.each do |team|
      verified_team = teams_response.find { |resp| resp['id'] == team.id }
      if verified_team.present?
        team.name = verified_team['name']
        team.user_ids = fetch_users(team.id, token)
      else
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.invalid_team')}")
      end
    end

    @teams
  end

  private

  def fetch_teams(token)
    response = RestClient.post(
      "#{SERVICE_IAM}/v1/teams/search",
      payload.to_json,
      {
        Authorization: "Bearer #{token}",
        content_type: :json
      }
    )

    return JSON.parse(response.body)['content'] unless response.nil?
    Rails.logger.error "Get teams -> invalid response"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue RestClient::InternalServerError
    Rails.logger.error "Get teams -> 500"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  rescue RestClient::BadRequest
    Rails.logger.error "Get teams -> 400"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue StandardError => e
    Rails.logger.error "Get teams -> 400 - StandardError -> #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  end

  def payload
    {
      fields: [
        'name',
        'id'
      ],
      jsonRule: {
        rules: [
          {
            operator: 'in',
            id: 'id',
            field: 'id',
            type: 'long',
            value: @teams.map(&:id).join(',')
          }
        ],
        condition: 'AND',
        valid: true
      }
    }
  end

  def permissions
    [
      {
        "name": "team",
        "description": "has access to team resource",
        "limits": -1,
        "units": "count",
        "action": {
          "read": true,
          "readAll": true
        }
      },
      {
        "name": "user",
        "description": "has access to user resource",
        "limits": -1,
        "units": "count",
        "action": {
          "read": true,
          "readAll": true
        }
      }
    ]
  end

  def fetch_users(team_id, token)
    response = RestClient.get(
      SERVICE_IAM + "/v1/teams/#{team_id}/users",
      {
        Authorization: "Bearer #{token}",
        content_type: :json
      }
    )

    return JSON.parse(response.body)['content']&.map { |user| user['id'] } unless response.nil?
    Rails.logger.error "Fetch team users -> invalid response"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue RestClient::InternalServerError
    Rails.logger.error "Fetch team users -> 500"
    raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
  rescue RestClient::BadRequest
    Rails.logger.error "Fetch team users -> 400"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  rescue StandardError => e
    Rails.logger.error "Fetch team users -> 400 - StandardError -> #{e.message}"
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
  end
end
