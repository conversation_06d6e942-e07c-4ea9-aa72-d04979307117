# frozen_string_literal: true

class TemplateMediaService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def upload
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data) unless @params[:media_file].present?

    unless @current_user.can_create_templates?
      Rails.logger.error "User doesn't have permission to create whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    connected_account = validate_connected_account_and_agent

    file_type = File.extname(@params[:media_file].original_filename)
    file_name = @params[:media_file].original_filename

    if TEMPLATE_MEDIA_FILE_EXTENSIONS.exclude?(file_type.downcase)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_template_file_type}||#{I18n.t('error.template_media.invalid_file_type')}")
    end

    file_name = "tenant_#{@current_user.tenant_id}/connected_account_#{connected_account.id}/#{file_name.gsub(file_type, '')}_#{SecureRandom.uuid}#{file_type}"

    S3::UploadFile.new(@params[:media_file].path, file_name, S3_ATTACHMENT_BUCKET).call
    template_media = TemplateMedia.create(
      file_name: file_name,
      file_size: @params[:media_file].size,
      tenant_id: @current_user.tenant_id,
      file_type: get_mime_type(file_type)
    )

    template_media
  end

  def get
    validate_connected_account_and_agent

    template_media = TemplateMedia.find_by(tenant_id: @auth_data.tenant_id, id: @params[:id])

    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Template Media')}") if template_media.blank?

    template_media
  end

  def delete_unused_media
    media_to_delete = TemplateMedia.where(whatsapp_template_component_id: nil)
    file_names = media_to_delete.map { |media| media.file_name }
    S3::DeleteFileFromS3.new(file_names, S3_ATTACHMENT_BUCKET).call
    media_to_delete.delete_all
  end

  def upload_media_to_facebook
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}") if @params[:template_media].nil? || @params[:connected_account].nil?

    file_name = @params[:template_media].file_name.split('/', 3)[-1]
    S3::DownloadFileFromS3.new(@params[:template_media].file_name).call
    mime_type = get_mime_type(".#{@params[:template_media].file_type.split('/')[-1]}")
    upload_session_response = Facebook::UploadSession.new(
      {
        file_name: file_name,
        file_size: @params[:template_media].file_size,
        file_type: mime_type
      },
      @params[:connected_account]
    ).start
    upload_media_response = Facebook::WhatsappTemplateMedia.new(file_name, mime_type, upload_session_response.body['id'], @params[:connected_account]).start_upload

    @params[:template_media].update(whatsapp_file_handle: upload_media_response.body['h'])
  end

  private

  def validate_connected_account_and_agent
    connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, id: @params[:connected_account_id])

    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    unless AgentUser.where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id, connected_account_id: @params[:connected_account_id]).exists?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_connected_account')}")
    end

    connected_account
  end

  def get_mime_type(file_type)
    case file_type.downcase
    when '.pdf'
      'application/pdf'
    when '.jpeg', '.jpg'
      'image/jpeg'
    when '.png'
      'image/png'
    when '.mp4'
      'video/mp4'
    end
  end
end
