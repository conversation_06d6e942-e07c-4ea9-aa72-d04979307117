class User::Authenticate

  def self.authenticate(headers)
    token = http_auth_header(headers)
    auth_data = User::TokenParser.parse(token)

    user = User.find_or_initialize_by(id: auth_data.user_id, tenant_id: auth_data.tenant_id)
    thread = Thread.current
    thread[:token] = token
    if user.new_record?
      user = User::Validator.validate(user)
      p user
      raise(ExceptionHandler::AuthenticationError, ErrorCode.invalid_token) unless user.save
    end
    thread[:user] = user
    thread[:auth] = auth_data
    auth_data
  end

  def self.http_auth_header(headers)
    return headers['Authorization'].split(' ').last if headers['Authorization'].present?

    raise(ExceptionHandler::AuthenticationError, ErrorCode.invalid_token)
  end

  private_class_method :http_auth_header
end
