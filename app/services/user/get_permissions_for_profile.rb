require 'rest-client'

class User::GetPermissionsForProfile < ApplicationService
  def initialize(profile_id)
    @profile_id = profile_id
  end

  def call
    token = Thread.current[:token]
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless token

    begin
      Rails.logger.info "Profile Url::: #{SERVICE_IAM}/v1/profiles/#{@profile_id}/permissions"
      response = RestClient.get(
        SERVICE_IAM + "/v1/profiles/#{@profile_id}/permissions",
        {
          'Authorization': "Bearer #{token}"
        }
      )
      return JSON(response.body) unless response.nil?
      Rails.logger.error "Get Profile Permissions - invalid response"
      raise(ExceptionHandler::InvalidProfileError, ErrorCode.invalid_profile)
    rescue RestClient::NotFound => err
      Rails.logger.error "Get Profile Permissions - 404:: #{err.response&.body&.inspect}"
      raise(ExceptionHandler::InvalidProfileError, ErrorCode.invalid_profile)
    rescue RestClient::Unauthorized => err
      Rails.logger.error "Get Profile unauthorized - 401:: #{err.response&.body&.inspect}"
      raise(ExceptionHandler::ProfileAccessNotAllowedError, ErrorCode.profile_access_not_allowed)
    rescue RestClient::InternalServerError => err
      Rails.logger.error "Get Profile Permissions - 500:: #{err.response&.body&.inspect}"
      Rails.logger.info "Internal server error ==> #{err.response}"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue RestClient::BadRequest => err
      Rails.logger.error "Get Profile Permissions - 400:: #{err.response&.body&.inspect}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end