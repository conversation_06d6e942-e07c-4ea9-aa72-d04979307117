class User::GetUserDetails < ApplicationService

  def initialize(user_id, tenant_id, validate_user = true, params = {})
    @user_id = user_id
    @tenant_id = tenant_id
    @validate_user = validate_user
    @params = params.with_indifferent_access
  end

  def call
    user = User.
      find_or_initialize_by(
        id: @user_id,
        tenant_id: @tenant_id
      )
    if user.new_record? && @validate_user
      user_lookup = User::Validator.validate(user)
      user.name = user_lookup.name
      user.phone_number = user_lookup.phone_number
      user.timezone = user_lookup.timezone
      user.date_format = user_lookup.date_format
    end

    unless @validate_user
      user.name = @params[:name]
      user.phone_number = @params[:phone_number]
      user.timezone = @params[:timezone]
      user.date_format = @params[:dateFormat]
    end

    raise(ExceptionHandler::AuthenticationError, ErrorCode.not_found("user: #{@user_id}")) unless user.save
    user
  end
end
