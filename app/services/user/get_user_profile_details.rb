# frozen_string_literal: true

class User::GetUserProfileDetails < ApplicationService
  def initialize(user_id, tenant_id, token = nil)
    @user_id = user_id
    @tenant_id = tenant_id
    @token = token
  end

  def call
    user = User.find_or_initialize_by(id: @user_id, tenant_id: @tenant_id)
    if user.new_record? || !user.phone_number.present?
      user_profile = User::GetUserProfile.call(nil, @token)&.with_indifferent_access
      Rails.logger.info "User profile: #{user_profile.inspect}"

      user.name = "#{user_profile[:firstName]} #{user_profile[:lastName]}"
      phone_number = user_profile[:phoneNumbers].find { |pn| pn[:primary] == true }
      user.phone_number = "#{phone_number[:dialCode]}#{phone_number[:value]}"
    end

    raise(ExceptionHandler::AuthenticationError, ErrorCode.not_found("user: #{@user_id}")) unless user.save

    Rails.logger.info "User built using profile: #{user.inspect}"
    user
  end
end
