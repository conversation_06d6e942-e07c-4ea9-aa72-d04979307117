# frozen_string_literal: true

require 'rest-client'

class User::Summary
  def initialize(user_ids, token = nil)
    @user_ids = user_ids
    @token = token
  end

  def get
    return [] if @user_ids.blank?

    unless @token.present?
      Rails.logger.error "User::Summary Invalid token"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.invalid_token)
    end

    summary_response = fetch_user_summary
    summary_response.map do |user_response|
      hash = user_response.slice('id', 'name').with_indifferent_access
      primary_phone_number = user_response['phoneNumbers']&.find { |ph_obj| ph_obj['primary'] }
      if primary_phone_number.present?
        hash.merge!(phone_number: "#{primary_phone_number['dialCode']}#{primary_phone_number['value']}")
      end

      hash
    end
  end

  private

  def fetch_user_summary
    begin
      response = RestClient.get(
        "#{SERVICE_IAM}/v1/users/summary?id=#{@user_ids.join(',')}",
        {
          Authorization: "Bearer #{@token}"
        }
      )

      return JSON(response.body) unless response.nil? || response.body == ''

      Rails.logger.error "User::Summary invalid response"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::BadRequest
      Rails.logger.error "User::Summary iam 400"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::Unauthorized
      Rails.logger.error "User::Summary iam 401"
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    rescue RestClient::NotFound
      Rails.logger.error "User::Summary iam 404"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    rescue RestClient::InternalServerError
      Rails.logger.error "User::Summary iam 500"
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    rescue StandardError
      Rails.logger.error "User::Summary iam 500"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
  end
end
