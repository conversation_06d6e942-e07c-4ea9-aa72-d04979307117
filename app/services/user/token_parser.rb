class User::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.parse(token)
    begin
      body = JWT.decode(token, nil, false)
      decoded_token = HashWithIndifferentAccess.new body[0]
      raise ExceptionHandler::AuthenticationError, ErrorCode.invalid_token unless decoded_token && decoded_token[:data]

      data = decoded_token['data']
      auth_data = Auth::Data.new(data)
      raise ExceptionHandler::AuthenticationError, ErrorCode.invalid_token unless auth_data.valid?

      auth_data
    rescue JWT::DecodeError => e
      Rails.logger.error "Error while parsing JWT token : #{e.message}"
      raise ExceptionHandler::AuthenticationError, ErrorCode.invalid_token
    end
  end
end
