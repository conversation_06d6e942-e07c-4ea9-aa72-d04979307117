require 'rest-client'

class User::<PERSON>ida<PERSON>

  def self.validate(user)
    token = Thread.current['token']

    token = GenerateToken.call(user.id, user.tenant_id, permissions) unless token.present?

    return nil unless user && token

    begin
      response = RestClient.get(SERVICE_IAM + "/v1/users/#{user.id}", { 'Authorization': "Bearer #{token}" }
      )
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_user_details) if response.nil?

      user_data = JSON(response.body)
      user.name = [user_data['firstName'],user_data['lastName']].join(' ')
      primary_phone_number = user_data['phoneNumbers'].find { |pn| pn['primary'] == true }
      user.phone_number = "#{primary_phone_number['dialCode']}#{primary_phone_number['value']}"
      user.timezone = user_data['timezone']
      user.date_format = user_data['dateFormat']
      user
    rescue RestClient::Unauthorized
      Rails.logger.error 'Unauthorised user'
      raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
    rescue RestClient::NotFound, RestClient::BadRequest
      Rails.logger.error 'No user found with given access token'
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_user_details)
    rescue RestClient::InternalServerError
      Rails.logger.error 'Error while fetching user details'
      raise(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
    end
  end

  def self.permissions
    [
      {
        "name": "user",
        "description": "has access to user resource",
        "limits": -1,
        "units": "count",
        "action": {
          "read": true,
          "readAll": true
        }
      }
    ]
  end
end
