# frozen_string_literal: true

class ValidateAndBuildQueryFromJsonRule
  def initialize(scope, rules = [])
    @rules = rules
    @scope = scope
    @scope_class = scope.model
    @type_wise_operators = YAML.safe_load(File.read("#{Rails.root}/config/typeWiseOperators.yml"))
  end

  def build_query
    validate_and_modify_rules
    @rules.each { |rule| build_query_for(rule) }
    @scope
  end

  private

  def validate_and_modify_rules
    invalid_rule = @rules.find do |rule|
      (
        "#{@scope_class.to_s.underscore.upcase}_FILTERABLE_FIELDS".constantize.exclude?(rule[:field]) ||
        !@type_wise_operators[rule[:type]] ||
        @type_wise_operators[rule[:type]].exclude?(rule[:operator])
      )
    end

    if invalid_rule
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid filter params')}")
    end

    @rules.each do |rule|
      case rule[:field]
      when 'createdAt', 'updatedAt'
        rule[:field] = rule[:field].underscore
      when 'createdBy', 'updatedBy'
        rule[:field] = "#{rule[:field].underscore}_id"
      when 'category', 'entityType', 'language'
        rule[:field] = rule[:field].underscore
        rule[:type] = 'string'
      when 'connectedAccount'
        rule[:field] = 'connected_account_id'
      when 'multi_field'
        rule[:field] = 'name'
        rule[:type] = 'string'
        rule[:operator] = 'contains'
      when 'startTime'
        rule[:field] = rule[:field].underscore
        if rule[:value].present?
          rule[:value] =
            if rule[:value].is_a?(Array)
              rule[:value].map { |value| DateTime.parse(value).to_i rescue nil }
            else
              DateTime.parse(rule[:value]).to_i rescue nil
            end
        end
      end

      if rule[:type] == 'double'
        rule[:type] = 'long'
      end

      case rule[:operator]
      when 'is_empty'
        rule[:operator] = 'is_null'
      when 'is_not_empty'
        rule[:operator] = 'is_not_null'
      when 'before_current_date_and_time'
        rule[:value] = get_date_value_for_relative_operators(rule[:timeZone], rule[:operator], rule[:field])
        rule[:operator] = 'less'
      when 'after_current_date_and_time'
        rule[:value] = get_date_value_for_relative_operators(rule[:timeZone], rule[:operator], rule[:field])
        rule[:operator] = 'greater'
      when *RELATIVE_FILTERS_LIST
        rule[:value] = get_date_value_for_relative_operators(rule[:timeZone], rule[:operator], rule[:field])
        rule[:operator] = 'between'
      end
    end
  end

  def build_query_for(rule)
    send("has_#{rule[:type]}_#{rule[:operator]}", rule[:field], rule[:value])
  end

  def has_string_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.matches(escaped(value), false))
  end

  def has_string_not_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where.not(field.matches("#{escaped(value)}", false))
  end

  def has_string_contains(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.matches("%#{escaped(value)}%"))
  end

  def has_string_not_contains(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where.not(field.matches("%#{escaped(value)}%"))
  end

  def has_string_begins_with(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.matches("#{escaped(value)}%"))
  end

  def has_string_is_null(field, value)
    @scope = @scope.where({ field => ["", nil] })
  end

  def has_string_is_not_null(field, value)
    @scope = @scope.where.not({ field => ["", nil] })
  end

  def has_string_in(field, value)
    values = value.split(',').flatten.map { |val| escaped(val) }
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.matches_any(values))
  end

  def has_string_not_in(field, value)
    values = value.split(',').flatten.map { |val| escaped(val) }
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where.not(field.matches_any(values))
  end

  def has_date_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.eq(value))
  end

  def has_date_not_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where.not(field.eq(value))
  end

  def has_date_greater(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.gt(value))
  end

  def has_date_less(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.lt(value))
  end

  def has_date_less_or_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.lteq(value))
  end

  def has_date_greater_or_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value))
  end

  def has_date_between(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.between(value.first..value.last))
  end

  def has_date_not_between(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.not_between(value.first..value.last))
  end

  def has_date_is_null(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.eq(nil))
  end

  def has_date_is_not_null(field, value)
    @scope = @scope.where.not({field => nil})
  end

  def has_long_equal(field, value)
    @scope = @scope.where({field => value})
  end

  def has_long_not_equal(field, value)
    @scope = @scope.where.not({field => value})
  end

  def has_long_is_null(field, value)
    @scope = @scope.where({field => nil})
  end

  def has_long_is_not_null(field, value)
    @scope = @scope.where.not({field => nil})
  end

  def has_long_greater(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.gt(value))
  end

  def has_long_greater_or_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value))
  end

  def has_long_less(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.lt(value))
  end

  def has_long_less_or_equal(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.lteq(value))
  end

  def has_long_in(field, value)
    values = value.split(',').flatten
    @scope = @scope.where({field => values})
  end

  def has_long_not_in(field, value)
    values = value.split(',').flatten
    @scope = @scope.where.not({field => values})
  end

  def has_long_between(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where(field.gteq(value.first).and(field.lteq(value.last)))
  end

  def has_long_not_between(field, value)
    field = @scope_class.arel_table[field.to_sym]
    @scope = @scope.where.not(field.gteq(value.first).and(field.lteq(value.last)))
  end

  def escaped(value)
    value.gsub('_', '\\_').gsub('%', '\\%')
  end

  def get_date_value_for_relative_operators(timezone, operator, field = nil)
    format_date_function = field == 'start_time' ? 'to_i' : 'utc'

    case operator
    when 'today'
      [
        Time.now.in_time_zone(timezone).beginning_of_day.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_day.send(format_date_function)
      ]
    when 'tomorrow'
      [
        Time.now.in_time_zone(timezone).tomorrow.beginning_of_day.send(format_date_function),
        Time.now.in_time_zone(timezone).tomorrow.end_of_day.send(format_date_function)
      ]
    when 'yesterday'
      [
        Time.now.in_time_zone(timezone).yesterday.beginning_of_day.send(format_date_function),
        Time.now.in_time_zone(timezone).yesterday.end_of_day.send(format_date_function)
      ]

    when 'last_seven_days'
      [
        (Time.now.in_time_zone(timezone) - 7.days).beginning_of_day.send(format_date_function),
        (Time.now.in_time_zone(timezone) - 1.days).end_of_day.send(format_date_function)
      ]

    when 'next_seven_days'
      [
        (Time.now.in_time_zone(timezone) + 1.days).beginning_of_day.send(format_date_function),
        (Time.now.in_time_zone(timezone) + 7.days).end_of_day.send(format_date_function)
      ]

    when 'last_fifteen_days'
      [
        (Time.now.in_time_zone(timezone) - 15.days).beginning_of_day.send(format_date_function),
        (Time.now.in_time_zone(timezone) - 1.days).end_of_day.send(format_date_function)
      ]

    when 'next_fifteen_days'
      [
        (Time.now.in_time_zone(timezone) + 1.days).beginning_of_day.send(format_date_function),
        (Time.now.in_time_zone(timezone) + 15.days).end_of_day.send(format_date_function)
      ]

    when 'last_thirty_days'
      [
        (Time.now.in_time_zone(timezone) - 30.days).beginning_of_day.send(format_date_function),
        (Time.now.in_time_zone(timezone) - 1.days).end_of_day.send(format_date_function)
      ]

    when 'next_thirty_days'
      [
        (Time.now.in_time_zone(timezone) + 1.days).beginning_of_day.send(format_date_function),
        (Time.now.in_time_zone(timezone) + 30.days).end_of_day.send(format_date_function)
      ]

    when 'current_week'
      [
        Time.now.in_time_zone(timezone).beginning_of_week.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_week.send(format_date_function)
      ]

    when 'last_week'
      [
        Time.now.in_time_zone(timezone).last_week.beginning_of_week.send(format_date_function),
        Time.now.in_time_zone(timezone).last_week.end_of_week.send(format_date_function)
      ]

    when 'next_week'
      [
        Time.now.in_time_zone(timezone).next_week.beginning_of_week.send(format_date_function),
        Time.now.in_time_zone(timezone).next_week.end_of_week.send(format_date_function)
      ]

    when 'current_month'
      [
        Time.now.in_time_zone(timezone).beginning_of_month.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_month.send(format_date_function)
      ]

    when 'last_month'
      [
        Time.now.in_time_zone(timezone).last_month.beginning_of_month.send(format_date_function),
        Time.now.in_time_zone(timezone).last_month.end_of_month.send(format_date_function)
      ]

    when 'next_month'
      [
        Time.now.in_time_zone(timezone).next_month.beginning_of_month.send(format_date_function),
        Time.now.in_time_zone(timezone).next_month.end_of_month.send(format_date_function)
      ]

    when 'current_quarter'
      [
        Time.now.in_time_zone(timezone).beginning_of_quarter.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_quarter.send(format_date_function)
      ]

    when 'last_quarter'
      [
        Time.now.in_time_zone(timezone).last_quarter.beginning_of_quarter.send(format_date_function),
        Time.now.in_time_zone(timezone).last_quarter.end_of_quarter.send(format_date_function)
      ]

    when 'next_quarter'
      [
        Time.now.in_time_zone(timezone).next_quarter.beginning_of_quarter.send(format_date_function),
        Time.now.in_time_zone(timezone).next_quarter.end_of_quarter.send(format_date_function)
      ]

    when 'current_year'
      [
        Time.now.in_time_zone(timezone).beginning_of_year.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_year.send(format_date_function)
      ]

    when 'last_year'
      [
        Time.now.in_time_zone(timezone).last_year.beginning_of_year.send(format_date_function),
        Time.now.in_time_zone(timezone).last_year.end_of_year.send(format_date_function)
      ]

    when 'next_year'
      [
        Time.now.in_time_zone(timezone).next_year.beginning_of_year.send(format_date_function),
        Time.now.in_time_zone(timezone).next_year.end_of_year.send(format_date_function)
      ]

    when 'week_to_date'
      [
        Time.now.in_time_zone(timezone).beginning_of_week.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_day.send(format_date_function)
      ]

    when 'month_to_date'
      [
        Time.now.in_time_zone(timezone).beginning_of_month.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_day.send(format_date_function)
      ]

    when 'quarter_to_date'
      [
        Time.now.in_time_zone(timezone).beginning_of_quarter.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_day.send(format_date_function)
      ]

    when 'year_to_date'
      [
        Time.now.in_time_zone(timezone).beginning_of_year.send(format_date_function),
        Time.now.in_time_zone(timezone).end_of_day.send(format_date_function)
      ]

    when 'before_current_date_and_time'
      field == 'start_time' ? Time.zone.now.to_i : format_date_time(Time.zone.now)

    when 'after_current_date_and_time'
      field == 'start_time' ? Time.zone.now.to_i : format_date_time(Time.zone.now)
    end
  end

  def format_date_time(value)
    value.iso8601(6)
  end
end
