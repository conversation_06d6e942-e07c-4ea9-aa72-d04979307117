require 'rest-client'
class ValidateContact < ApplicationService

  def initialize(contact, token = nil)
    @contact = contact
    @token = token
  end

  def call
    return [] unless @contact.present?

    verified_contact = GetContact.call(@contact.entity_id, @token)
    if verified_contact
      if (phone_numbers = verified_contact['phoneNumbers'])
        phone_number = phone_numbers.find{ |pn| @contact.phone_number.include?(pn['value'].strip)}
        if phone_number
          @contact.name = "#{verified_contact['firstName']} #{verified_contact['lastName']}"
          @contact.owner_id = verified_contact['ownerId']
        else
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        end
      end
    else
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
    end
    return @contact
  end
end
