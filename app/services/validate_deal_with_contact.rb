require 'rest-client'
class ValidateDealWithContact < ApplicationService
  def initialize(deal, contact, token = nil)
    @deal = deal
    @contact = contact
    @token = token
  end

  def call
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid) unless @contact.present?

    unless @deal
      contact = ValidateContact.call(@contact, @token)
      return @deal, contact
    end

    content = GetContactsForDeal.call(@deal.entity_id, @token)
    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data) unless content.present?

    matched_contact = false
    content.each do |contact|
      if (contact['id'] == @contact.entity_id)
        if phone_numbers = contact['phoneNumbers']
          phone_number = phone_numbers.find{ |pn| @contact.phone_number.include?(pn['value'].strip)}
          raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data) unless phone_number
        end
        matched_contact = true
        break
      end
    end

    raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid) unless matched_contact

    return @deal, @contact
  end
end
