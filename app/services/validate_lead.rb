require 'rest-client'
class ValidateLead < ApplicationService

  def initialize(lead, token = nil)
    @lead = lead
    @token = token
  end

  def call
    return [] unless @lead.present?
    verified_lead = GetLead.call(@lead.entity_id, @token)

    if verified_lead
      if phone_numbers = verified_lead["phoneNumbers"]
        phone_number = phone_numbers.find{ |pn| @lead.phone_number.include?(pn['value'].strip)}
        if phone_number
          @lead.name = "#{verified_lead['firstName']} #{verified_lead['lastName']}"
          @lead.owner_id = verified_lead['ownerId']
        else
          raise(ExceptionHandler::InvalidLeadPhoneError, ErrorCode.invalid_lead_phone)
        end
      end
    else
      Rails.logger.info "Throwing invalid lead"
      raise(ExceptionHandler::InvalidLeadError, ErrorCode.invalid_lead)
    end
    return @lead
  end
end
