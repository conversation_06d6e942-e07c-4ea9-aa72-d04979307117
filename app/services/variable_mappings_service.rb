# frozen_string_literal: true

class VariableMappingsService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def get
    whatsapp_template = WhatsappTemplate.find_by(tenant_id: @auth_data.tenant_id, id: @params[:id])
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) if whatsapp_template.nil?

    unless @current_user.can_read_template?(whatsapp_template)
      Rails.logger.error "User doesn't have permission to read whatsapp template"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_template.variable_mappings
  end

  def save
    whatsapp_template = WhatsappTemplate.find_by(tenant_id: @auth_data.tenant_id, id: @params[:id])
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) if whatsapp_template.nil?

    unless @current_user.can_update_template?(whatsapp_template)
      Rails.logger.error "User doesn't have permission to update whatsapp template"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    validate_template_variables(whatsapp_template.variable_mappings.select(:id, :template_variable, :component_type, :parent_entity), @params[:variable_mappings])

    variable_mappings = @params[:variable_mappings].map.with_index do |mapping, index|
      [
        index, mapping.slice(:id, :component_type, :template_variable, :entity, :fallback_value, :field_type, :internal_name)
      ]
    end.to_h

    whatsapp_template.assign_attributes(variable_mappings_attributes: variable_mappings)

    begin
      whatsapp_template.save!
    rescue StandardError => e
      Rails.logger.error "VariableMappingsService Error while creating template Tenant id #{@auth_data.tenant_id} message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.something_went_wrong')}")
    end

    whatsapp_template.variable_mappings
  end

  def prepare_variables_for_template
    whatsapp_template = @params[:whatsapp_template]
    variables_for_template = whatsapp_template.extract_variables
    variable_objects = whatsapp_template.variable_mappings

    variables_array = variables_for_template.inject([]) do |arr, (type, variables)|
      variables.each do |variable|
        existing_object = variable_objects.find { |obj| obj.component_type == type && obj.template_variable == variable }
        if existing_object.present?
          arr << {
            id: existing_object.id,
            component_type: existing_object.component_type,
            template_variable: existing_object.template_variable,
            entity: existing_object.entity,
            internal_name: existing_object.internal_name,
            fallback_value: existing_object.fallback_value,
            field_type: existing_object.field_type,
            tenant_id: whatsapp_template.tenant_id,
            parent_entity: whatsapp_template.entity_type
          }
        else
          arr << {
            id: nil,
            component_type: type,
            template_variable: variable,
            entity: nil,
            internal_name: nil,
            fallback_value: nil,
            field_type: nil,
            tenant_id: whatsapp_template.tenant_id,
            parent_entity: whatsapp_template.entity_type
          }
        end
      end

      arr
    end

    mapping_ids_to_delete = variable_objects.map(&:id) - variables_array.map { |variable| variable[:id] }.compact
    variables_array += mapping_ids_to_delete.map { |id_to_delete| { id: id_to_delete, _destroy: true } }
    variables_array.map.with_index { |hash, index| [index, hash] }.to_h
  end

  private

  def validate_template_variables(existing_template_variables, incoming_header_variables)
    [HEADER, BODY, BUTTON_URL, BUTTON_COPY_CODE].each do |component_type|
      incoming_component_variables = @params[:variable_mappings].map { |m| m[:template_variable] if m[:component_type] == component_type }.compact_blank
      existing_component_variables = existing_template_variables.map { |var| var.template_variable if var.component_type == component_type }

      if (existing_component_variables - incoming_component_variables).any? || (incoming_component_variables - existing_component_variables).any?
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.variables_mismatch')}")
      end

      unless incoming_component_variables.length == incoming_component_variables.uniq.length
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.duplicate_variables')}")
      end
    end

    if existing_template_variables.present?
      template_variables = EntityVariables.new(existing_template_variables.first.parent_entity).get

      incoming_header_variables.each do |incoming_variable|
        matching_variable_present = template_variables.find  do |variable|
          variable['entity'] == incoming_variable[:entity] &&
          variable['internalName'] == incoming_variable[:internal_name] &&
          variable['type'] == incoming_variable[:field_type]
        end.present?

        unless matching_variable_present
          raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_variable', variable_index: incoming_variable[:template_variable], component_type: incoming_variable[:component_type])}")
        end
      end
    end
  end
end
