# frozen_string_literal: true

DATE_FORMAT_MAPPING = { "MMM D, YYYY [at] h:mm a" => "%b %d,%Y", "DD-MM-YYYY HH:mm:ss" => '%e-%m-%Y'}
DATE_TIME_FORMAT_MAPPING = { "MMM D, YYYY [at] h:mm a" => "%b %d,%Y at%l:%M %P", "DD-MM-YYYY HH:mm:ss" => '%e-%m-%Y %k:%M:%S'}

class WhatsappTemplateService
  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def get
    unless @auth_data.can_access?('whatsappTemplate', 'read')
      Rails.logger.error "User doesn't have permission to read whatsapp template"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_template = WhatsappTemplate.where(tenant_id: @auth_data.tenant_id).find_by(id: @params[:id])
    raise(ExceptionHandler::NotFound, ErrorCode.not_found) if whatsapp_template.nil?

    unless @auth_data.can_access?('whatsappTemplate', 'read_all')
      unless (template_creator?(whatsapp_template) || connected_account_agent?(whatsapp_template))
        raise(ExceptionHandler::NotFound, ErrorCode.not_found)
      end
    end

    whatsapp_template
  end

  def search
    unless @auth_data.can_access?('whatsappTemplate', 'read')
      Rails.logger.error "User doesn't have permission to read whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_templates = WhatsappTemplate.where(tenant_id: @auth_data.tenant_id)
    unless @auth_data.can_access?('whatsappTemplate', 'read_all')
      whatsapp_templates =
        whatsapp_templates.where(created_by_id: @current_user.id)
        .or(
          whatsapp_templates.where(
            connected_account_id: AgentUser.select(:connected_account_id).where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id)
          )
        )
    end

    if @params.dig(:json_rule, :rules).present?
      whatsapp_templates = ValidateAndBuildQueryFromJsonRule.new(whatsapp_templates, @params[:json_rule][:rules]).build_query
    end

    sorting_field, direction = @params[:sort]&.split(',')
    sorting_field ||= 'updatedAt'
    direction ||= 'desc'
    unless WHATSAPP_TEMPLATE_SORTABLE_FIELDS.include?(sorting_field) && %w[asc desc].include?(direction)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid sorting params')}")
    end

    whatsapp_templates.order(sorting_field.underscore.to_sym => direction).paginate(
      page: @params[:page] || 1,
      per_page: @params[:size] || 10
    )
  end

  def create
    unless @auth_data.can_access?('whatsappTemplate', 'write')
      Rails.logger.error "User doesn't have permission to create whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    @params[:connected_account_id] = @params.dig(:connected_account,:id)
    whatsapp_template = WhatsappTemplate.new(@params.slice(*%w[name connected_account_id entity_type category language]).merge(tenant_id: @auth_data.tenant_id, status: DRAFT_STATUS, created_by: @current_user, updated_by: @current_user))
    whatsapp_template.assign_attributes(whatsapp_template_namespace: WhatsappTemplate.generate_namespace(whatsapp_template.name))

    unless whatsapp_template.valid?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.model_errors', error: whatsapp_template.errors.full_messages.to_sentence)}")
    end

    unless connected_account_agent?(whatsapp_template)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_connected_account')}")
    end

    template_components = tally_and_format_components(@params[:components])
    whatsapp_template.assign_attributes(components_attributes: template_components)
    whatsapp_template.assign_attributes(
      variable_mappings_attributes: VariableMappingsService.new({ whatsapp_template: whatsapp_template }).prepare_variables_for_template
    )

    begin
      whatsapp_template.save!
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "WhatsappTemplateService Error while creating template Tenant id #{@auth_data.tenant_id} message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{e.message.gsub('Validation failed:', '').gsub('Components base', '').gsub('Components', '').strip.squish.capitalize}")
    end

    whatsapp_template
  end

  def create_and_submit
    ActiveRecord::Base.transaction do
      whatsapp_template = create
      whatsapp_template.status = SUBMITTING
      whatsapp_template.save!
      SubmitWhatsappTemplateJob.perform_later(whatsapp_template.id)
      whatsapp_template
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "WhatsappTemplateService Error while creating template Tenant id #{@auth_data.tenant_id} message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{e.message.gsub('Validation failed:', '').gsub('Components base', '').gsub('Components', '').strip.squish.capitalize}")
    end
  end

  def update
    unless @auth_data.can_access?('whatsappTemplate', 'update')
      Rails.logger.error "User doesn't have permission to update whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_template = get
    unless @auth_data.can_access?('whatsappTemplate', 'update_all') || template_creator?(whatsapp_template)
      Rails.logger.error "User doesn't have permission to update whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    unless [DRAFT_STATUS, APPROVED, REJECTED, PAUSED, INACTIVE_TEMPLATE].include?(whatsapp_template.status)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_template_status_to_update')}")
    end

    has_whatsapp_template_name_changed = whatsapp_template.name != @params[:name]
    whatsapp_template.assign_attributes(name: @params[:name], updated_by: @current_user)
    unless whatsapp_template.valid?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.model_errors', error: whatsapp_template.errors.full_messages.to_sentence)}")
    end

    template_components = tally_and_format_components(@params[:components])
    existing_template_component_ids = whatsapp_template.components.map(&:id)
    (existing_template_component_ids - template_components.values.map { |comp| comp[:id].to_i }).each do |comp_id|
      template_components[comp_id + 100] = { id: comp_id, _destroy: true }
    end
    whatsapp_template.assign_attributes(components_attributes: template_components)

    ActiveRecord::Base.transaction do
      whatsapp_template.status = SUBMITTING
      whatsapp_template.save!
      whatsapp_template.assign_attributes(
        variable_mappings_attributes: VariableMappingsService.new({ whatsapp_template: whatsapp_template }).prepare_variables_for_template
      )
      whatsapp_template.save!

    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "WhatsappTemplateService Error while creating or updating template id #{whatsapp_template.id} Tenant id #{@auth_data.tenant_id} message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{e.message.gsub('Validation failed:', '').gsub('Components base', '').gsub('Components', '').strip.squish.capitalize}")
    end

    SubmitWhatsappTemplateJob.perform_later(whatsapp_template.id)
    Publishers::WhatsappTemplateNameUpdatedPublisher.call(whatsapp_template) if has_whatsapp_template_name_changed
    whatsapp_template
  end

  def lookup
    rules = [
      {
        field: 'status',
        type: 'long',
        operator: 'equal',
        value: APPROVED
      }
    ]

    if @params[:q].present?
      rules << {
        field: 'name',
        type: 'string',
        operator: 'contains',
        value: @params[:q]
      }
    end

    if @params[:entity_type].present?
      rules << {
        field: 'entityType',
        type: 'long',
        operator: 'equal',
        value: @params[:entity_type]
      }
    end

    if @params[:connected_account_id].present?
      rules << {
        field: 'connectedAccount',
        type: 'long',
        operator: 'equal',
        value: @params[:connected_account_id]
      }
    end

    @params[:json_rule] = {
      rules: rules
    }

    search
  end

  def deactivate
    unless @auth_data.can_access?('whatsappTemplate', 'update')
      Rails.logger.error "User doesn't have permission to update whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_template = get
    unless @auth_data.can_access?('whatsappTemplate', 'update_all') || template_creator?(whatsapp_template)
      Rails.logger.error "User doesn't have permission to update whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    unless [DRAFT_STATUS, APPROVED, REJECTED, PAUSED].include?(whatsapp_template.status)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_template_status_to_deactivate')}")
    end

    whatsapp_template.update(status: INACTIVE_TEMPLATE, updated_by: @current_user)
    whatsapp_template
  end

  def preview
    whatsapp_template = get

    unless whatsapp_template.entity_type == @params[:entity_type]
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.template_entity_and_preview_entity_mismatch')}")
    end

    validate_variable_mappings(whatsapp_template)

    get_formatted_message(whatsapp_template)[1]
  end

  def send_message
    tenant_whatsapp_credit = WhatsappCredit.find_by(tenant_id: @auth_data.tenant_id)

    unless tenant_whatsapp_credit&.has_whatsapp_credits?
      Rails.logger.info "User does not have sufficient whatsapp credits balance, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance}||#{I18n.t('error.insufficient_whatsapp_credits')}")
    end
    
    whatsapp_template = get
    
    template_entity_type = @params[:template_entity] || @params[:entity_type]
    template_entity_id = @params[:template_entity_id] || @params[:entity_id]
    
    unless whatsapp_template.entity_type == template_entity_type
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.template_entity_and_preview_entity_mismatch')}")
    end
    
    validate_variable_mappings(whatsapp_template)
    
    original_entity_type = @params[:entity_type]
    original_entity_id = @params[:entity_id]
    
    @params[:entity_type] = template_entity_type
    @params[:entity_id] = template_entity_id

    payload, whatsapp_template = get_formatted_message(whatsapp_template, true)

    @params[:entity_type] = original_entity_type
    @params[:entity_id] = original_entity_id

    conversation_entity_data = fetch_conversation_entity_data
    
    if @params[:conversation_id].present?
      phone_number_conversation = Conversation.find_by(id: @params[:conversation_id], tenant_id: @auth_data.tenant_id)
      unless phone_number_conversation.present?
        Rails.logger.info "Conversation not found for the conversation_id: #{@params[:conversation_id]} Tenant id: #{@auth_data.tenant_id}"
        raise(ExceptionHandler::ConversationNotFoundError, "#{ErrorCode.conversation_not_found}||#{I18n.t('error.conversation_not_found')}")
      end

      selected_phone_number = conversation_entity_data['phoneNumbers']&.find { |phone| "#{phone['dialCode']}#{phone['value']}" == phone_number_conversation.phone_number } if phone_number_conversation.present?
    else
      selected_phone_number = conversation_entity_data['phoneNumbers']&.find { |phone| phone['id'] == @params[:phone_id] }
    end
  
    unless selected_phone_number.present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_phone_number}||#{I18n.t('error.invalid_phone_number_for_conversation')}")
    end

    entity_name = "#{conversation_entity_data['firstName']} #{conversation_entity_data['lastName']}".strip
    phone_number = "#{selected_phone_number['dialCode']}#{selected_phone_number['value']}"
    connected_account = whatsapp_template.connected_account

    conversation_params = {
      tenant_id: @auth_data.tenant_id,
      connected_account_id: connected_account.id,
      phone_number: phone_number
    }

    phone_number_conversation = Conversation.find_by(conversation_params) unless phone_number_conversation.present?

    unless @current_user.can_send_conversation_message?(@params[:entity_id], @params[:entity_type], conversation_entity_data['ownerId'])
      Rails.logger.info "User does not have conversation permission on #{@params[:entity_type]}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    unless phone_number_conversation.present?
      phone_number_conversation = Conversation.new(conversation_params)
      phone_number_conversation.owner_id = @current_user.id
      phone_number_conversation.save!
      SubConversation.create!(
        tenant_id: @auth_data.tenant_id,
        connected_account_id: connected_account.id,
        conversation_id: phone_number_conversation.id
      )

      look_up = GetLookUpWithOwner.call({ id: @params[:entity_id], entity: @params[:entity_type], phone_number: phone_number, name: entity_name, owner_id: conversation_entity_data['ownerId'] })
      look_up.conversations << phone_number_conversation
      look_up.save! if look_up.new_record?

      AssociateConversationWithEntitiesJob.perform_later(phone_number_conversation.id, LOOKUP_LEAD, { dialCode: selected_phone_number['dialCode'], value: selected_phone_number['value'] })
      AssociateConversationWithEntitiesJob.perform_later(phone_number_conversation.id, LOOKUP_CONTACT, { dialCode: selected_phone_number['dialCode'], value: selected_phone_number['value'] })
    end

    if phone_number_conversation.owner_id != @current_user.id
      phone_number_conversation.update!(owner_id: @current_user.id)
    end

    send_message_via_interakt({
      whatsapp_template: whatsapp_template,
      payload: payload,
      phone_number: phone_number,
      connected_account: connected_account,
      entity_name: entity_name,
      phone_number_conversation: phone_number_conversation,
      conversation_entity_data: conversation_entity_data
    })
  end

  def sync_status
    unless @auth_data.can_access?('whatsappTemplate', 'update')
      Rails.logger.error "User doesn't have permission to update whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    whatsapp_template = get

    unless @auth_data.can_access?('whatsappTemplate', 'update_all') || template_creator?(whatsapp_template)
      Rails.logger.error "User doesn't have permission to update whatsapp templates"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    connected_account = whatsapp_template.connected_account

    request_parameters = {
      url: "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{whatsapp_template.whatsapp_template_id}",
      request_type: :get
    }

    response = Facebook::Request.process(request_parameters, connected_account)

    if(response.body['status'] != whatsapp_template.status)
      whatsapp_template.update(status: response.body['status'], reason: nil, additional_info: nil)
    end
  end

  def send_bulk_message
    @entity_data = @params[:entity_data]

    tenant_whatsapp_credit = WhatsappCredit.find_by(tenant_id: @auth_data.tenant_id)

    unless tenant_whatsapp_credit&.has_whatsapp_credits_for_bulk?
      Rails.logger.info "User does not have sufficient whatsapp credits balance for bulk or workflow action, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      if tenant_whatsapp_credit.present? && !tenant_whatsapp_credit.is_low_credits_email_sent
        Publishers::SendLowWhatsappCreditsEmailPublisher.call(tenant_whatsapp_credit)
        tenant_whatsapp_credit.update(is_low_credits_email_sent: true)
      end
      raise(ExceptionHandler::InsufficientWhatsappCreditsBalance, "#{ErrorCode.insufficient_whatsapp_credits_balance_for_bulk}||#{I18n.t('error.insufficient_whatsapp_credits_for_bulk')}")
    end

    whatsapp_template = get

    template_entity_type = @params[:template_entity] || @params[:entity_type]
    template_entity_id = @params[:template_entity_id] || @params[:entity_id]

    unless whatsapp_template.entity_type == template_entity_type
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.template_entity_and_preview_entity_mismatch')}")
    end

    unless whatsapp_template.status == APPROVED
      raise(ExceptionHandler::InactiveWhatsappTemplate, "#{ErrorCode.inactive_whatsapp_template}||#{I18n.t('error.invalid_template.template_is_not_approved')}")
    end

    conversation_entity_data = fetch_conversation_entity_data

    if @params[:bulk_job] && !@current_user.can_send_conversation_message?(@params[:entity_id], @params[:entity_type], conversation_entity_data['ownerId'])
      Rails.logger.info "User does not have conversation create permission, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::MessageNotAllowedError, "#{ErrorCode.message_not_allowed}||#{I18n.t('error.message_not_allowed')}")
    end

    entity_name = "#{conversation_entity_data['firstName']} #{conversation_entity_data['lastName']}".strip
    phone_number = "#{@params[:phone_number]['dialCode']}#{@params[:phone_number]['value']}"
    connected_account = whatsapp_template.connected_account
    conversation_params = {
      tenant_id: @auth_data.tenant_id,
      connected_account_id: connected_account.id,
      phone_number: phone_number
    }

    phone_number_conversation = Conversation.find_by(conversation_params)

    if phone_number_conversation.present?
      Thread.current[:user] = User::GetUserDetails.call(phone_number_conversation.owner_id, @auth_data.tenant_id)
      @current_user = Thread.current[:user]
    end

    unless phone_number_conversation.present?
      Rails.logger.info "Conversation for #{@params[:bulk_job] ? 'bulk message' : 'workflow'} not found for the phone number: #{phone_number}, connected account id: #{connected_account.id}, Tenant id: #{@auth_data.tenant_id}"

      phone_number_conversation = Conversation.new(conversation_params)
      phone_number_conversation.owner_id = @params[:bulk_job] ? conversation_entity_data['ownerId'] : @current_user.id
      phone_number_conversation.save!
      SubConversation.create!(
        tenant_id: @auth_data.tenant_id,
        connected_account_id: connected_account.id,
        conversation_id: phone_number_conversation.id
      )

      look_up = GetLookUpWithOwner.call({ id: @params[:entity_id], entity: @params[:entity_type], phone_number: phone_number, name: entity_name, owner_id: conversation_entity_data['ownerId'].is_a?(Hash) ? conversation_entity_data['ownerId']['id'] : conversation_entity_data['ownerId'] })
      look_up.conversations << phone_number_conversation

      AssociateConversationWithEntitiesJob.perform_later(phone_number_conversation.id, LOOKUP_LEAD, { dialCode: @params[:phone_number]['dialCode'], value: @params[:phone_number]['value'] })
      AssociateConversationWithEntitiesJob.perform_later(phone_number_conversation.id, LOOKUP_CONTACT, { dialCode: @params[:phone_number]['dialCode'], value: @params[:phone_number]['value'] })
    end

    validate_variable_mappings(whatsapp_template)

    original_entity_type = @params[:entity_type]
    original_entity_id = @params[:entity_id]

    @params[:entity_type] = template_entity_type
    @params[:entity_id] = template_entity_id

    payload, whatsapp_template = get_formatted_message(whatsapp_template, true)

    @params[:entity_type] = original_entity_type
    @params[:entity_id] = original_entity_id

    send_message_via_interakt({
      whatsapp_template: whatsapp_template,
      payload: payload,
      phone_number: phone_number,
      connected_account: connected_account,
      entity_name: entity_name,
      phone_number_conversation: phone_number_conversation,
      conversation_entity_data: conversation_entity_data
    })

    WhatsappCreditsService.new.park_credits(@auth_data.tenant_id, @params[:phone_number]['dialCode'].slice(1..), whatsapp_template.category.capitalize, connected_account)
  end

  private

  def send_message_via_interakt(params)
    whatsapp_template = params[:whatsapp_template]
    payload = params[:payload]
    phone_number = params[:phone_number]
    connected_account = params[:connected_account]
    entity_name = params[:entity_name]
    phone_number_conversation = params[:phone_number_conversation]
    conversation_entity_data = params[:conversation_entity_data]

    phone_number_sub_conversation = phone_number_conversation.sub_conversations.last
    response = Interakt::Message.new(whatsapp_template.connected_account).send_template_message(whatsapp_template, payload, phone_number)

    message_remote_id = response.body.dig('messages', 0)['id']
    message_owner_id = conversation_entity_data['ownerId'].is_a?(Hash) ? conversation_entity_data['ownerId']['id'] : conversation_entity_data['ownerId']

    header_component = whatsapp_template.components.find { |component| component.component_type === HEADER }
    attachments = []
    template_media = nil

    if header_component&.is_header_component_media?
      template_media = if @params[:dynamic_template_media_id].present? && header_component.media_type == DYNAMIC
        TemplateMedia.find_by(id: @params[:dynamic_template_media_id], tenant_id: @auth_data.tenant_id)
      else
        header_component.template_media
      end

      if template_media.present?
        attachments << {
          file_name: template_media.extract_file_name
        }
      end
    end

    message_params = {
      content: generate_message_content(whatsapp_template),
      direction: 'outgoing',
      medium: 'whatsapp',
      owner_id: @params[:bulk_job] ? phone_number_conversation.owner_id : @current_user.id,
      sent_at: DateTime.now,
      sender_number: connected_account.waba_number,
      recipient_number: phone_number,
      status: 'sending',
      message_type: WHATSAPP_BUSINESS,
      related_to: [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: message_owner_id  }],
      recipients: [{ entity: @params[:entity_type], id: @params[:entity_id], phone_number: phone_number.gsub('+', ''), name: entity_name, owner_id: message_owner_id }],
      validate_lookup: false,
      remote_id: message_remote_id,
      connected_account: connected_account,
      conversation_id: phone_number_conversation.id,
      sub_conversation_id: phone_number_sub_conversation.id,
      whatsapp_template_id: whatsapp_template.id,
      attachments: attachments,
      metadata: {
        executedWorkflows: @params.dig(:metadata, :executedWorkflows) || []
      }
    }.with_indifferent_access

    if @params[:campaign_id].present? && @params[:activity_id].present?
      message_params[:campaign_info] = {
        campaignId: @params[:campaign_id],
        activityId: @params[:activity_id],
        entityId: @params[:entity_id],
        entity: @params[:entity_type],
        phoneNumber: @params[:phone_number]
      }
    end

    message_id = Message::MessageService.new(message_params).call
    CopyTemplateMediaToMessageJob.perform_later(message_id, template_media.id) if template_media.present?
    message_id
  end

  def validate_variable_mappings(whatsapp_template)
    template_variable_mappings = whatsapp_template.variable_mappings
    all_variables = whatsapp_template.extract_variables
    variables_count = all_variables.values.flatten.count
    return if variables_count == 0

    unless template_variable_mappings.count == variables_count && template_variable_mappings.all?(&:valid?)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.variables_unmapped')}")
    end
  end

  def get_formatted_message(whatsapp_template, create_media_object = false)
    @entity_data, user_data, tenant_data = get_entities_data(whatsapp_template)
    template_variable_mappings = whatsapp_template.variable_mappings

    dynamic_button_component_ids = []

    all_variables = whatsapp_template.extract_variables
    all_components = whatsapp_template.components
    formatted_components = { 'HEADER': [], 'BODY': [] }.with_indifferent_access
    header_component = all_components.find { |component| component.component_type === HEADER }

    if(create_media_object && header_component&.is_header_component_media?)
      header_component_format = header_component.component_format.downcase
      template_media = if @params[:dynamic_template_media_id].present? && header_component.media_type == DYNAMIC
        TemplateMedia.find_by(id: @params[:dynamic_template_media_id], tenant_id: @auth_data.tenant_id)
      else
        header_component.template_media
      end

      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.template_media_not_found')}") if template_media.blank?

      media_object = {
        link: template_media.media_url(true)[:url]
      }
      media_object[:filename] = template_media.extract_file_name if header_component_format == 'document'

      formatted_components[HEADER] = [{
        :type => header_component_format,
        header_component_format => media_object
      }]
    end

    all_variables.each do |component_type, component_variables|
      component_mappings = template_variable_mappings.select { |mapping| mapping.component_type == component_type }

      # For button variables, this will always be nil
      current_component = all_components.find do |component|
        component.component_type == component_type
      end

      component_variables.each do |variable|
        current_mapping = component_mappings.find { |mapping| mapping.template_variable == variable }
        # For each url variable, a different component is associated
        if component_type == BUTTON_COPY_CODE
          current_component = all_components.find do |component|
            (component.component_type == BUTTON && component.component_format == COPY_CODE)
          end
        elsif component_type == BUTTON_URL
          current_component = all_components.find do |component|
            (component.component_type == BUTTON && component.component_format == URL && component.component_value.ends_with?("{{#{variable}}}"))
          end
          dynamic_button_component_ids << current_component.id
        end

        data =
          if current_mapping.entity == 'tenant'
            tenant_data
          elsif current_mapping.entity == current_mapping.parent_entity
            @entity_data
          else
            user_data[current_mapping.entity]
          end

        if component_type == BUTTON_URL
          current_component.component_value = get_encoded_value_for_url(current_component.component_value[0...-5], get_value(data, current_mapping).presence || current_mapping.fallback_value || '')
        elsif component_type == BUTTON_COPY_CODE
          current_component.component_text = get_value(data, current_mapping).presence || current_mapping.fallback_value || ''
        else
          current_component.component_text = current_component.component_text.gsub("{{#{variable}}}", get_value(data, current_mapping).presence || current_mapping.fallback_value || '')
          formatted_components[current_component.component_type] << {
            type: current_component.component_format.downcase,
            text: get_value(data, current_mapping).presence || current_mapping.fallback_value || ''
          }
        end
      end
    end

    facebook_payload = formatted_components.map do |component, values|
      { type: component.downcase, parameters: values }.with_indifferent_access
    end.compact_blank

    # Note: Phone number button does not need an entry in payload
    facebook_payload += all_components.select { |comp| comp.component_type == BUTTON && comp.component_format != PHONE_NUMBER && (comp.component_format != URL || dynamic_button_component_ids.include?(comp.id)) }.sort_by { |comp| comp.position }.map do |comp|
      case comp.component_format
      when COPY_CODE
        {
          type: 'button',
          sub_type: 'copy_code',
          index: comp.position,
          parameters:
          [
            {
              type: 'coupon_code',
              coupon_code: comp.component_text
            }
          ]
        }.with_indifferent_access
      when QUICK_REPLY
        {
          type: 'button',
          sub_type: 'quick_reply',
          index: comp.position,
          parameters:
           [
            {
              type: 'payload',
              payload: comp.component_text
            }
          ]
       }.with_indifferent_access
      when URL
        {
          type: "button",
          sub_type: "url",
          index: comp.position,
          parameters:
           [
            {
              type: "text",
              text: comp.component_value
            }
          ]
       }.with_indifferent_access
      end
    end.compact_blank

    [facebook_payload, whatsapp_template]
  end

  def get_entities_data(whatsapp_template)
    @entity_data = @entity_data.present? ? @entity_data : {}
    user_data = {}
    tenant_data = {}

    template_variable_mappings = whatsapp_template.variable_mappings
    entity_mapped_fields = template_variable_mappings.select { |mapping| mapping if mapping.entity == @params[:entity_type] }
    entity_user_lookup_fields = "#{@params[:entity_type].upcase}_USER_LOOKUP_FIELDS".constantize
    user_mapped_fields = template_variable_mappings.select { |mapping| mapping if entity_user_lookup_fields.include?(mapping.entity) }
    tenant_mapped_fields = template_variable_mappings.select { |mapping| mapping if mapping.entity == 'tenant' }

    fields_to_fetch = entity_mapped_fields.map(&:internal_name) + user_mapped_fields.map(&:entity).uniq + ['phoneNumbers', 'customFieldValues']

    unless @entity_data.present?
      @entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type], fields: fields_to_fetch.uniq, generate_token: true }).get_by_json_rule
      @entity_data = @entity_data['content'][0].merge({ 'metaData': @entity_data['metaData'] })
    end

    if @entity_data['customFieldValues'].present?
      @entity_data = @entity_data.merge(@entity_data.delete('customFieldValues')).with_indifferent_access
    end

    if tenant_mapped_fields.any?
      tenant_data = GetTenant.call.with_indifferent_access
    end

    date_fields = template_variable_mappings.any? { |mapping| ['DATE_PICKER', 'DATETIME_PICKER'].include?(mapping.field_type) }

    if date_fields.present?
      @timezone_abbreviations = YAML.load_file(Rails.root.join('config/timezone_abbreviations.yml'))
    end

    if user_mapped_fields.any? || (date_fields.present? && (@current_user.timezone.blank? || @current_user.date_format.blank?))
      user_fields_to_fetch = []
      user_fields_to_fetch += ['timezone'] if @current_user.timezone.blank?
      user_fields_to_fetch += ['dateFormat'] if @current_user.date_format.blank?

      user_fields_to_fetch += user_mapped_fields.pluck(:internal_name)

      user_field_and_user_id_map = @entity_data.slice(*user_mapped_fields.pluck(:entity).uniq)

      user_field_and_user_id_map = user_field_and_user_id_map.map do |key, value|
        if value.is_a?(Hash)
          [key, value['id']]
        else
          [key, value]
        end
      end.to_h

      user_field_and_user_id_map = user_field_and_user_id_map.merge({ 'current_user': @current_user.id })
      user_data = EntityService.new({ entity_id: user_field_and_user_id_map.values.compact, entity_type: LOOKUP_USER, fields: user_fields_to_fetch.uniq }).get_by_json_rule

      user_field_and_user_id_map.each do |key, val|
        user_field_and_user_id_map[key] = user_data['content'].find { |user| user['id'] == val }&.merge({ metaData: user_data['metaData'] })
      end

      user_data = user_field_and_user_id_map.with_indifferent_access
      @current_user_settings = user_data['current_user']
      @current_user.update(timezone: @current_user_settings['timezone'], date_format: @current_user_settings['dateFormat'])
    end

    @entity_data = set_picklist_values(@entity_data)

    user_data.each do |user_field, user_details|
      user_data[user_field] = set_picklist_values(user_details)
    end

    tenant_data = set_picklist_values(tenant_data)

    [@entity_data, user_data, tenant_data]
  end

  def get_value(entity_data, variable_mapping)
    return entity_data unless entity_data.present?
    entity_data = entity_data.with_indifferent_access
    raw_value = entity_data[variable_mapping.internal_name]

    case variable_mapping.field_type
    when 'TEXT_FIELD', 'URL', 'NUMBER', 'PARAGRAPH_TEXT', 'TOGGLE'
      raw_value.to_s
    when 'PHONE'

      raw_value&.map { |phone| "#{phone['dialCode']} #{phone['value']}" }.join(', ')
    when 'PICK_LIST', 'PIPELINE'
      return raw_value['name'] if raw_value.is_a?(Hash)
      entity_data.dig('metaData', 'idNameStore', variable_mapping.internal_name, raw_value.to_s)
    when 'LOOK_UP'
      if raw_value.is_a?(Array)
        raw_value.map { |val| val['name'] }.join(', ')
      elsif raw_value.is_a?(Hash)
        raw_value['name']
      else
        entity_data.dig('metaData', 'idNameStore', variable_mapping.internal_name, raw_value.to_s)
      end
    when 'MULTI_PICKLIST'
      return '' unless raw_value.present?

      return raw_value.map { |val| val['name'] }.join(', ') if raw_value.first&.is_a?(Hash)

      raw_value.map! { |value| value.to_i } if raw_value.first&.is_a?(String)

      entity_data.dig('metaData', 'idNameStore', variable_mapping.internal_name).filter do |key, val|
        raw_value.include?(key.to_i)
      end.values.join(', ')
    when 'EMAIL'
      raw_value&.map { |email| email['value'] }&.join(', ')
    when 'DATE_PICKER'
      parse_date(raw_value)
    when 'DATETIME_PICKER'
      parse_date_time(raw_value)
    when 'MONEY'
      return '' unless raw_value.present?

      if raw_value.is_a?(Hash)
        return "#{raw_value['value']} #{get_currency_from_currency_id(raw_value['currencyId']) || ''}"
      end

      return ''
    when 'PIPELINE_STAGE'
      return '' unless raw_value.present?

      if raw_value.is_a?(Hash)
        return raw_value['name']
      end

      return ''
    else
      raw_value
    end
  end

  def parse_date_time(str)
    return nil if str.blank?
    str = str.to_s
    date = ''
    begin
      if str.include?('-')
        date = DateTime.parse(str)
        date = Time.parse(date.to_s)
      else
        date = Time.at(str.to_i / 1000)
      end
      date = date.in_time_zone(@current_user.timezone).strftime(DATE_TIME_FORMAT_MAPPING[@current_user.date_format])
      date += " #{@timezone_abbreviations[@current_user.timezone]}"
    rescue
      nil
    end
  end

  def parse_date(str)
    return nil if str.blank?
    str = str.to_s
    begin
      str = DateTime.parse(str).in_time_zone(@current_user.timezone).strftime(DATE_FORMAT_MAPPING[@current_user.date_format])
    rescue
      nil
    end
  end

  def template_creator?(whatsapp_template)
    whatsapp_template.created_by == @current_user
  end

  def connected_account_agent?(whatsapp_template)
    AgentUser.where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id, connected_account_id: whatsapp_template.connected_account_id).exists?
  end

  def tally_and_format_components(components)
    raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.components_missing')}") unless components.present?

    components_tally = components.map { |component| component[:type] }.tally
    if(components_tally[HEADER].to_i > 1 || components_tally[BODY].to_i != 1 || components_tally[FOOTER].to_i > 1 || components_tally[BUTTON].to_i > 10)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_component_count')}")
    end

    button_components = components.select { |component| component[:type] == BUTTON }
    buttons_tally = button_components.map { |component| component[:format] }.tally
    if(buttons_tally[PHONE_NUMBER].to_i > 1 || buttons_tally[URL].to_i > 2 || buttons_tally[COPY_CODE].to_i > 1)
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_button_count')}")
    end

    button_position_tally = button_components.map { |component| component[:position].to_i }.tally
    if button_position_tally.find { |_, position_count, | position_count > 1 }.present?
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_button_sequence')}")
    end

    quick_reply_buttons_count = buttons_tally[QUICK_REPLY].to_i
    if quick_reply_buttons_count.positive?
      button_components = button_components.sort_by { |button| button[:position].to_i }
      unless (
        button_components.first(quick_reply_buttons_count).all? { |comp| comp[:format] == QUICK_REPLY } ||
        button_components.last(quick_reply_buttons_count).all? { |comp| comp[:format] == QUICK_REPLY }
      )
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_button_categorization')}")
      end
    end

    url_buttons = components.select { |component| component[:type] == BUTTON && component[:format] == URL }
    if url_buttons.present?
      url_variables = url_buttons.inject([]) { |arr, button| arr + button[:value]&.scan(WHATSAPP_TEMPLATE_VARIABLE_REGEX).to_a }
      if url_variables.present? && [[1, 2], [1], [2]].exclude?(url_variables.map { |variable| variable.tr('{}', '') }.sort.map(&:to_i))
        raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_whatsapp_template}||#{I18n.t('error.invalid_template.invalid_url_button_variables')}")
      end
    end

    components.map.with_index do |component, index|
      [index, {
        id: component[:id],
        component_type: component[:type],
        component_format: component[:format],
        component_text: component[:text],
        component_value: component[:value],
        position: component[:position].to_i,
        content: component[:content],
        media_type: component[:media_type],
        tenant_id: @auth_data.tenant_id
      }]
    end.to_h
  end

  def generate_message_content(whatsapp_template)
    header_component = whatsapp_template.components.find { |component| component.component_type == HEADER }
    body_component = whatsapp_template.components.find { |component| component.component_type == BODY }
    footer_component = whatsapp_template.components.find { |component| component.component_type == FOOTER }
    button_components = whatsapp_template.components.select { |component| component.component_type == BUTTON }.sort_by { |component| component.position }

    ([header_component, body_component, footer_component] + button_components).compact.map(&:component_text).join("\n")
  end

  def get_encoded_value_for_url(base_url, remaining_url)
    begin
      uri = URI(base_url + remaining_url)
      uri.to_s.split(base_url).second
    rescue URI::InvalidURIError => e
      Rails.logger.error "WhatsappTemplateService Error while forming URL for Tenant id #{@auth_data.tenant_id} Template Id #{@params[:id]} message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_url_whatsapp_template_button}||Invalid url for whatsapp template CTA #{base_url+remaining_url}")
    end
  end

  def set_picklist_values(data)
    return data unless data.present?

    picklists_response = get_standard_picklist
    return unless picklists_response

    picklist_hash = {
      'country' => 'COUNTRY',
      'companyCountry' => 'COUNTRY',
      'requirementCurrency' => 'CURRENCY',
      'companyIndustry' => 'INDUSTRY',
      'companyBusinessType' => 'BUSINESS_TYPE',
      'industry' => 'INDUSTRY',
      'language' => 'LANG',
      'currency' => 'CURRENCY',
      'timezone' => 'TIMEZONE'
    }

    picklist_hash.each do |field, type|
      next unless data[field].present?

      data['metaData']['idNameStore'][field] = {
        data[field] => picklists_response[type].select{|hash| hash["name"].eql?(data[field])}.first&.[]("displayName")
      }
    end
    data
  end

  def get_currency_from_currency_id(currency_id)
    picklists_response = get_standard_picklist
    return unless picklists_response

    picklists_response['CURRENCY'].find { |currency| currency['id'] == currency_id }&.[]('name')
  end

  def get_standard_picklist
    GetStandardPicklists.call
  end

  def fetch_conversation_entity_data
    conversation_entity_type = @params[:entity_type]
    conversation_entity_id = @params[:entity_id]
    
    if @params[:template_entity].present? && @params[:template_entity] != conversation_entity_type
      conversation_entity_data = EntityService.new({ 
        entity_id: conversation_entity_id, 
        entity_type: conversation_entity_type, 
        fields: ['phoneNumbers', 'firstName', 'lastName', 'ownerId', 'customFieldValues'],
        generate_token: true 
      }).get_by_json_rule
      
      if conversation_entity_data['content'].present?
        conversation_entity_data = conversation_entity_data['content'][0].merge({ 'metaData': conversation_entity_data['metaData'] })
        
        if conversation_entity_data['customFieldValues'].present?
          conversation_entity_data = conversation_entity_data.merge(conversation_entity_data.delete('customFieldValues')).with_indifferent_access
        end
        
        return conversation_entity_data
      end
    end
    
    @entity_data
  end
end
