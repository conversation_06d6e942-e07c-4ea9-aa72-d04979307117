# frozen_string_literal: true

class WhatsappWebhooks
  def initialize(payload)
    @payload = payload.with_indifferent_access
  end

  def process
    if @payload[:object] == 'whatsapp_business_account' && @payload[:entry].present?
      waba_connected_account_hash = ConnectedAccount.where(waba_id: @payload[:entry].map { |waba_entry| waba_entry[:id] }).pluck(:waba_id, :id).group_by(&:shift).transform_values(&:flatten)
      @payload[:entry].each do |waba_entry|
        begin
          connected_account_ids = waba_connected_account_hash[waba_entry[:id]]
          if connected_account_ids.present?
            waba_entry[:changes].each do |field_entry|
              begin
                case field_entry[:field]
                when 'message_template_status_update'
                  whatsapp_template =
                    WhatsappTemplate.find_by(
                      whatsapp_template_id: field_entry[:value][:message_template_id],
                      connected_account_id:  connected_account_ids
                    )
                  if whatsapp_template.present?
                    if whatsapp_template.update(
                      status: field_entry[:value][:event],
                      reason: field_entry[:value][:reason],
                      additional_info: retrieve_additional_info(field_entry)
                    )
                      PublishEvent.call(Event::WhatsappTemplateStatusUpdated.new(whatsapp_template))
                    else
                      Rails.logger.error "WhatsappWebhooks Error while updating template status field entry #{field_entry.inspect} message #{whatsapp_template&.errors&.full_messages&.to_sentence}"
                    end
                  end
                when 'messages'
                  if field_entry.dig(:value, :statuses).present?
                    message = Message.find_by(remote_id: field_entry[:value][:statuses][0][:id])

                    status_to_update = field_entry[:value][:statuses][0][:status]
                    # TODO: Check for delivered webhook payload
                    if message.present?
                      status_updated = false
                      case status_to_update
                      when 'sent'
                        if message.status == 'sending'
                          status_updated = Message.where(id: message.id, status: 'sending').update_all(status: status_to_update, sent_at: Time.at(field_entry[:value][:statuses][0][:timestamp].to_i).utc)
                        end
                      when 'delivered'
                        if message.status == 'sent' || message.status == 'sending'
                          status_updated = Message.where(id: message.id, status: ['sent', 'sending']).update_all(status: status_to_update, delivered_at: Time.at(field_entry[:value][:statuses][0][:timestamp].to_i).utc)
                        end
                      when 'read'
                        message.update(status: status_to_update, read_at: Time.at(field_entry[:value][:statuses][0][:timestamp].to_i).utc)
                        status_updated = true
                      when 'failed'
                        error_code = field_entry[:value][:statuses][0][:errors][0][:code]
                        error_code = error_code.present? ? "#{error_code} - " : ""
                        message.update(status: status_to_update, status_message: "#{error_code}#{field_entry[:value][:statuses][0][:errors][0][:error_data][:details]}", failed_at: DateTime.now.utc)
                        status_updated = true
                      end
                      if status_updated
                        Publishers::WhatsappMessageStatusUpdatedPublisher.call(message.reload)
                        Publishers::WhatsappMessageStatusUpdatedV2Publisher.call(message)
                        Publishers::WhatsappEntityMessageStatusTrackPublisher.call(message) if message.campaign_info.present?
                      end
                    else
                      Rails.logger.error "Whatsapp Message with remote_id #{field_entry[:value][:statuses][0][:id]} and sender_number #{field_entry[:value][:metadata][:display_phone_number].delete(' ')} not found for message update status of #{status_to_update}"
                    end
                  elsif field_entry.dig(:value, :messages).present?
                    process_incoming_message(field_entry, connected_account_ids)
                  end
                end
              rescue StandardError => e
                Rails.logger.error "WhatsappWebhooks Error while updating template status field entry #{field_entry.inspect} message #{e.message}"
              end
            end
          end
        rescue StandardError => e
          Rails.logger.error "WhatsappWebhooks Error while updating template for waba entry #{waba_entry.inspect} message #{e.message}"
        end
      end
    end
  end

  def process_incoming_message(field_entry, connected_account_ids)
    field_entry.dig(:value, :messages).each do |message|
      begin
        connected_account = ConnectedAccount.where(id: connected_account_ids, phone_number_id: field_entry.dig(:value, :metadata, :phone_number_id), status: ACTIVE).first
        next if connected_account.blank?

        connected_account_created_by_user = connected_account.created_by
        admin_token = GenerateToken.new(connected_account.created_by_id, connected_account.tenant_id, admin_permissions).call
        thread = Thread.current
        thread[:token] = admin_token
        thread[:auth] = User::TokenParser.parse(admin_token)
        thread[:user] = connected_account_created_by_user

        parsed_phone = Phonelib.parse("+#{message[:from].gsub(' ', '')}")
        parsed_phone_number = "+#{parsed_phone.country_code}#{parsed_phone.raw_national}"

        conversation_params = {
          connected_account_id: connected_account.id,
          tenant_id: connected_account.tenant_id,
          phone_number: parsed_phone_number
        }

        is_new_conversation = false
        phone_number_conversation = Conversation.find_by(conversation_params)

        unless phone_number_conversation.present?
          unless connected_account_created_by_user.can_create_conversation?
            Rails.logger.info "User does not have conversation create permission, User id: #{connected_created_by_user.id}, Tenant id: #{connected_account.tenant_id}"
            raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
          end

          phone_number_conversation = Conversation.new(conversation_params)
          phone_number_conversation.owner_id = connected_account_created_by_user.id
          phone_number_conversation.save!

          SubConversation.create!(
            tenant_id: connected_account.tenant_id,
            connected_account_id: connected_account.id,
            conversation_id: phone_number_conversation.id
          )
          is_new_conversation = true
        end

        if phone_number_conversation.owner_id != connected_account_created_by_user.id
          phone_number_conversation_user = User.find_by(id: phone_number_conversation.owner_id, tenant_id: connected_account.tenant_id)
          admin_token = GenerateToken.new(phone_number_conversation_user.id, connected_account.tenant_id, admin_permissions).call
          thread = Thread.current
          thread[:token] = admin_token
          thread[:auth] = User::TokenParser.parse(admin_token)
          thread[:user] = phone_number_conversation_user
        end

        options = {
          entity_name: field_entry.dig(:value, :contacts, 0, :profile, :name),
          token: admin_token,
          user_profile_permissions: admin_profile_permissions,
          native_whatsapp_webhook: true,
          connected_account: connected_account,
          is_new_conversation: is_new_conversation
        }

        payload = {
          direction: 'incoming',
          medium: WHATSAPP,
          remote_id: message[:id],
          message_type: WHATSAPP_BUSINESS,
          sender_number: message[:from],
          status: 'received',
          recipient_number: field_entry.dig(:value, :metadata, :display_phone_number).gsub(' ', ''),
          sent_at: Time.at(message[:timestamp].to_i).utc,
          owner_id: phone_number_conversation.owner_id,
          validate_lookup: false,
          tenant_id: connected_account.tenant_id,
          connected_account: connected_account,
          conversation_id: phone_number_conversation.id,
          sub_conversation_id: phone_number_conversation.sub_conversations.last.id
        }

        if SUPPORTED_MEDIA_TYPES.include?(message[:type])
          message_mime_type = message.dig(message[:type], 'mime_type')
          media_file_static_name =
            case message[:type]
            when 'image', 'video'
              message_mime_type.gsub('/', '.')
            when 'audio'
              message_mime_type.split(';').first.gsub('/', '.')
            when 'document'
              message.dig(message[:type], 'filename')
            end

          payload[:attachments] =
            [
              {
                file_name: media_file_static_name
              }
            ]
        end

        if message[:type] == 'text'
          payload.merge!(content: message[:text][:body])
        elsif message[:type] == 'button' && message[:button][:text].present?
          payload.merge!(content: message[:button][:text])
        elsif SUPPORTED_MEDIA_TYPES.include?(message[:type]) && message.dig(message[:type], 'caption').present?
          payload.merge!(content: message.dig(message[:type], 'caption'))
        end

        if SUPPORTED_MEDIA_TYPES.include?(message[:type]) && message.dig("#{message[:type]}", 'id').present?
          options[:skip_message_created_events] = true
        end

        response = Message::SyncService.new(payload, options).call
        phone_number_conversation.update!(last_message_received_at: payload[:sent_at])
        UploadMediaAttachmentToS3Job.perform_later(connected_account.id, response[:id], message.dig("#{message[:type]}", 'id'), true) if SUPPORTED_MEDIA_TYPES.include?(message[:type]) && message.dig("#{message[:type]}", 'id').present?
      rescue StandardError => e
        Rails.logger.error "WhatsappWebhooks Error while processing incoming message #{field_entry.inspect} message #{e.message}"
      end
    end
  end

  private

  def retrieve_additional_info(field_entry)
    case field_entry[:value][:event]
    when APPROVED, REJECTED, PENDING_DELETION
      nil
    when PAUSED
      field_entry[:value][:other_info]
    when FLAGGED
      field_entry[:value][:disable_info]
    end
  end

  def admin_permissions
    [
      {
        id: 1,
        name:  LOOKUP_LEAD,
        description: 'has access to lead',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 2,
        name: LOOKUP_CONTACT,
        description: 'has access to contact',
        limits: -1,
        units: 'count',
        action: {
          readAll: true,
          read: true,
          sms: true,
          write: true,
          update: true
        }
      },
      {
        id: 3,
        name: 'sms',
        description: 'has access to sms',
        limits: -1,
        units: 'count',
        action: {
          read: true,
          sms: true,
          write: true
        }
      },
      {
        id: 4,
        name: LOOKUP_USER,
        description: 'has access to user',
        limits: -1,
        units: 'count',
        action: {
          read: true
        }
      }
    ]
  end

  def admin_profile_permissions
    [
      {
        id: 1,
        name:  LOOKUP_LEAD,
        description: 'has access to lead',
        limits: -1,
        units: 'count',
        actions: {
          readAll: true,
          read: true,
          sms: true
        }
      },
      {
        id: 2,
        name: LOOKUP_CONTACT,
        description: 'has access to contact',
        limits: -1,
        units: 'count',
        actions: {
          readAll: true,
          read: true,
          sms: true
        }
      }
    ]
  end
end
