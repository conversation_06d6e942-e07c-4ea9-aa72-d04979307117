# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  error:
    unauthorized: "Unauthorized access."
    account_not_connected: "Whatsapp Business Number not connected"
    invalid_data: '%{error}'
    not_found: '%{type} not found'
    invalid_phone_id: 'Invalid Phone Id'
    invalid_entity_type: 'Invalid entity type'
    something_went_wrong: 'Something went wrong'
    insufficient_whatsapp_credits: 'Insufficient whatsapp credits balance'
    insufficient_whatsapp_credits_for_bulk: 'Insufficient whatsapp credits balance for bulk or workflow action'
    credit_details_not_found: 'Credit details not found'
    invalid_team: 'Invalid team.'
    invalid_share_rule: 'Invalid share rule - %{error}'
    conversation_not_found: 'Conversation not found'
    message_not_allowed: "You don't have required conversation permissions to send message"
    invalid_phone_number_for_conversation: 'This number is no longer associated with this entity. To continue the conversation, please use an alternate number.'
    connected_account:
      cannot_activate_account_contact_support: 'Failed to register whatsapp business. Please contact support.'
    invalid_message:
      invalid_entity: 'Invalid entity type or entity id.'
      missing_phone_id: 'Phone number missing.'
      missing_phone_id_or_conversation_id: 'Phone number or conversation id missing.'
      invalid_message_body: 'Please ensure that message is present and less than 4096 characters.'
      connected_account_not_found: 'Connected Account not found.'
      inactive_or_unverified_account: 'Inactive or unverified account. Please reconnect.'
      invalid_connected_account: "Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent."
      invalid_phone_number: 'Invalid phone number.'
      invalid_message_type: 'Invalid message type.'
      invalid_media_type: 'Invalid media type.'
      invalid_media_size: 'File size exceeds limit.'
      session_inactive: 'Session is inactive.'
      caption_not_allowed: 'Caption is not allowed for this message.'
    invalid_action:
      cannot_mark_message_as_read: 'Cannot mark outgoing or other message type as read. Please choose only an incoming message.'
      inactive_connected_account: 'Please activate account.'
    invalid_template:
      model_errors: '%{error}'
      something_went_wrong: 'Oops! Something went wrong. Please try again later.'
      invalid_connected_account: "Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent."
      invalid_template_status_to_update: 'Template with only Draft, Approved, Rejected, Paused or Inactive status can be updated.'
      invalid_template_status_to_deactivate: 'Template with only Draft, Approved, Rejected, or Paused status can be deactivated.'
      invalid_namespace_format: 'invalid format. Only alphanumeric and underscore allowed.'
      components_missing: 'Components missing. At least body component is required.'
      invalid_component_count: 'Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.'
      invalid_button_count: 'Buttons are limited to 1 phone number, copy code and 2 url buttons.'
      invalid_button_sequence: 'Please ensure all buttons are in sequence with unique number positions.'
      invalid_button_categorization: 'All quick reply buttons should be categorized at the start or end.'
      invalid_url_button_variables: 'Please ensure button url variables are either 1 or 2 and unique in case of more than one url button.'
      header_length_exceeded: 'Header text cannot be more than 60 characters'
      header_variable_invalid: 'Header text should contain at most 1 variable Only {{1}} is allowed'
      body_length_exceeded: 'Body text cannot be more than 1024 characters'
      footer_length_exceeded: 'Footer text cannot be more than 60 characters'
      footer_variable_not_allowed: 'Footer text cannot have variables'
      incorrect_phone_button_length: 'Phone number label must be restricted to 25 characters'
      invalid_phone_number: 'Phone number should be valid and include dial code'
      incorrect_url_button_length: 'URL label must be restricted to 25 characters and url to 2000 characters'
      invalid_url_text: 'Please enter valid URL'
      invalid_url_variable: 'Variable {{1}} or {{2}} in URL will only be appended at the end of url'
      incorrect_copy_code_button_length: 'Copy code text must be restricted to 15 characters'
      copy_code_invalid_text: 'Copy code text should only contain alphanumeric characters'
      incorrect_quick_reply_button_length: 'Quick reply text must be restricted to 25 characters'
      quick_reply_variable_not_allowed: 'Quick reply text cannot have variables'
      variables_mismatch: 'Fewer or  more variables than expected'
      duplicate_variables: 'Please ensure template variables are not duplicated'
      invalid_variable: 'Invalid variable mapping for variable %{variable_index} in %{component_type}'
      template_entity_and_preview_entity_mismatch: 'Template entity and preview entity mismatch'
      variables_unmapped: 'All template variables are not mapped'
      media_must_be_present: 'must be present when header type is media'
      template_is_not_approved: 'Whatsapp Template is not Approved'
      template_media_not_found: "Template media not found"
    template_media:
      invalid_file_type: 'Invalid file type'
