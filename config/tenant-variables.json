[{"id": 1, "internalName": "companyName", "displayName": "Tenant - Company Name", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 2, "internalName": "website", "displayName": "Tenant - Website", "standard": true, "type": "URL", "entity": "tenant", "groupBy": "Tenant"}, {"id": 3, "internalName": "accountName", "displayName": "Tenant - Account Name", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 4, "internalName": "industry", "displayName": "Tenant - Industry", "standard": true, "type": "PICK_LIST", "entity": "tenant", "groupBy": "Tenant"}, {"id": 5, "internalName": "address", "displayName": "Tenant - Address", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 6, "internalName": "city", "displayName": "Tenant - City", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 7, "internalName": "state", "displayName": "Tenant - State", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 8, "internalName": "country", "displayName": "Tenant - Country", "standard": true, "type": "PICK_LIST", "entity": "tenant", "groupBy": "Tenant"}, {"id": 9, "internalName": "zip", "displayName": "Tenant - Zipcode", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 10, "internalName": "id", "displayName": "Tenant - Tenant ID", "standard": true, "type": "NUMBER", "entity": "tenant", "groupBy": "Tenant"}, {"id": 11, "internalName": "taxIdentificationNumber", "displayName": "Tenant - Tax identification number", "standard": true, "type": "TEXT_FIELD", "entity": "tenant", "groupBy": "Tenant"}, {"id": 12, "internalName": "language", "displayName": "Tenant - Language", "standard": true, "type": "PICK_LIST", "entity": "tenant", "groupBy": "Tenant"}, {"id": 13, "internalName": "currency", "displayName": "Ten<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "standard": true, "type": "PICK_LIST", "entity": "tenant", "groupBy": "Tenant"}, {"id": 14, "internalName": "timezone", "displayName": "Tenant - Timezone", "standard": true, "type": "PICK_LIST", "entity": "tenant", "groupBy": "Tenant"}]