{"54": "Argentina", "55": "Brazil", "1": "North America", "213": "Rest of Africa", "244": "Rest of Africa", "229": "Rest of Africa", "267": "Rest of Africa", "226": "Rest of Africa", "257": "Rest of Africa", "237": "Rest of Africa", "235": "Rest of Africa", "291": "Rest of Africa", "251": "Rest of Africa", "241": "Rest of Africa", "220": "Rest of Africa", "233": "Rest of Africa", "245": "Rest of Africa", "225": "Rest of Africa", "254": "Rest of Africa", "266": "Rest of Africa", "231": "Rest of Africa", "218": "Rest of Africa", "261": "Rest of Africa", "265": "Rest of Africa", "223": "Rest of Africa", "222": "Rest of Africa", "212": "Rest of Africa", "258": "Rest of Africa", "264": "Rest of Africa", "227": "Rest of Africa", "242": "Rest of Africa", "250": "Rest of Africa", "221": "Rest of Africa", "232": "Rest of Africa", "252": "Rest of Africa", "211": "Rest of Africa", "249": "Rest of Africa", "268": "Rest of Africa", "255": "Rest of Africa", "228": "Rest of Africa", "216": "Rest of Africa", "256": "Rest of Africa", "260": "Rest of Africa", "93": "Rest of Asia Pacific", "61": "Rest of Asia Pacific", "880": "Rest of Asia Pacific", "855": "Rest of Asia Pacific", "86": "Rest of Asia Pacific", "852": "Rest of Asia Pacific", "81": "Rest of Asia Pacific", "856": "Rest of Asia Pacific", "976": "Rest of Asia Pacific", "977": "Rest of Asia Pacific", "64": "Rest of Asia Pacific", "675": "Rest of Asia Pacific", "63": "Rest of Asia Pacific", "65": "Rest of Asia Pacific", "94": "Rest of Asia Pacific", "886": "Rest of Asia Pacific", "992": "Rest of Asia Pacific", "66": "Rest of Asia Pacific", "993": "Rest of Asia Pacific", "998": "Rest of Asia Pacific", "84": "Rest of Asia Pacific", "355": "Rest of Central & Eastern Europe", "374": "Rest of Central & Eastern Europe", "994": "Rest of Central & Eastern Europe", "375": "Rest of Central & Eastern Europe", "359": "Rest of Central & Eastern Europe", "385": "Rest of Central & Eastern Europe", "420": "Rest of Central & Eastern Europe", "995": "Rest of Central & Eastern Europe", "30": "Rest of Central & Eastern Europe", "36": "Rest of Central & Eastern Europe", "371": "Rest of Central & Eastern Europe", "370": "Rest of Central & Eastern Europe", "373": "Rest of Central & Eastern Europe", "389": "Rest of Central & Eastern Europe", "48": "Rest of Central & Eastern Europe", "40": "Rest of Central & Eastern Europe", "381": "Rest of Central & Eastern Europe", "421": "Rest of Central & Eastern Europe", "386": "Rest of Central & Eastern Europe", "380": "Rest of Central & Eastern Europe", "591": "Rest of Latin America", "506": "Rest of Latin America", "1809": "Rest of Latin America", "1829": "Rest of Latin America", "1849": "Rest of Latin America", "593": "Rest of Latin America", "503": "Rest of Latin America", "502": "Rest of Latin America", "509": "Rest of Latin America", "504": "Rest of Latin America", "1658": "Rest of Latin America", "1876": "Rest of Latin America", "505": "Rest of Latin America", "507": "Rest of Latin America", "595": "Rest of Latin America", "1787": "Rest of Latin America", "1939": "Rest of Latin America", "598": "Rest of Latin America", "58": "Rest of Latin America", "973": "Rest of Middle East", "964": "Rest of Middle East", "962": "Rest of Middle East", "965": "Rest of Middle East", "961": "Rest of Middle East", "968": "Rest of Middle East", "974": "Rest of Middle East", "967": "Rest of Middle East", "43": "Rest of Western Europe", "32": "Rest of Western Europe", "45": "Rest of Western Europe", "358": "Rest of Western Europe", "353": "Rest of Western Europe", "47": "Rest of Western Europe", "351": "Rest of Western Europe", "46": "Rest of Western Europe", "41": "Rest of Western Europe", "56": "Chile", "57": "Colombia", "20": "Egypt", "33": "France", "49": "Germany", "91": "India", "62": "Indonesia", "972": "Israel", "39": "Italy", "60": "Malaysia", "52": "Mexico", "31": "Netherlands", "234": "Nigeria", "92": "Pakistan", "51": "Peru", "7": "Russia", "966": "Saudi Arabia", "27": "South Africa", "34": "Spain", "90": "Turkey", "971": "United Arab Emirates", "44": "United Kingdom"}