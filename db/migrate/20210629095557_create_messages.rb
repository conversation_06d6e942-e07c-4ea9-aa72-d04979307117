class CreateMessages < ActiveRecord::Migration[6.1]
  def change
    create_table :messages do |t|
      t.string :medium
      t.bigint :owner_id, null: false
      t.integer :direction
      t.text :content
      t.datetime :sent_at
      t.string :recipient_number
      t.string :sender_number
      t.integer :status
      t.datetime :delivered_at
      t.datetime :read_at

      t.timestamps
    end
  end
end
