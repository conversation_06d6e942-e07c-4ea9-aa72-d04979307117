class CreateConnectedAccounts < ActiveRecord::Migration[6.1]
  def change
    create_table :connected_accounts do |t|
      t.string :access_token
      t.string :name
      t.string :waba_number
      t.string :waba_id
      t.string :status
      t.string :phone_number_id
      t.string :display_name
      t.string :pin2fa
      t.bigint :tenant_id, null: false, index: true
      t.bigint :created_by_id, null: false
      t.bigint :updated_by_id, null: false
      t.text :entities_to_create, default: [], array: true
      t.timestamps
    end
  end
end
