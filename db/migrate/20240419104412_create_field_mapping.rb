class CreateFieldMapping < ActiveRecord::Migration[6.1]
  def change
    create_table :field_mappings do |t|
      t.string :entity_type
      t.bigint :campaign
      t.bigint :source
      t.string :sub_source
      t.string :utm_campaign
      t.string :utm_content
      t.string :utm_medium
      t.string :utm_source
      t.string :utm_term
      t.references :connected_account, null: false, foreign_key: true

      t.timestamps
    end
  end
end
