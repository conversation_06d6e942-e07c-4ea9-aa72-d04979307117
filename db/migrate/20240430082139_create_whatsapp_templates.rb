class CreateWhatsappTemplates < ActiveRecord::Migration[6.1]
  def change
    create_table :whatsapp_templates do |t|
      t.references :connected_account, null: false, foreign_key: true
      t.string :entity_type
      t.string :name
      t.string :category
      t.string :language
      t.string :status
      t.string :whatsapp_template_namespace
      t.string :whatsapp_template_id
      t.bigint :tenant_id, null: false
      t.bigint :created_by_id, null: false
      t.bigint :updated_by_id, null: false

      t.timestamps
    end
  end
end
