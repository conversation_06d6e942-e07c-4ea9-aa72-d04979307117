class CreateWhatsappTemplateComponents < ActiveRecord::Migration[6.1]
  def change
    create_table :whatsapp_template_components do |t|
      t.string :component_type
      t.string :component_format
      t.string :component_text
      t.string :component_value
      t.jsonb :content, default: {}
      t.bigint :tenant_id
      t.integer :position
      t.references :whatsapp_template, null: false, foreign_key: true

      t.timestamps
    end
  end
end
