class CreateEntitySessionLogs < ActiveRecord::Migration[6.1]
  def change
    create_table :entity_session_logs do |t|
      t.bigint :entity_id
      t.string :entity_type
      t.jsonb :phone_numbers_contacted, default: {}
      t.bigint :connected_account_id
      t.bigint :tenant_id, null: false

      t.timestamps
    end

    add_index :entity_session_logs, [:tenant_id, :connected_account_id, :entity_type], name: 'entity_sessions_index'
  end
end
