class CreateWhatsappCreditHistories < ActiveRecord::Migration[6.1]
  def change
    create_table :whatsapp_credit_histories do |t|
      t.bigint :tenant_id, null: false
      t.bigint :connected_account_id
      t.string :entry_type
      t.string :conversation_category
      t.bigint :start_time
      t.bigint :end_time
      t.bigint :conversation_count
      t.string :conversation_type
      t.string :phone_number
      t.string :country
      t.float  :value
      t.float  :balance

      t.timestamps
    end
  end
end
