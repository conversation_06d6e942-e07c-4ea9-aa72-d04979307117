class CreateShareRules < ActiveRecord::Migration[6.1]
  def change
    create_table :share_rules do |t|
      t.string :name
      t.string :description
      t.bigint :entity_id
      t.string :entity_type
      t.bigint :from_id
      t.string :from_type
      t.bigint :to_id
      t.string :to_type
      t.bigint :share_rule_id
      t.boolean :share_all_records, default: false
      t.jsonb :actions
      t.bigint :tenant_id, null: false

      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.references :updated_by, null: false, foreign_key: { to_table: :users }

      t.timestamps
    end
  end
end
