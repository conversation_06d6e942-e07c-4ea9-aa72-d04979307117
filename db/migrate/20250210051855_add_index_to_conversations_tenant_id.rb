class AddIndexToConversationsTenantId < ActiveRecord::Migration[6.1]
  def change
    add_index :conversations, :tenant_id, name: 'conversations_tenantId_idx', if_not_exists: true
    add_index :whatsapp_templates, :tenant_id, name: 'whatsapptemplates_tenantId_idx', if_not_exists: true
    add_index :whatsapp_credit_histories, :tenant_id, name: 'whatsappcredithistories_tenantId_idx', if_not_exists: true
    add_index :whatsapp_credit_histories, [:tenant_id, :conversation_category], name: 'whatsappcredithistories_tenantIdAndConversationCategory_idx', if_not_exists: true
    remove_index :look_ups, name: 'lookups_tenantid', if_exists: true
  end
end
