class CreateContactDealAssociations < ActiveRecord::Migration[6.1]
  def change
    create_table :contact_deal_associations do |t|
      t.bigint :contact_id, null: false
      t.bigint :deal_id, null: false
      t.bigint :tenant_id, null: false
      t.string :deal_name
      t.timestamps
    end

    add_index :contact_deal_associations, [:tenant_id, :contact_id, :deal_id],
              unique: true,
              name: 'idx_contact_deal_associations_tenant_contact_deal_unique'

    add_index :contact_deal_associations, [:tenant_id, :contact_id],
              name: 'idx_contact_deal_associations_tenant_contact'
  end
end
