podTemplate(
    containers: [
        containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat',
            ttyEnabled: true),
        containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
    ],
    imagePullSecrets: ['registry-credentials']) {
  properties([parameters(
      [string(name: 'dockerImageTag', description: 'Docker image tag to deploy'),
       string(name: 'branchName', defaultValue: 'dev', description: 'Branch being deployed')
       ]
      )
    ])

  currentBuild.description = "branch ${params.branchName}"
  node(POD_LABEL) {
    try{
      stage('Approval for Deployment') {
        userInput = input(id: 'confirm', message: 'Do you wish to deploy to PROD environment?',
            parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current build in PROD environment', name: 'confirm']])
      }
      container('helm') {
        withCredentials([[$class       : 'FileBinding',
                          credentialsId: 'prod-kubeconfig',
                          variable     : 'KUBECONFIG'],
                         [$class       : 'StringBinding',
                          credentialsId: 'sd-charts-github-api-token',
                          variable     : 'API_TOKEN']]) {
          stage('Add Helm repository') {
            sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
                label: 'Add stable helm repo'
            sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/master/'",
                label: 'Add helm repo'
            sh script: 'helm repo list', label: 'List available helm repos'
          }
          withCredentials([[$class       : 'StringBinding',
                              credentialsId: 'prod-application-namespace',
                              variable     : 'APP_NAMESPACE'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-postgres-username',
                              variable     : 'POSTGRES_USERNAME'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-postgres-password',
                              variable     : 'POSTGRES_PASSWORD'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-postgres-host',
                              variable     : 'POSTGRES_HOST'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-postgres-port',
                              variable     : 'POSTGRES_PORT'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-postgres-flags',
                              variable     : 'POSTGRES_FLAGS'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-rabbitmq-username',
                              variable     : 'RABBITMQ_USERNAME'],
                            [$class       : 'StringBinding',
                              credentialsId: 'prod-env-rabbitmq-password',
                              variable     : 'RABBITMQ_PASSWORD'],
                            [$class       : 'StringBinding',
                              credentialsId: 'sd-message-rails-master-key',
                              variable     : 'RAILS_MASTER_KEY'],
                            [
                              $class       : 'StringBinding',
                              credentialsId: 'prod-whatsapp-facebook-client-id',
                              variable     : 'FACEBOOK_CLIENT_ID'
                            ],
			                      [
                              $class       : 'StringBinding',
                              credentialsId: 'prod-whatsapp-facebook-client-secret',
                              variable     : 'FACEBOOK_CLIENT_SECRET'
                            ],
                            [
                              $class       : 'StringBinding',
                              credentialsId: 'prod-email-credential-encryption-secret',
                              variable     : 'MESSAGE_CREDENTIAL_ENCRYPTION_SECRET'
                            ],
			                      [
                              $class       : 'StringBinding',
                              credentialsId: 'email-credential-encryption-iv',
                              variable     : 'MESSAGE_CREDENTIAL_ENCRYPTION_IV'
                            ],
                            [
                              $class       : 'StringBinding',
                              credentialsId: 'interakt-partner-token-prod',
                              variable     : 'INTERAKT_PARTNER_TOKEN'
                            ],
                            [
                              $class       : 'StringBinding',
                              credentialsId: 'interakt-solution-id-prod',
                              variable     : 'INTERAKT_SOLUTION_ID'
                            ]
                          ]) {
            stage('Deploy') {
              echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-message:${params.dockerImageTag}"
              sh script: "helm upgrade --install sd-message sd-charts/sd-message " +
                  "--set " +
                  "appConfig.rabbitmq.password=${RABBITMQ_PASSWORD}," +
                  "appConfig.rabbitmq.username=${RABBITMQ_USERNAME}," +
                  "appConfig.postgres.hostname=${POSTGRES_HOST}," +
                  "appConfig.postgres.port=${POSTGRES_PORT}," +
                  "appConfig.postgres.flags='${POSTGRES_FLAGS}'," +
                  "appConfig.postgres.username=${POSTGRES_USERNAME}," +
                  "appConfig.postgres.password=${POSTGRES_PASSWORD}," +
                  "image.tag=${params.dockerImageTag}," +
                  "namespace=${APP_NAMESPACE}," +
                  "appConfig.credentials.masterKey=${RAILS_MASTER_KEY}," +
                  "appConfig.kylas.uiHost=https://app.kylas.io," +
                  "appConfig.s3.bucketName='prod-message'," +
                  "appConfig.facebook.clientId='${FACEBOOK_CLIENT_ID}'," +
                  "appConfig.facebook.clientSecret='${FACEBOOK_CLIENT_SECRET}'," +
                  "appConfig.credential.encryption.secret='${MESSAGE_CREDENTIAL_ENCRYPTION_SECRET}'," +
                  "appConfig.credential.encryption.iv='${MESSAGE_CREDENTIAL_ENCRYPTION_IV}'," +
                  "appConfig.interakt.partnerToken='${INTERAKT_PARTNER_TOKEN}'," +
                  "appConfig.interakt.solutionId='${INTERAKT_SOLUTION_ID}'," +
                  "deployment.annotations.buildNumber=${currentBuild.number} " +
                  "--wait",
                  label: 'Install helm release'
            }
          }
        }
      }
      container('curl') {
        stage('Refresh Gateway routes') {
          sh script: 'curl -X POST \\\n' +
              '  https://api.kylas.io/actuator/gateway/refresh \\\n' +
              '  -H \'Accept: application/json\' \\\n' +
              '  -H \'Host: api.kylas.io\' \\\n' +
              '  -H \'cache-control: no-cache\'', label: 'Force refresh routes cache'
        }
      }
    }
    catch (err) {
      def user = err.getCauses()[0].getUser()
      userInput = false
      echo "Aborted by: [${user}]"
    }
  }
}
