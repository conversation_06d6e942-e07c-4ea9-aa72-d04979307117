namespace :conversations do
  desc "Populate lastActivityAt for all conversations based on last message sentAt"
  task :populate_last_activity_at, [:tenant_id] => [:environment] do |_task, args|
    tenant_id = args[:tenant_id]


    Rails.logger.info "Starting to populate lastActivityAt for conversations..."

    conversations = Conversation.all

    if tenant_id.present?
      conversations = conversations.where(tenant_id: tenant_id)
    end

    conversations.find_each do |conversation|
      if conversation.last_activity_at.present?
        Rails.logger.info "lastActivityAt already present for conversation #{conversation.id}"
        next
      end

      last_message = Message.where(conversation_id: conversation.id).order(sent_at: :desc).first
      
      if last_message
        conversation.update(last_activity_at: last_message.sent_at)
        Rails.logger.info "Updated conversation #{conversation.id} with lastActivityAt: #{last_message.sent_at}"
      else
        Rails.logger.info "No messages found for conversation #{conversation.id}"
      end
    end
    
    Rails.logger.info "Finished populating lastActivityAt for conversations"
  end
end

# To run this task with a specific tenant_id:
# RAILS_ENV=<Environment> bundle exec rake "conversations:populate_last_activity_at[<tenant_id>]"
#
# To run for all tenants:
# RAILS_ENV=<Environment> bundle exec rake "conversations:populate_last_activity_at"
