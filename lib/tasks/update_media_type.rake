namespace :whatsapp_template_media_type do
  desc 'Update media_type to STATIC for WhatsApp template components with header type and media formats'
  task :update_media_type, [:tenant_id] => [:environment] do |_task, args|
    tenant_id = args[:tenant_id]

    Rails.logger.info "Starting to update media_type for WhatsApp template components..."

    components = WhatsappTemplateComponent.all
    media_formats = [IMAGE, VIDEO, DOCUMENT]

    if tenant_id.present?
      components = components.where(tenant_id: tenant_id)
    end

    components.where(component_type: HEADER, component_format: media_formats).find_each do |component|
      begin
        if component.media_type.present?
          Rails.logger.info "media_type already present for component #{component.id}"
          next
        end

        component.update(media_type: STATIC)
        Rails.logger.info "Updated component #{component.id} with media_type: STATIC"
      rescue => e
        Rails.logger.error "Error updating component #{component.id}: #{e.message}"
      end
    end

    Rails.logger.info "Finished updating media_type for WhatsApp template components"
  end
end

# To run this task with a specific tenant_id:
# RAILS_ENV=<Environment> bundle exec rake "whatsapp_template_media_type:update_media_type[<tenant_id>]"
#
# To run for all tenants:
# RAILS_ENV=<Environment> bundle exec rake "whatsapp_template_media_type:update_media_type" 