namespace :update_message_type do
  desc 'Update message type of all messages based on medium'
  task execute_task: :environment do
    whatsapp_mediums = ['Netcore', 'Wati', 'Interakt', '<PERSON><PERSON>hup', '<PERSON>plet', '<PERSON>allabox', 'Telinfy', 'whatsapp', 'GreenAds Global', 'Twilio Whatsapp']
    sms_mediums = ['Keep In Touch', 'Go2market', 'Onextel', 'Msgadvert', 'Pinnacle', 'Twilio SMS', 'Pinnacle New', 'Sakari']
    whatsapp_updated = Message.where(medium: whatsapp_mediums, message_type: nil).update_all(message_type: WHATSAPP)
    sms_updated = Message.where(medium: sms_mediums, message_type: nil).update_all(message_type: SMS)

    puts "Updated messages for whatsapp = #{whatsapp_updated} and for sms = #{sms_updated}"
  end
end
