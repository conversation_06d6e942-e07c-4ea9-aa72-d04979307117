# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenFor1amScheduler do
  include ActiveJob::TestHelper

  let(:routing_key) { SCHEDULER_1AM_QUEUE }
  let(:payload) { {}.to_json }
  let(:user){ create(:user) }

  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SCHEDULER_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SCHEDULER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context '1 am event' do
        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(SCHEDULER_EXCHANGE, SCHEDULER_1AM_EVENT, SCHEDULER_1AM_QUEUE)
            .and_yield(payload.to_s)
        end

        it 'schedules update whatsapp credits job' do
          expect{ Listeners::ListenFor1amScheduler.listen }.to have_enqueued_job(UpdateWhatsappCreditsJob).on_queue('default')
        end
      end
    end
  end
end
