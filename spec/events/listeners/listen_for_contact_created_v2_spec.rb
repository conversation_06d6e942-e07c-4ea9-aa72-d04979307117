# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForContactCreatedV2 do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SALES_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SALES_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'contact created v2 event' do
        let(:user){ create(:user) }
        let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id) }
        let(:second_connected_account){ create(:connected_account, tenant_id: user.tenant_id) }
        let!(:conversation){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+************') }
        let!(:conversation_2){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+************') }
        let!(:conversation_3){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: second_connected_account.id, phone_number: '+************') }

        let(:routing_key)              { CONTACT_CREATED_V2_QUEUE }
        let(:payload) do
          {
            'entity' => {
              'userId' => user.id,
              'tenantId' => user.tenant_id,
              'id' => 1234,
              'firstName' => 'test',
              'lastName' => 'user',
              'ownerId' => {
                'id' => user.id,
                'name' => user.name
              },
              'phoneNumbers': [
                {
                  'type' => 'MOBILE',
                  'code' => 'IN',
                  'value' => '**********',
                  'dialCode' => '+91',
                  'primary' => true
                },
                {
                  'type' => 'WORK',
                  'code' => 'IN',
                  'value' => '**********',
                  'dialCode' => '+91',
                  'primary' => false
                }
              ]
            }
          }.to_json
        end

        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(SALES_EXCHANGE, CONTACT_CREATED_V2_EVENT, CONTACT_CREATED_V2_QUEUE)
            .and_yield(payload.to_s)
        end

        it 'associates conversations with entity' do
          expect {
            described_class.listen
          }.to change(LookUp, :count).by(2)
          .and change(ConversationLookUp, :count).by(3)

          expect(LookUp.last.name).to eq('test user')
        end
      end
    end
  end
end
