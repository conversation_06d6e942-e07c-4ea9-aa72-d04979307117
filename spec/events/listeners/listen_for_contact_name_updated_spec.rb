# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForContactNameUpdated do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SALES_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SALES_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'contact name updated event' do
        let(:user){ create(:user) }
        let(:look_up){ create(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id) }
        let(:routing_key)              { CONTACT_NAME_UPDATED_QUEUE }
        let(:payload) do
          {
            'contactId' => look_up.entity_id,
            'tenantId' => user.tenant_id,
            'firstName' => 'test',
            'lastName' => 'user'
          }.to_json
        end

        before do
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(SALES_EXCHANGE, CONTACT_NAME_UPDATED_EVENT, CONTACT_NAME_UPDATED_QUEUE)
            .and_yield(payload.to_s)
        end

        it 'updates lookups name' do
          described_class.listen
        
          expect(look_up.reload.name).to eq('test user')
        end
      end
    end
  end
end
