# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForContactPhoneNumbersUpdatedV2 do
  describe '#listen' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SALES_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SALES_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'contact phone numbers updated v2 event' do
        let(:user){ create(:user) }
        let(:token){ FactoryBot.build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
        let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id) }
        let(:second_connected_account){ create(:connected_account, tenant_id: user.tenant_id) }
        let!(:conversation){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+************') }
        let!(:conversation_2){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+************') }
        let!(:conversation_3){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: second_connected_account.id, phone_number: '+************') }

        let(:routing_key)              { CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE }

        before do
          allow(GenerateToken).to receive(:call).and_return(token.token)
          
          stub_request(:get, SERVICE_SALES + "/v1/contacts/1234").
          with(
            headers: {
            "Authorization" => "Bearer #{ token.token }"
            }).
          to_return(status: 200, body: { "id": 1234, "ownerId": 1, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})
        end

        def get_payload(old_phone_numbers, new_phone_numbers)
          {
            'entity' => {
              'userId' => user.id,
              'tenantId' => user.tenant_id,
              'id' => 1234,
              'firstName' => 'test',
              'lastName' => 'user',
              'phoneNumbers': new_phone_numbers
            },
            'oldEntity' => {
              'userId' => user.id,
              'tenantId' => user.tenant_id,
              'id' => 1234,
              'phoneNumbers': old_phone_numbers
            }
          }.to_json
        end

        context 'when phone_numbers are added' do
          before do
            new_phone_numbers = [
              {
                "id": 211769,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999998",
                "dialCode": "+91",
                "primary": true
              },
              {
                "id": 211768,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999997",
                "dialCode": "+91",
                "primary": false
              }
            ]

            allow(RabbitmqConnection).to receive(:subscribe)
              .with(SALES_EXCHANGE, CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT, CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE)
              .and_yield(get_payload([], new_phone_numbers).to_s)
          end

          it 'associates conversations with added phone numbers with entity' do
            expect {
              described_class.listen
            }.to change(LookUp, :count).by(2)
            .and change(ConversationLookUp, :count).by(3)

            expect(LookUp.last.name).to eq('test user')
          end
        end

        context 'when phone_numbers are removed' do
          before do
            old_phone_numbers = [
              {
                "id": 211769,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999998",
                "dialCode": "+91",
                "primary": true
              },
              {
                "id": 211768,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999997",
                "dialCode": "+91",
                "primary": false
              }
            ]

            allow(RabbitmqConnection).to receive(:subscribe)
              .with(SALES_EXCHANGE, CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT, CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE)
              .and_yield(get_payload(old_phone_numbers, []).to_s)
          end

          it 'returns without change' do
            expect {
              described_class.listen
            }.to change(LookUp, :count).by(0)
            .and change(ConversationLookUp, :count).by(0)
          end
        end

        context 'when phone_numbers are empty' do
          before do
            allow(RabbitmqConnection).to receive(:subscribe)
              .with(SALES_EXCHANGE, CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT, CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE)
              .and_yield(get_payload(nil, []).to_s)
          end

          it 'returns without change' do
            expect {
              described_class.listen
            }.to change(LookUp, :count).by(0)
            .and change(ConversationLookUp, :count).by(0)
          end
        end

        context 'when some phone_numbers are added and some are removed' do
          before do
            old_phone_numbers = [
              {
                "id": 211769,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999998",
                "dialCode": "+91",
                "primary": true
              },
            ]

            new_phone_numbers = [
              {
                "id": 211769,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999998",
                "dialCode": "+91",
                "primary": true
              },
              {
                "id": 211770,
                "type": "MOBILE",
                "code": "IN",
                "value": "9898787676",
                "dialCode": "+91",
                "primary": false
              },
              {
                "id": 211775,
                "type": "MOBILE",
                "code": "IN",
                "value": "9999999997",
                "dialCode": "+91",
                "primary": true
              }
            ]

            allow(RabbitmqConnection).to receive(:subscribe)
              .with(SALES_EXCHANGE, CONTACT_PHONE_NUMBERS_UPDATED_V2_EVENT, CONTACT_PHONE_NUMBERS_UPDATED_V2_QUEUE)
              .and_yield(get_payload(old_phone_numbers, new_phone_numbers).to_s)
          end

          it 'associates conversations with added phone numbers with entity' do
            expect {
              described_class.listen
            }.to change(LookUp, :count).by(1)
            .and change(ConversationLookUp, :count).by(2)
          end
        end
      end
    end
  end
end
