# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForContactReassign do
  describe '#listen' do
    context 'when contact is reassigned' do
      let(:contact_lookup) { create(:look_up, entity_type: LOOKUP_CONTACT) }
      let(:payload) { { "entityId" => contact_lookup.entity_id, "newOwnerId" => contact_lookup.owner_id + 1, "oldOwnerId" => contact_lookup.owner_id, "tenantId" => contact_lookup.tenant_id } }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.sales', 'sales.contact.owner_updated', 'q.sales.contact.owner_updated.message')
          .and_yield(payload.to_json)
      end

      it 'listens for event and updates owner' do
        expect(Rails.logger).to receive(:info).with("Received message for sales.contact.owner_updated tenant id #{payload['tenantId']} entity id #{payload['entityId']} new owner id #{payload['newOwnerId']}")
        described_class.listen

        expect(contact_lookup.reload.owner_id).to eq(payload['newOwnerId'])
      end
    end
  end
end
