# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForDailyScheduler do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SCHEDULER_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SCHEDULER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
      end

      context '3 am event' do
        let(:routing_key) { SCHEDULER_3AM_QUEUE }
        let(:payload) do {}.to_json
        end

        before do
          unused_media1 = create(:template_media, file_name: 'file_1.png')
          unused_media2 = create(:template_media, file_name: 'file_2.png')
          connected_account = create(:connected_account)
          template = create(:whatsapp_template, connected_account: connected_account)
          used_media = create(:template_media, tenant_id: connected_account.tenant_id, file_name: 'file_3.png')
          header_component = template.components.find_by(component_type: 'HEADER').update!(component_value: used_media.id, component_format: 'IMAGE', media_type: 'STATIC')

          allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
          s3_instance = instance_double(S3::DeleteFileFromS3)
          allow(S3::DeleteFileFromS3).to receive(:new).with(['file_1.png', 'file_2.png'], S3_ATTACHMENT_BUCKET).and_return(s3_instance)
          allow(s3_instance).to receive(:call).and_return(nil)

          @soft_deleted_conversation = create(:conversation)
          @soft_deleted_conversation.soft_delete!
          @message = create(:message, conversation_id: @soft_deleted_conversation.id, tenant_id: @soft_deleted_conversation.tenant_id)
          
          allow(RabbitmqConnection).to receive(:subscribe)
            .with(SCHEDULER_EXCHANGE, SCHEDULER_3AM_EVENT, SCHEDULER_3AM_QUEUE)
            .and_yield(payload.to_s)
        end

        it 'deletes unused media' do
          Listeners::ListenForDailyScheduler.listen
          expect(TemplateMedia.count).to eq(1)
          expect(TemplateMedia.last.file_name).to eq('file_3.png')
        end

        it 'cleans up soft-deleted conversations' do
          Listeners::ListenForDailyScheduler.listen
          

          expect(Conversation.soft_deleted.count).to eq(0)
          expect(Conversation.unscoped.count).to eq(0)
          expect(Message.count).to eq(0)
        end

        context 'when cleaning up soft-deleted messages' do
          let(:user) { create(:user) }
          let!(:message1) { create(:message, tenant_id: user.tenant_id) }
          let!(:message2) { create(:message, tenant_id: user.tenant_id) }
          let!(:soft_deleted_message1) { create(:message, tenant_id: user.tenant_id, deleted_at: Time.current) }
          let!(:soft_deleted_message2) { create(:message, tenant_id: user.tenant_id, deleted_at: Time.current) }

          it 'processes soft-deleted messages' do
            expect { Listeners::ListenForDailyScheduler.listen }.to change(Message.unscoped, :count).by(-3)
            expect(Message.soft_deleted.count).to eq(0)
          end

          context 'when an error occurs during cleanup' do
            before do
              allow(Message::DeleteMessageService).to receive(:new).and_raise(StandardError.new('Cleanup failed'))
            end

            it 'continues processing other messages' do
              expect { Listeners::ListenForDailyScheduler.listen }.not_to raise_error
            end
          end
        end
      end
    end
  end
end
