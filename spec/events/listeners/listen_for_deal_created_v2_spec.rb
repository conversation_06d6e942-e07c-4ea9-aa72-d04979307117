# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForDealCreatedV2 do
  let(:tenant_id) { 99 }
  let(:deal_id) { 1 }
  let(:deal_name) { 'deal 1' }

  let(:base_payload) do
    {
      "entity" => {
        "id" => deal_id,
        "name" => deal_name,
        "ownedBy" => {
          "id" => 12,
          "name" => "Steve Rogers"
        },
        "estimatedValue" => {
          "currencyId" => 413,
          "value" => 75000.99
        },
        "actualValue" => {
          "currencyId" => 413,
          "value" => 2000
        },
        "estimatedClosureOn" => "2019-10-01T05:43:45.717Z",
        "actualClosureDate" => nil,
        "associatedContacts" => [],
        "product" => {
          "id" => 301,
          "name" => "Product 301"
        },
        "pipeline" => {
          "id" => 111,
          "name" => "First Pipeline",
          "stage" => {
            "id" => 21,
            "name" => "Stage 1"
          }
        },
        "forecastingType" => "OPEN",
        "company" => {
          "id" => 201,
          "name" => "company 2"
        },
        "createdAt" => nil,
        "updatedAt" => nil,
        "createdBy" => {
          "id" => 1,
          "name" => "Om"
        },
        "updatedBy" => {
          "id" => 1,
          "name" => "Om"
        },
        "campaign" => {
          "id" => 2,
          "name" => "Facebook"
        },
        "source" => {
          "id" => 1,
          "name" => "Google"
        }
      },
      "oldEntity" => nil,
      "metadata" => {
        "tenantId" => tenant_id,
        "userId" => 12,
        "entityType" => "DEAL",
        "entityId" => deal_id,
        "entityAction" => "CREATED",
        "workflowId" => nil,
        "executedWorkflows" => []
      }
    }
  end

  describe '.listen' do
    context 'when deal is created with associated contacts' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 101, "name" => "contact 1" },
            { "id" => 102, "name" => "contact 2" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'creates new contact deal associations' do
        expect {
          described_class.listen
        }.to change { ContactDealAssociation.count }.by(2)

        association_1 = ContactDealAssociation.find_by(tenant_id: tenant_id, contact_id: 101, deal_id: deal_id)
        association_2 = ContactDealAssociation.find_by(tenant_id: tenant_id, contact_id: 102, deal_id: deal_id)

        expect(association_1).to be_present
        expect(association_1.deal_name).to eq(deal_name)
        expect(association_2).to be_present
        expect(association_2.deal_name).to eq(deal_name)
      end
    end

    context 'when deal is created without associated contacts' do
      let(:payload) { base_payload }

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'does not create any contact deal associations' do
        expect {
          described_class.listen
        }.not_to change { ContactDealAssociation.count }
      end
    end

    context 'when deal is created with empty associated contacts array' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = []
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'does not create any contact deal associations' do
        expect {
          described_class.listen
        }.not_to change { ContactDealAssociation.count }
      end
    end

    context 'when association already exists' do
      let!(:existing_association) do
        create(:contact_deal_association, tenant_id: tenant_id, contact_id: 101, deal_id: deal_id, deal_name: 'old name')
      end

      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 101, "name" => "contact 1" },
            { "id" => 102, "name" => "contact 2" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'does not create duplicate associations but creates new ones' do
        expect {
          described_class.listen
        }.to change { ContactDealAssociation.count }.by(1)

        # Existing association should remain unchanged
        existing_association.reload
        expect(existing_association.deal_name).to eq('old name')

        # New association should be created
        association_2 = ContactDealAssociation.find_by(tenant_id: tenant_id, contact_id: 102, deal_id: deal_id)
        expect(association_2).to be_present
        expect(association_2.deal_name).to eq(deal_name)
      end
    end

    context 'when an error occurs' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 101, "name" => "contact 1" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
        allow(ContactDealAssociation).to receive(:find_or_create_by).and_raise(StandardError.new('Database error'))
      end

      it 'logs the error and continues' do
        expect(Rails.logger).to receive(:error).with(/Database error/)

        expect { described_class.listen }.not_to raise_error
      end
    end

    context 'when deal_id is missing' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["id"] = nil
          p["entity"]["associatedContacts"] = [
            { "id" => 101, "name" => "contact 1" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'does not create any associations' do
        expect {
          described_class.listen
        }.not_to change { ContactDealAssociation.count }
      end
    end

    context 'when tenant_id is missing' do
      let(:payload) do
        base_payload.tap do |p|
          p["metadata"]["tenantId"] = nil
          p["entity"]["associatedContacts"] = [
            { "id" => 101, "name" => "contact 1" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'does not create any associations' do
        expect {
          described_class.listen
        }.not_to change { ContactDealAssociation.count }
      end
    end
  end
end
