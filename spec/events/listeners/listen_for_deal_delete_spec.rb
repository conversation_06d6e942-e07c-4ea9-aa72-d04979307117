# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForDealDelete do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic DEAL_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(DEAL_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
      end

      context 'deal deleted event' do
        let(:routing_key)              { DEAL_DELETED_QUEUE }
        let(:exclusively_related_deal) { create(:look_up, entity_type: LOOKUP_DEAL, tenant_id: @user.tenant_id) }
        let(:another_contact)          { create(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: @user.tenant_id) }
        let(:contact_lookup)           { create(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: @user.tenant_id) }
        let(:payload)                  do
          { 'userId' => @user.id, 'tenantId' => @user.tenant_id, 'id' => exclusively_related_deal.entity_id }.to_json
        end

        before do
          @message_1 = create(:message, owner: @user, tenant_id: @user.tenant_id)
          @message_3 = create(:message, owner: @user, tenant_id: @user.tenant_id)

          @message_3.related_to << exclusively_related_deal

          allow(RabbitmqConnection).to receive(:subscribe)
            .with(DEAL_EXCHANGE, DEAL_DELETED_EVENT, DEAL_DELETED_QUEUE)
            .and_yield(payload.to_s)
        end

        it 'removes association correctly' do
          expect(Publishers::MessageDeleted).to receive(:call)
          @message_1.related_to << exclusively_related_deal
          @message_1.related_to << contact_lookup
          rl = @message_1.related_look_ups.last
          rl.recipient = true
          rl.save!

          Listeners::ListenForDealDelete.listen
          message_3 = Message.find_by_id(@message_3.id)
          expect(message_3).to be_nil

          message_1 = Message.find_by_id(@message_1.id)
          expect(message_1).to be_present
          expect(message_1.related_to.count).to eq(1)
          expect(message_1.related_to.ids).to match_array([contact_lookup.id])
          expect(message_1.recipients.count).to eq(1)
          expect(message_1.recipients.ids).to match_array([contact_lookup.id])

          expect(LookUp.find_by(entity_type: exclusively_related_deal.entity)).to be_nil
        end

        it "deletes all messages when it's not associated with another lookup" do
          expect(Publishers::MessageDeleted).to receive(:call).exactly(:twice)

          @message_1.related_to << exclusively_related_deal
          rl = @message_1.related_look_ups.last
          rl.recipient = true
          rl.save!

          message_1_id = @message_1.id
          message_3_id = @message_3.id

          Listeners::ListenForDealDelete.listen
          expect(Message.exists?(message_1_id)).to be_falsey
          expect(Message.exists?(message_3_id)).to be_falsey
          expect(LookUp.exists?(entity_type: exclusively_related_deal.entity)).to be_falsey
        end

        context 'contact deal associations' do
          let!(:contact_deal_association_1) do
            create(:contact_deal_association,
                   tenant_id: @user.tenant_id,
                   deal_id: exclusively_related_deal.entity_id,
                   contact_id: 111,
                   deal_name: 'Deal to be deleted')
          end

          let!(:contact_deal_association_2) do
            create(:contact_deal_association,
                   tenant_id: @user.tenant_id,
                   deal_id: exclusively_related_deal.entity_id,
                   contact_id: 222,
                   deal_name: 'Deal to be deleted')
          end

          let!(:other_deal_association) do
            create(:contact_deal_association,
                   tenant_id: @user.tenant_id,
                   deal_id: 999,
                   contact_id: 333,
                   deal_name: 'Other Deal')
          end

          it 'deletes contact deal associations for the deleted deal' do
            expect(Publishers::MessageDeleted).to receive(:call)
            expect {
              Listeners::ListenForDealDelete.listen
            }.to change { ContactDealAssociation.count }.by(-2)

            expect(ContactDealAssociation.exists?(contact_deal_association_1.id)).to be_falsey
            expect(ContactDealAssociation.exists?(contact_deal_association_2.id)).to be_falsey

            expect(ContactDealAssociation.exists?(other_deal_association.id)).to be_truthy
          end
        end
      end
    end
  end
end
