# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForDealNameUpdated do
  let(:tenant_id) { 123 }
  let(:deal_id) { 456 }
  let(:new_deal_name) { 'Updated Deal Name' }
  
  let(:payload) do
    {
      "id" => deal_id,
      "name" => new_deal_name,
      "tenantId" => tenant_id,
      "ownerId" => 59
    }.to_json
  end

  let!(:contact_deal_association_1) do
    create(:contact_deal_association, 
           tenant_id: tenant_id, 
           deal_id: deal_id, 
           contact_id: 111, 
           deal_name: 'Old Deal Name')
  end

  let!(:contact_deal_association_2) do
    create(:contact_deal_association, 
           tenant_id: tenant_id, 
           deal_id: deal_id, 
           contact_id: 222, 
           deal_name: 'Old Deal Name')
  end

  let!(:other_deal_association) do
    create(:contact_deal_association, 
           tenant_id: tenant_id, 
           deal_id: 999, 
           contact_id: 333, 
           deal_name: 'Other Deal')
  end

  before do
    allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload)
  end

  describe '.listen' do
    it 'updates deal names for all contact deal associations with matching deal_id and tenant_id' do
      expect(Rails.logger).to receive(:info).with(/Received message for #{DEAL_NAME_UPDATED_EVENT}/)
      expect(Rails.logger).to receive(:info).with(/Updated 2 contact deal associations/)

      described_class.listen

      contact_deal_association_1.reload
      contact_deal_association_2.reload
      other_deal_association.reload

      expect(contact_deal_association_1.deal_name).to eq(new_deal_name)
      expect(contact_deal_association_2.deal_name).to eq(new_deal_name)
      expect(other_deal_association.deal_name).to eq('Other Deal')
    end
  end
end
