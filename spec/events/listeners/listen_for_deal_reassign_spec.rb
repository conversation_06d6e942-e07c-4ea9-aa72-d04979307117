# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForDealReassign do
  describe '#listen' do
    context 'when deal is reassigned' do
      let(:deal_lookup) { create(:look_up, entity_type: LOOKUP_DEAL) }
      let(:payload) { { "id" => deal_lookup.entity_id, "oldOwnerId" => 1234, "deal" => { "ownerId" => 1235, "tenantId" => deal_lookup.tenant_id } } }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.deal', 'deal.reassigned', 'q.deal.reassigned.message')
          .and_yield(payload.to_json)
      end

      it 'listens for event and updates owner' do
        expect(Rails.logger).to receive(:info).with("Received message for deal.reassigned tenant id #{payload.dig('deal', 'tenantId')} entity id #{payload['id']} new owner id #{payload.dig('deal', 'ownerId')}")
        described_class.listen

        expect(deal_lookup.reload.owner_id).to eq(1235)
      end
    end
  end
end
