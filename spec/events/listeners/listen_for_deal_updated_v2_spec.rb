# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForDealUpdatedV2 do
  let(:tenant_id) { 99 }
  let(:deal_id) { 1 }
  let(:deal_name) { 'Updated Deal Name' }
  let(:old_deal_name) { 'Old Deal Name' }

  let(:base_payload) do
    {
      "entity" => {
        "id" => deal_id,
        "name" => deal_name,
        "associatedContacts" => []
      },
      "oldEntity" => {
        "id" => deal_id,
        "name" => old_deal_name,
        "associatedContacts" => []
      },
      "metadata" => {
        "tenantId" => tenant_id,
        "userId" => 12,
        "entityType" => "DEAL",
        "entityId" => deal_id,
        "entityAction" => "UPDATED"
      }
    }
  end

  before do
    allow(RabbitmqConnection).to receive(:subscribe).and_yield(base_payload.to_json)
  end

  describe '.listen' do
    context 'when contacts are added to deal' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 101, "name" => "contact 1" },
            { "id" => 102, "name" => "contact 2" }
          ]
          p["oldEntity"]["associatedContacts"] = []
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'creates new contact deal associations' do
        expect {
          described_class.listen
        }.to change { ContactDealAssociation.count }.by(2)

        association_1 = ContactDealAssociation.find_by(tenant_id: tenant_id, contact_id: 101, deal_id: deal_id)
        association_2 = ContactDealAssociation.find_by(tenant_id: tenant_id, contact_id: 102, deal_id: deal_id)

        expect(association_1).to be_present
        expect(association_1.deal_name).to eq(deal_name)
        expect(association_2).to be_present
        expect(association_2.deal_name).to eq(deal_name)
      end
    end

    context 'when contacts are removed from deal' do
      let!(:existing_association_1) do
        create(:contact_deal_association, tenant_id: tenant_id, contact_id: 103, deal_id: deal_id, deal_name: old_deal_name)
      end
      let!(:existing_association_2) do
        create(:contact_deal_association, tenant_id: tenant_id, contact_id: 104, deal_id: deal_id, deal_name: old_deal_name)
      end

      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = []
          p["oldEntity"]["associatedContacts"] = [
            { "id" => 103, "name" => "contact 1" },
            { "id" => 104, "name" => "contact 2" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'removes contact deal associations' do
        expect {
          described_class.listen
        }.to change { ContactDealAssociation.count }.by(-2)

        expect(ContactDealAssociation.exists?(existing_association_1.id)).to be_falsey
        expect(ContactDealAssociation.exists?(existing_association_2.id)).to be_falsey
      end
    end

    context 'when contacts are both added and removed' do
      let!(:existing_association) do
        create(:contact_deal_association, tenant_id: tenant_id, contact_id: 105, deal_id: deal_id, deal_name: old_deal_name)
      end

      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 106, "name" => "contact 2" }
          ]
          p["oldEntity"]["associatedContacts"] = [
            { "id" => 105, "name" => "contact 1" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'adds new contacts and removes old contacts correctly' do
        expect {
          described_class.listen
        }.to change { ContactDealAssociation.count }.by(0) # +1 added, -1 removed

        expect(ContactDealAssociation.exists?(existing_association.id)).to be_falsey
        
        association_106 = ContactDealAssociation.find_by(tenant_id: tenant_id, contact_id: 106, deal_id: deal_id)
        expect(association_106).to be_present
      end
    end

    context 'when no associated contacts change' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 109, "name" => "contact 1" }
          ]
          p["oldEntity"]["associatedContacts"] = [
            { "id" => 109, "name" => "contact 1" }
          ]
          p["entity"]["name"] = old_deal_name
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
      end

      it 'does not make any changes' do
        expect {
          described_class.listen
        }.not_to change { ContactDealAssociation.count }
      end
    end

    context 'when an error occurs' do
      let(:payload) do
        base_payload.tap do |p|
          p["entity"]["associatedContacts"] = [
            { "id" => 106, "name" => "contact 2" }
          ]
        end
      end

      before do
        allow(RabbitmqConnection).to receive(:subscribe).and_yield(payload.to_json)
        allow(ContactDealAssociation).to receive(:find_or_create_by).and_raise(StandardError.new('Database error'))
      end

      it 'logs the error and continues' do
        expect(Rails.logger).to receive(:error).with(/Database error/)

        expect { described_class.listen }.not_to raise_error
      end
    end
  end
end
