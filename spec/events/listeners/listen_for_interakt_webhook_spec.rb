# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForInteraktWebhook do
  describe '#call' do
    context 'when listening to interakt webhook event' do
      let(:payload) { file_fixture('interakt_webhooks/waba-onboarded-successfully.json').read }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.message', 'message.interakt.webhook', 'q.message.interakt.webhook.message')
          .and_yield(payload.to_s)
      end

      it 'shall call webhook payload processor' do
        expect(Rails.logger).to receive(:info).with("Received message for message.interakt.webhook #{payload}")
        # TODO Add actual change expectations when processor logic is written
        expect(InteraktWebhooks).to receive(:new).with(JSON.parse(payload)).and_return(InteraktWebhooks.new(JSON.parse(payload)))
        described_class.listen
      end
    end
  end
end
