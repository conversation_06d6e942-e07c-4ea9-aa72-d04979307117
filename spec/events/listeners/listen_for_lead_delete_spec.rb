# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForLeadDelete do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic SALES_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(SALES_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'lead deleted event' do
        let(:routing_key)              { LEAD_DELETED_QUEUE }

        context 'message delete' do
          let(:exclusively_related_lead) { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id) }
          let(:another_lead)             { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id) }
          let(:user_lookup)              { create(:look_up, entity_type: LOOKUP_USER, tenant_id: @user.tenant_id) }
          let(:payload)                  do
            { 'userId' => @user.id, 'tenantId' => @user.tenant_id, 'id' => exclusively_related_lead.entity_id }.to_json
          end

          before do
            @message_1 = create(:message, owner: @user, tenant_id: @user.tenant_id)
            @message_2 = create(:message, owner: @user, tenant_id: @user.tenant_id)

            allow(RabbitmqConnection).to receive(:subscribe)
              .with(SALES_EXCHANGE, LEAD_DELETED_EVENT, LEAD_DELETED_QUEUE)
              .and_yield(payload.to_s)
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          end

          it 'removes association correctly and deletes messages correctly' do
            expect(Publishers::MessageDeleted).to receive(:call)

            @message_1.related_to = [exclusively_related_lead, user_lookup]
            @message_1.related_look_ups.each do |ml|
              ml.recipient = true
              ml.save
            end

            @message_2.related_to = [another_lead, exclusively_related_lead]
            @message_2.related_look_ups.each do |ml|
              ml.recipient = true
              ml.save
            end

            Listeners::ListenForLeadDelete.listen

            expect(Message.find_by_id(@message_1.id)).to be_nil

            message_2 = Message.find(@message_2.id)
            expect(message_2).to be_present
            expect(message_2.recipients.count).to eq(1)
            expect(message_2.recipients.ids).to match_array([another_lead.id])
            expect(message_2.related_to.count).to eq(1)
            expect(message_2.related_to.ids).to match_array([another_lead.id])
            expect(
              LookUp.exists?(
                entity_id: exclusively_related_lead.entity_id,
                entity_type: exclusively_related_lead.entity_type
              )
            ).to be_falsey
          end

          it "deletes all messages when it's not associated with another lookup" do
            expect(Publishers::MessageDeleted).to receive(:call).exactly(:twice)

            @message_1.related_to = [exclusively_related_lead]
            @message_1.related_look_ups.each do |ml|
              ml.recipient = true
              ml.save
            end

            @message_2.related_to = [exclusively_related_lead]
            @message_2.related_look_ups.each do |ml|
              ml.recipient = true
              ml.save
            end

            message_1_id = @message_1.id
            message_2_id = @message_2.id

            Listeners::ListenForLeadDelete.listen

            expect(Message.exists?(message_1_id)).to be_falsey
            expect(Message.exists?(message_2_id)).to be_falsey
            expect(
              LookUp.exists?(
                entity_id: exclusively_related_lead.entity_id,
                entity_type: exclusively_related_lead.entity_type
              )
            ).to be_falsey
          end

          it "does not delete message if it is present on a conversation" do
            expect(Publishers::MessageDeleted).to receive(:call).exactly(:once)

            @message_1.related_to = [exclusively_related_lead]
            @message_1.related_look_ups.each do |ml|
              ml.recipient = true
              ml.save
            end

            @message_2.related_to = [exclusively_related_lead]
            @message_2.related_look_ups.each do |ml|
              ml.recipient = true
              ml.save
            end

            @message_2.update(conversation_id: 3)

            message_1_id = @message_1.id
            message_2_id = @message_2.id

            Listeners::ListenForLeadDelete.listen

            expect(Message.exists?(message_1_id)).to be_falsey
            expect(Message.exists?(message_2_id)).to be_truthy
            expect(
              LookUp.exists?(
                entity_id: exclusively_related_lead.entity_id,
                entity_type: exclusively_related_lead.entity_type
              )
            ).to be_falsey
          end
        end

        context 'conversations delete' do
          let(:exclusively_related_lead) { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id) }
          let(:exclusively_related_lead_with_another_phone) { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id, entity_id: exclusively_related_lead.entity_id) }
          let(:another_lead)             { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id) }
          let(:user_lookup)              { create(:look_up, entity_type: LOOKUP_USER, tenant_id: @user.tenant_id) }
          let(:connected_account) { create(:connected_account, tenant_id: @user.tenant_id, status: 'active') }
          let(:payload)                  do
            { 'userId' => @user.id, 'tenantId' => @user.tenant_id, 'id' => exclusively_related_lead.entity_id }.to_json
          end

          before do
            @conversation = create(:conversation, owner_id: @user.id, tenant_id: @user.tenant_id, connected_account_id: connected_account.id, phone_number: '9878')
            @sub_conversation = create(:sub_conversation, tenant_id: @user.tenant_id, connected_account_id: connected_account.id, conversation_id: @conversation.id)
            @message_1 = create(:message, owner: @user, tenant_id: @user.tenant_id, conversation_id: @conversation.id, sub_conversation_id: @sub_conversation.id, connected_account_id: connected_account.id)
            @message_2 = create(:message, owner: @user, tenant_id: @user.tenant_id)

            allow(RabbitmqConnection).to receive(:subscribe)
              .with(SALES_EXCHANGE, LEAD_DELETED_EVENT, LEAD_DELETED_QUEUE)
              .and_yield(payload.to_s)
            allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          end

          it 'removes association from conversation lookup correctly' do
            @conversation.look_ups << exclusively_related_lead
            @conversation.look_ups << another_lead

            Listeners::ListenForLeadDelete.listen

            conversation = Conversation.find(@conversation.id)
            expect(conversation).to be_present
            expect(
              LookUp.exists?(
                entity_id: exclusively_related_lead.entity_id,
                entity_type: exclusively_related_lead.entity_type
              )
            ).to be_falsey
          end

          it 'deletes conversation, subconversation, messages if last entity related to that conversation is deleted' do
            expect(Publishers::MessageDeleted).to receive(:call).exactly(:twice)
            @conversation.look_ups << exclusively_related_lead
            @message_2.recipients << exclusively_related_lead_with_another_phone

            Listeners::ListenForLeadDelete.listen

            expect(Conversation.count).to be(0)
            expect(Conversation.unscoped.count).to be(1)
            expect(Conversation.unscoped.first.deleted_at).to be_present
            expect(ConversationLookUp.count).to be(0)
            expect(LookUp.count).to be(0)
            expect(MessageLookUp.count).to be(0)
          end
        end
      end
    end
  end
end
