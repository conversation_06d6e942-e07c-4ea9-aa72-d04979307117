# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForLeadReassign do
  describe '#listen' do
    context 'when lead is reassigned' do
      let(:lead_lookup) { create(:look_up, entity_type: LOOKUP_LEAD) }
      let(:payload) { { "entityId" => lead_lookup.entity_id, "newOwnerId" => lead_lookup.owner_id + 1, "oldOwnerId" => lead_lookup.owner_id, "tenantId" => lead_lookup.tenant_id } }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.sales', 'sales.lead.owner_updated', 'q.sales.lead.owner_updated.message')
          .and_yield(payload.to_json)
      end

      it 'listens for event and updates owner' do
        expect(Rails.logger).to receive(:info).with("Received message for sales.lead.owner_updated tenant id #{payload['tenantId']} entity id #{payload['entityId']} new owner id #{payload['newOwnerId']}")
        described_class.listen

        expect(lead_lookup.reload.owner_id).to eq(payload['newOwnerId'])
      end
    end
  end
end
