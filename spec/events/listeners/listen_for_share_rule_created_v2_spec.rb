# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForShareRuleCreatedV2 do
  describe '.listen' do
    let(:routing_key) { SHARE_RULE_CREATED_V2_QUEUE }
    let(:payload) { JSON.parse(file_fixture('listeners/share-rule-created-v2.json').read) }
    let(:from_user) { create(:user, id: 3779, tenant_id: 478) }
    let(:to_user) { create(:user, id: 6932, tenant_id: 478) }

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic CONFIG_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(CONFIG_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'when entity type is not valid' do
        before(:each) do
          payload['entity']['entityType'] = 'company'
          allow(RabbitmqConnection).to receive(:subscribe)
          .with(CONFIG_EXCHANGE, SHARE_RULE_CREATED_V2_EVENT, SHARE_RULE_CREATED_V2_QUEUE)
          .and_yield(payload.to_json)
        end

        it 'should not create share rule' do
          expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.created.v2")
           expect{ Listeners::ListenForShareRuleCreatedV2.listen }.to change(ShareRule, :count).by(0)
        end
      end

      context 'when entity type is valid' do
        before(:each) do
          from_user
          to_user
        end

        context 'when message permission is not present in the given share rule' do
          before(:each) do
            allow(RabbitmqConnection).to receive(:subscribe)
              .with(CONFIG_EXCHANGE, SHARE_RULE_CREATED_V2_EVENT, SHARE_RULE_CREATED_V2_QUEUE)
              .and_yield(payload.to_json)
          end

          it 'does not create new share rule' do
            expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.created.v2")
            expect(Rails.logger).to receive(:info).with("Message permission is not there in the given share rule id #{payload.dig('metadata', 'entityId')}, Tenant id: #{payload.dig('metadata', 'tenantId')}")
            expect{ Listeners::ListenForShareRuleCreatedV2.listen }.to change(ShareRule, :count).by(0)
          end
        end

        context 'when message permission is present in the given share rule' do
          before(:each) do
            payload['entity']['actions']['sms'] = true
            allow(RabbitmqConnection).to receive(:subscribe)
              .with(CONFIG_EXCHANGE, SHARE_RULE_CREATED_V2_EVENT, SHARE_RULE_CREATED_V2_QUEUE)
              .and_yield(payload.to_json)
          end

          it 'creates share rule' do
            expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.created.v2")
            expect{ Listeners::ListenForShareRuleCreatedV2.listen }.to change(ShareRule, :count).by(1)
            expect(ShareRule.last.share_rule_id).to eq(3654)
          end
        end
      end
    end
  end
end
