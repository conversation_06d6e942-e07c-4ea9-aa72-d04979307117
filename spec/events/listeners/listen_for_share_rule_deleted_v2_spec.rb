# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForShareRuleDeletedV2 do
  describe '.listen' do
    let(:routing_key) { SHARE_RULE_DELETED_V2_QUEUE }
    let(:payload) { JSON.parse(file_fixture('listeners/share-rule-deleted-v2.json').read) }
    let(:from_user) { create(:user, id: 3779, tenant_id: 478) }
    let(:to_user) { create(:user, id: 6932, tenant_id: 478) }
    let(:share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: 754, from_id: 4010, to_id: 6932, from_type: 'USER', to_type: 'USER') }

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic CONFIG_EXCHANGE
    end

    context 'when valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(CONFIG_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'when entity type is invalid' do
        before(:each) do
          payload['oldEntity']['entityType'] = 'company'
          allow(RabbitmqConnection).to receive(:subscribe)
          .with(CONFIG_EXCHANGE, SHARE_RULE_DELETED_V2_EVENT, SHARE_RULE_DELETED_V2_QUEUE)
          .and_yield(payload.to_json)
        end

        it 'should not delete share rule' do
          expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.deleted.v2")
          expect{ Listeners::ListenForShareRuleDeletedV2.listen }.to change(ShareRule, :count).by(0)
        end
      end

      before(:each) do
        allow(RabbitmqConnection).to receive(:subscribe)
        .with(CONFIG_EXCHANGE, SHARE_RULE_DELETED_V2_EVENT, SHARE_RULE_DELETED_V2_QUEUE)
        .and_yield(payload.to_json)
      end

      context 'when share rule is not found with given id' do
        it 'returns without deleting share rule' do
          expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.deleted.v2")

          expect{ Listeners::ListenForShareRuleDeletedV2.listen }.to change(ShareRule, :count).by(0)
        end
      end

      context 'when share rule is found with given id' do
        before(:each) { share_rule }

        it 'deletes the share rule' do
          expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.deleted.v2")

          expect{ Listeners::ListenForShareRuleDeletedV2.listen }.to change(ShareRule, :count).by(-1)
        end
      end
    end
  end
end
