# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForShareRuleUpdatedV2 do
  describe '.listen' do
    let(:routing_key) { SHARE_RULE_UPDATED_V2_QUEUE }
    let(:payload) { JSON.parse(file_fixture('listeners/share-rule-updated-v2.json').read) }
    let(:from_user) { create(:user, id: 3779, tenant_id: 478) }
    let(:to_user) { create(:user, id: 6932, tenant_id: 478) }
    let(:share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: 478, from_id: 3779, to_id: 6932, from_type: 'USER', to_type: 'USER') }

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic CONFIG_EXCHANGE
    end

    context 'when valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(CONFIG_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
      end

      context 'when entity type is invalid' do
        before(:each) do
          payload['entity']['entityType'] = 'company'
          allow(RabbitmqConnection).to receive(:subscribe)
          .with(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_V2_EVENT, SHARE_RULE_UPDATED_V2_QUEUE)
          .and_yield(payload.to_json)
        end

        it 'should not create or update share rule' do
          expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.updated.v2")
           expect{ Listeners::ListenForShareRuleUpdatedV2.listen }.to change(ShareRule, :count).by(0)
        end
      end

      context 'when entity type is valid' do
        before(:each) do
          from_user
          to_user
        end

        context 'when share rule with given id is already present' do
          before(:each) { share_rule }

          context 'when message permission is present in share rule' do
            before(:each) do
              payload['entity']['actions']['sms'] = true
              allow(RabbitmqConnection).to receive(:subscribe)
                .with(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_V2_EVENT, SHARE_RULE_UPDATED_V2_QUEUE)
                .and_yield(payload.to_json)
            end

            it 'updates existing share rule' do
              expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.updated.v2")
              expect{ Listeners::ListenForShareRuleUpdatedV2.listen }.to change(ShareRule, :count).by(0)
              expect(ShareRule.last.share_rule_id).to eq(3654)
              expect(ShareRule.last.actions).to eq(
                {
                  "sms"=>true,
                  "call"=>false,
                  "note"=>false,
                  "read"=>false,
                  "task"=>false,
                  "email"=>false,
                  "write"=>false,
                  "delete"=>false,
                  "update"=>false,
                  "meeting"=>false,
                  "readAll"=>false,
                  "reshare"=>false,
                  "document"=>false,
                  "reassign"=>false,
                  "deleteAll"=>false,
                  "quotation"=>false,
                  "updateAll"=>false
                }
              )
            end
          end

          context 'when message permission is not present in share rule' do
            before(:each) do
              allow(RabbitmqConnection).to receive(:subscribe)
                .with(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_V2_EVENT, SHARE_RULE_UPDATED_V2_QUEUE)
                .and_yield(payload.to_json)
            end

            it 'deletes existing share rule' do
              expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.updated.v2")
              expect(Rails.logger).to receive(:info).with("Message permission is not there in the given share rule id #{payload.dig('metadata', 'entityId')}, Tenant id: #{payload.dig('metadata', 'tenantId')}")

              expect{ Listeners::ListenForShareRuleUpdatedV2.listen }.to change(ShareRule, :count).by(-1)
            end
          end
        end

        context 'when share rule with given id is not present' do
          before(:each) do
            payload['entity']['actions']['sms'] = true
            allow(RabbitmqConnection).to receive(:subscribe)
              .with(CONFIG_EXCHANGE, SHARE_RULE_UPDATED_V2_EVENT, SHARE_RULE_UPDATED_V2_QUEUE)
              .and_yield(payload.to_json)
          end

          it 'creates new share rule' do
            expect(Rails.logger).to receive(:info).with("Received message for share rule id #{payload.dig('metadata', 'entityId')} for sharerule.updated.v2")
            expect{ Listeners::ListenForShareRuleUpdatedV2.listen }.to change(ShareRule, :count).by(1)
            expect(ShareRule.last.share_rule_id).to eq(3654)
          end
        end
      end
    end
  end
end
