# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForTeamUpdatedV2 do
  describe '.listen' do
    let(:team) { create(:team, user_ids: []) }
    let(:routing_key) { TEAM_UPDATED_V2_QUEUE }
    let(:payload) do
      team_updated_v2 = JSON.parse(file_fixture('listeners/team-updated-v2.json').read)
      team_updated_v2['entity']['id'] = team_updated_v2['oldEntity']['id'] = team_updated_v2['metadata']['entityId'] = team.id
      team_updated_v2['metadata']['tenantId'] = team.tenant_id

      team_updated_v2
    end

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ''
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, TEAM_UPDATED_V2_EVENT, TEAM_UPDATED_V2_QUEUE)
          .and_yield(payload.to_json)
      end

      it 'updates user ids on team and team name' do
        expect(Rails.logger).to receive(:info).with("Received message for team id #{team.id} for team.updated.v2")
        Listeners::ListenForTeamUpdatedV2.listen

        expect(team.reload.name).to eq('Updated team')
        expect(team.user_ids).to match_array([1])
      end
    end
  end
end
