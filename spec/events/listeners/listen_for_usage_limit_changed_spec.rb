# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForUsageLimitChanged do
  describe '.listen' do
    let(:payload) { JSON.parse(file_fixture('usage-limit-changed-payload.json').read) }

    context 'when plan downgraded' do
      before do
        payload['planId'] = 'embark'
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'deactivates connected accounts' do
        expect_any_instance_of(ConnectedAccountService).to receive(:deactivate_all_accounts)
        described_class.listen
      end
    end

    context 'when add on expired' do
      before do
        payload['usageEntityLimits'].reject! { |usage_entity| usage_entity['usageEntity'] == 'WHATSAPP_BUSINESS' }
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'deactivates connected accounts' do
        expect_any_instance_of(ConnectedAccountService).to receive(:deactivate_all_accounts)
        described_class.listen
      end
    end

    context 'when usage limit changed and some other entity changed' do
      before do
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'does not add credits to account' do
        expect_any_instance_of(ConnectedAccountService).not_to receive(:deactivate_all_accounts)
        described_class.listen
      end
    end

    context 'when credits are added' do
      before do
        payload['usageEntityLimits'].push({ usageEntity: 'WHATSAPP_CREDITS', limit: 1000, isCharged: true })
        expect(RabbitmqConnection).to receive(:subscribe).with('ex.iam', 'usage.limit.changed', 'q.message.usage.limit.changed')
        .and_yield(payload.to_json)
      end

      it 'adds the credits to account' do
        expect_any_instance_of(WhatsappCreditsService).to receive(:add).with(4240, 1000)
        described_class.listen
      end
    end
  end
end
