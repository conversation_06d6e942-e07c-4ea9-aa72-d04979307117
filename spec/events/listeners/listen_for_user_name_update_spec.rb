require 'rails_helper'

RSpec.describe Listeners::ListenForUserNameUpdate do
  describe '#call' do
    before do
      @user = create(:user)
      @lookup = create(:look_up, entity_type: LOOKUP_USER, tenant_id: @user.tenant_id, entity_id: @user.id, name: @user.name)
    end

    context 'valid input' do
      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.iam', 'user.name.updated', 'q.message.user.name.updated')
          .and_yield(payload.to_s)
        described_class.listen
      end

      context 'user name updated event' do
        let(:routing_key) { 'user.name.updated' }
        let(:payload)     { { "userId" => @user.id, "tenantId" => @user.tenant_id, "firstName" => "John", "lastName" => "Doe " }.to_json }

        it 'updates the User with new name' do
          expect(@user.reload.name).to eq('<PERSON>')
        end
      end
    end
  end
end
