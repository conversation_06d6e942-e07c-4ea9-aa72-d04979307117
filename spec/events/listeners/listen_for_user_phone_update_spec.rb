require 'rails_helper'

RSpec.describe Listeners::ListenForUserPhoneUpdate do
  describe '#call' do
    before do
      @user = create(:user)
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE
    end

    context 'valid input' do
      before do
        @queue = @channel.queue ""
        @queue.bind @exchange, routing_key: routing_key
        allow(RabbitmqConnection).to receive(:get_exchange)
          .with(USER_EXCHANGE)
          .and_return(@exchange)
        allow(RabbitmqConnection).to receive(:get_channel)
          .and_return(@channel)
        allow(RabbitmqConnection).to receive(:subscribe)
          .with(USER_EXCHANGE, USER_PHONE_UPDATED_EVENT, USER_PHONE_UPDATED_QUEUE)
          .and_yield(payload.to_s)
        Listeners::ListenForUserPhoneUpdate.listen()
      end

      context 'user phone updated event' do
        let(:routing_key) { USER_PHONE_UPDATED_EVENT }
        let(:phone_1)     { { "code"=>"IN", "dialCode"=>"+91", "primary"=>true, "type"=>"MOBILE", "value"=>"**********" } }
        let(:phone_2)     { { "code"=>"IN", "dialCode"=>"+91", "primary"=>false, "type"=>"MOBILE", "value"=>"**********" } }
        let(:payload)     { { "userId" => @user.id, "tenantId" => @user.tenant_id, "phoneNumbers" => [phone_2, phone_1] }.to_json }

        it 'updates the User with new name' do
          user = User.find(@user.id)
          expect(user.phone_number).to eq("#{phone_1['dialCode']}#{phone_1['value']}")
        end
      end
    end
  end
end