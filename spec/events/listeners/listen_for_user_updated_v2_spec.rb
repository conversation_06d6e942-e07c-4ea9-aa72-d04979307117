# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForUserUpdatedV2 do
  describe '.listen' do
    let!(:user) { create(:user, id: 53) }
    let(:payload) { file_fixture('listeners/user-updated-v2-response.json').read }

    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic USER_EXCHANGE

      @queue = @channel.queue ""
      @queue.bind @exchange, routing_key: USER_UPDATED_V2_EVENT
      allow(RabbitmqConnection).to receive(:get_exchange)
        .with(USER_EXCHANGE)
        .and_return(@exchange)
      allow(RabbitmqConnection).to receive(:get_channel)
        .and_return(@channel)
      allow(RabbitmqConnection).to receive(:subscribe)
        .with(USER_EXCHANGE, USER_UPDATED_V2_EVENT, USER_UPDATED_V2_QUEUE)
        .and_yield(payload.to_s)
      Listeners::ListenForUserUpdatedV2.listen()
    end

    context 'when user timezone and date format is updated' do
      it 'saves updated timezone and dateformat on the user' do
        expect(user.reload.timezone).to eq("Asia/Calcutta")
        expect(user.reload.date_format).to eq("MMM D, YYYY [at] h:mm a")
      end
    end
  end
end
