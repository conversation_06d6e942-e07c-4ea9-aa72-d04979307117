# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Listeners::ListenForWhatsappWebhook do
  describe '#call' do
    context 'when listening to whatsapp webhook event' do
      let(:payload) { file_fixture('webhooks/whatsapp-template-approved.json').read }

      before do
        allow(RabbitmqConnection).to receive(:subscribe)
          .with('ex.message', 'message.whatsapp.webhook', 'q.message.whatsapp.webhook.message')
          .and_yield(payload.to_s)
      end

      it 'shall call webhook payload processor' do
        expect(Rails.logger).to receive(:info).with("Received message for message.whatsapp.webhook #{payload}")
        # TODO Add actual change expectations when processor logic is written
        expect(WhatsappWebhooks).to receive(:new).with(JSON.parse(payload)).and_return(WhatsappWebhooks.new(JSON.parse(payload)))
        described_class.listen
      end
    end
  end
end
