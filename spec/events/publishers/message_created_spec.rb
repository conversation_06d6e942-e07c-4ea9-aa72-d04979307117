# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::MessageCreated do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic 'ex.message'
      @queue = @channel.queue 'message.created'
      @queue.bind @exchange, routing_key: 'message.created'
      allow(RabbitmqConnection).to receive(:get_exchange).with('ex.message', 'message').and_return(@queue)
    end

    context 'when MessageCreated publishers is called' do
      before do
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user)
        @message.recipients = [@look_up]
        @message.related_to = [@look_up]
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

        Publishers::MessageCreated.call(@message.id)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expected_payload_message = {
          'entity' => MessageSerializer::Details.serialize(@message),
          'oldEntity' => nil,
          'metadata' => {
            'tenantId' => @message.tenant_id,
            'userId' => @message.owner_id,
            'entityId' => @message.id,
            'entityType' => 'MESSAGE',
            'entityAction' => 'CREATED'
          }
        }
        expect(payload.first[:routing_key]).to eq('message.created')
        expect(JSON.parse(payload.last)).to eq(expected_payload_message)
      end
    end

    context 'for whatsapp business media messages where content is not present' do
      before do
        connected_account = create(:connected_account, tenant_id: @user.tenant_id)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user, content: '', message_type: 'whatsapp_business', connected_account_id: connected_account.id)
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

        Publishers::MessageCreated.call(@message.id)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)['entity']['content']).to eq('(document)')
      end
    end
  end
end
