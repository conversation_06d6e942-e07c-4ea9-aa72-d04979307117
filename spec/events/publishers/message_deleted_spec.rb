# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::MessageDeleted do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic 'ex.message'
      @queue = @channel.queue 'message.deleted'
      @queue.bind @exchange, routing_key: 'message.deleted'
      allow(RabbitmqConnection).to receive(:get_exchange).with('ex.message', 'message').and_return(@queue)
    end

    context 'when MessageDeleted publishers is called' do
      before do
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user)
        @message.recipients = [@look_up]
        @message.related_to = [@look_up]
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

        @deleted_message_data = MessageSerializer::Details.serialize(@message, @user)
        Publishers::MessageDeleted.call(@user.tenant_id, @user.id, @deleted_message_data)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expected_payload_message = {
          'entity' => nil,
          'oldEntity' => @deleted_message_data,
          'metadata' => {
            'tenantId' => @user.tenant_id,
            'userId' => @user.id,
            'entityId' => @deleted_message_data['id'],
            'entityType' => 'MESSAGE',
            'entityAction' => 'DELETED'
          }
        }
        expect(payload.first[:routing_key]).to eq('message.deleted')
        expect(JSON.parse(payload.last)).to eq(expected_payload_message)
        expect(JSON.parse(payload.last)['oldEntity'].keys).to include('deletedBy', 'deletedAt')
      end
    end
  end
end
