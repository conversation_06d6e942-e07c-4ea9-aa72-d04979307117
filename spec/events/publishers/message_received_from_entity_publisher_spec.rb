require 'rails_helper'

RSpec.describe Publishers::MessageReceivedFromEntityPublisher do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue MESSAGE_RECEIVED_FROM_ENTITY_EVENT
      @queue.bind @exchange, routing_key: MESSAGE_RECEIVED_FROM_ENTITY_EVENT
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context 'when the MessageReceivedFromEntityPublisher is called' do
      before do
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user)
        @message.recipients = [@look_up]
        @message.related_to = [@look_up]
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

        data = MessageSerializer::Details.serialize(@message).except("attachments")
        @event_data = Event::MessageReceivedFromEntity.new(data).to_json

        Publishers::MessageReceivedFromEntityPublisher.call(@message)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq({
          "id" => @message.id,
          "tenantId" => @message.tenant_id,
          "content" => @message.content,
          "medium" => @message.medium,
          "direction" => @message.direction,
          "sentAt" => @message.sent_at.iso8601(3),
          "deliveredAt" => @message.delivered_at.iso8601(3),
          "readAt" => @message.read_at.iso8601(3),
          "recipientNumber" => @message.recipient_number,
          "senderNumber" => @message.sender_number,
          "createdAt" => @message.created_at.iso8601(3),
          "updatedAt" => @message.updated_at.iso8601(3),
          "messageType" => @message.message_type,
          "statusMessage" => nil,
          "conversationId" => nil, 
          "status" => "Sent",
          "owner" => {
            "id" => @user.id,
            "name" => @user.name
          },
          "relatedTo" => [
            { "name" => @look_up.name, "id" => @look_up.entity_id, "entity" => "lead", "ownerId" => 123 }
          ],
          "recipients" => [
            {"name" => @look_up.name, "id" => @look_up.entity_id, "entity" => "lead", "ownerId" => 123}
          ]
        })
      end
    end
  end
end
