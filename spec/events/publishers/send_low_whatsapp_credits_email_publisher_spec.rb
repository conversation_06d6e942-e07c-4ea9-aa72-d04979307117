# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::SendLowWhatsappCreditsEmailPublisher do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue MESSAGE_WHATSAPP_CREDITS_ABOUT_TO_EXPIRE
      @queue.bind @exchange, routing_key: MESSAGE_WHATSAPP_CREDITS_ABOUT_TO_EXPIRE
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context 'when the SendLowWhatsappCreditsEmailPublisher is called' do
      let(:whatsapp_credit) { create(:whatsapp_credit, total: 1000, consumed: 874, parked: 40) }
      let(:connected_account){ create(:connected_account, tenant_id: whatsapp_credit.tenant_id) }
      let(:connected_account_2){ create(:connected_account, tenant_id: whatsapp_credit.tenant_id) }

      before do
        connected_account
        connected_account_2
        Publishers::SendLowWhatsappCreditsEmailPublisher.call(whatsapp_credit)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq({
          "entityGroup" => "Whatsapp Credits",
          "remainingCredits" => 86.0,
          "usedCreditsPercentage" => 91,
          "parkedCredits" => 40.0,
          "tenantId" => whatsapp_credit.tenant_id,
          "users" => [
            { "name" => connected_account.created_by.name, "id" => connected_account.created_by_id },
            { "name" => connected_account_2.created_by.name, "id" => connected_account_2.created_by_id }
          ]
        })
      end
    end
  end
end
