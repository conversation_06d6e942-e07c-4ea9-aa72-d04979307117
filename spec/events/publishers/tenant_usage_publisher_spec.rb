require 'rails_helper'

RSpec.describe Publishers::TenantUsagePublisher do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE

      @all_tenants_event_data = Event::TenantUsage.new([
        {:tenantId=>15, :count=>1, :usageEntity=>"MESSAGE"},
        {:tenantId=>14, :count=>1, :usageEntity=>"MESSAGE"},
        {:tenantId=>15, :count=>90, :usageEntity=>"STORAGE_MESSAGE_ATTACHMENT"},
        {:tenantId=>14, :count=>50, :usageEntity=>"STORAGE_MESSAGE_ATTACHMENT"},
        {"count"=>150, "tenantId"=>15, "usageEntity"=>"STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT"},
        {"count"=>100, "tenantId"=>14, "usageEntity"=>"STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT"}
      ])

      @tenant_event_data = Event::TenantUsage.new([
        {:tenantId=>15, :count=>1, :usageEntity=>"MESSAGE"},
        {:tenantId=>15, :count=>90, :usageEntity=>"STORAGE_MESSAGE_ATTACHMENT"},
        {:tenantId=>15, :count=>150, :usageEntity=>"STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT"},
        {:tenantId=>15, :count=>200, :usageEntity=>"WHATSAPP_CREDITS"}
      ])

      @queue = @channel.queue "tenant.usage.collected"
      @queue.bind @exchange, routing_key: "tenant.usage.collected"
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context "When tenant usage publisher is called" do
      context 'when multiple messages are present in DB' do
        before do
          message_1 = create(:message, tenant_id: 14)
          message_2 = create(:message, tenant_id: 15)

          create_list(:attachment, 5, message: message_1, size: 10)
          create_list(:attachment, 3, message: message_2, size: 20)
          create_list(:attachment, 1, message: message_2, size: 30)
          create(:template_media, tenant_id: 14, file_size: 100)
          create(:template_media, tenant_id: 15, file_size: 150)
          create(:whatsapp_credit, tenant_id: 15, total: 1000, consumed: 200)
        end

        context 'and tenant id argument is passed' do
          before { Publishers::TenantUsagePublisher.call(15) }

          it 'publishes correct number of events', :publisher_spec do
            expect(@queue.message_count).to eq(1)
          end

          it 'publishes the correct payload', :publisher_spec do
            payload = @queue.pop
            expect(JSON(payload.last)['usageRecords']).to match_array(JSON(@tenant_event_data.to_json)['usageRecords'])
          end
        end

        context 'and tenant id argument is not passed' do
          before { Publishers::TenantUsagePublisher.call }

          it 'publishes correct number of events', :publisher_spec do
            expect(@queue.message_count).to eq(1)
          end

          it 'publishes the correct payload', :publisher_spec do
            payload = @queue.pop
            expect(JSON(payload.last)['usageRecords']).to match_array(JSON(@all_tenants_event_data.to_json)['usageRecords'])
          end
        end
      end

      context 'when last message is deleted from DB' do
        context 'and tenant id argument is passed' do
          before { Publishers::TenantUsagePublisher.call(15) }

          it 'publishes correct number of events', :publisher_spec do
            expect(@queue.message_count).to eq(1)
          end

          it 'publishes the correct payload', :publisher_spec do
            @tenant_event_data = Event::TenantUsage.new([{:tenantId=>15, :count=>0, :usageEntity=>"MESSAGE"},
                                                         {:tenantId=>15, :count=>0, :usageEntity=>"STORAGE_MESSAGE_ATTACHMENT"}])
            payload = @queue.pop
            expect(JSON(payload.last)['usageRecords']).to match_array(JSON(@tenant_event_data.to_json)['usageRecords'])
          end
        end
      end
    end
  end
end
