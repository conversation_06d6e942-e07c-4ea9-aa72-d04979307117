# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::WhatsappEntityMessageStatusTrackPublisher do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue WHATSAPP_ENTITY_MESSAGE_STATUS_TRACK
      @queue.bind @exchange, routing_key: WHATSAPP_ENTITY_MESSAGE_STATUS_TRACK
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context 'when the WhatsappEntityMessageStatusTrackPublisher is called' do
      before do
        @conversation = create(:conversation, tenant_id: @user.tenant_id)
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user, direction: 'outgoing', conversation_id: @conversation.id, campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' })
        @conversation.look_ups << @look_up

        Publishers::WhatsappEntityMessageStatusTrackPublisher.call(@message)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq({
          "id"=>@message.id,
          "userId"=>@message.owner_id,
          "tenantId"=>@message.tenant_id,
          "status"=>@message.status,
          "sentAt"=>Time.at(@message.sent_at).strftime("%FT%T.%LZ"),
          "deliveredAt"=>Time.at(@message.delivered_at).strftime("%FT%T.%LZ"),
          "readAt"=>Time.at(@message.read_at).strftime("%FT%T.%LZ"),
          "receivedAt"=>nil,
          "failedAt"=>nil,
          "campaignInfo"=>{"campaignId"=>123, "activityId"=>234, "entityId"=>345, "entity"=>"lead", "phoneNumber"=>"+************"}
        })
      end

    end

    context 'when the message has failed status' do
      before do
        @conversation = create(:conversation, tenant_id: @user.tenant_id)
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user, direction: 'outgoing', conversation_id: @conversation.id, campaign_info: { campaignId: 123, activityId: 234, entityId: 345, entity: 'lead', phoneNumber: '+************' }, status: 'failed', failed_at: DateTime.now.utc, delivered_at: nil, read_at: nil, status_message: '1003 - You do not have permission to send messages to this recipient.')
        @conversation.look_ups << @look_up
        Publishers::WhatsappEntityMessageStatusTrackPublisher.call(@message)
      end

      it 'publishes the failedAt timestamp' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to include(
          {
            "id"=>@message.id,
            "userId"=>@message.owner_id,
            "tenantId"=>@message.tenant_id,
            "status"=>@message.status,
            "sentAt"=>Time.at(@message.sent_at).strftime("%FT%T.%LZ"),
            "deliveredAt"=>nil,
            "readAt"=>nil,
            "receivedAt"=>nil,
            "failedAt"=>Time.at(@message.failed_at).strftime("%FT%T.%LZ"),
            "statusMessage"=>@message.status_message,
            "campaignInfo"=>{"campaignId"=>123, "activityId"=>234, "entityId"=>345, "entity"=>"lead", "phoneNumber"=>"+************"}
          }
        )
      end
    end
  end
end
