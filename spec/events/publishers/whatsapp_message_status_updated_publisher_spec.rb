# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::WhatsappMessageStatusUpdatedPublisher do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue WHATSAPP_MESSAGE_STATUS_UPDATED
      @queue.bind @exchange, routing_key: WHATSAPP_MESSAGE_STATUS_UPDATED
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context 'when the WhatsappMessageStatusUpdatedPublisher is called' do
      before do
        @conversation = create(:conversation, tenant_id: @user.tenant_id)
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user, conversation_id: @conversation.id)
        @conversation.look_ups << @look_up
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

        Publishers::WhatsappMessageStatusUpdatedPublisher.call(@message)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq({
          "id" => @message.id,
          "conversationId" => @conversation.id,
          "conversationOwnerId" => @conversation.owner_id,  
          "status" => "sent",
          "relatedTo" => [
            { "name" => @look_up.name, "id" => @look_up.entity_id, "entity" => "lead" }
          ],
          "tenantId" => @message.tenant_id
        })
      end
    end
  end
end
