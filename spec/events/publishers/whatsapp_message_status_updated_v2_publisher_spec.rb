# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::WhatsappMessageStatusUpdatedV2Publisher do
  describe '#call' do
    let(:whatsapp_template){ create(:whatsapp_template) }

    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue WHATSAPP_MESSAGE_STATUS_UPDATED_V2
      @queue.bind @exchange, routing_key: WHATSAPP_MESSAGE_STATUS_UPDATED_V2
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context 'when the WhatsappMessageStatusUpdatedV2Publisher is called' do
      before do
        @conversation = create(:conversation, tenant_id: @user.tenant_id)
        @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD, tenant_id: @user.tenant_id)
        @connected_account = create(:connected_account, tenant_id: @user.tenant_id)
        @message = create(:message, tenant_id: @user.tenant_id, owner: @user, conversation_id: @conversation.id, connected_account_id: @connected_account.id, whatsapp_template_id: whatsapp_template.id, direction: 'outgoing')
        @message.update_column(:metadata, { 'executedWorkflows' => ['WF_302'] })
        @conversation.look_ups << @look_up
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

        Publishers::WhatsappMessageStatusUpdatedV2Publisher.call(@message)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq(
          {
            "entity" => {
              "id"=>@message.id,
              "tenantId"=>@message.tenant_id,
              "status"=>@message.status,
              "sentFromAccount"=>{
                "id"=>@connected_account.id, 
                "name"=>@connected_account.display_name
              },
              "direction"=>@message.direction,
              "messageBody"=>@message.plain_text_content,
              "sentAt"=>Time.at(@message.sent_at).strftime("%FT%T.%LZ"),
              "deliveredAt"=>Time.at(@message.delivered_at).strftime("%FT%T.%LZ"),
              "readAt"=>Time.at(@message.read_at).strftime("%FT%T.%LZ"),
              "receivedAt"=>nil,
              "template"=>{
                "id"=>whatsapp_template.id, 
                "name"=>whatsapp_template.name
              },
              "templateType"=>"text",
              "relatedTo"=>[
                {
                  "entity"=>"lead", 
                  "id"=>@look_up.entity_id, 
                  "name"=>@look_up.name
                }
              ]
            },
            "oldEntity"=>nil,
            "metadata"=>{
              "entityAction"=>"UPDATED", 
              "tenantId"=>@message.tenant_id, 
              "userId"=>@message.owner_id, 
              "entityType"=>"WHATSAPP_MESSAGE", 
              "workflowId"=>nil, 
              "entityId"=>@message.id,
              "executedWorkflows"=>["WF_302"]
            }
          }
        )
      end
    end
  end
end
