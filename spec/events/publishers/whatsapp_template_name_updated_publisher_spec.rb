# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::WhatsappTemplateNameUpdatedPublisher do
  describe '#call' do
    before do
      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue MESSAGE_WHATSAPP_TEMPLATE_NAME_UPDATED
      @queue.bind @exchange, routing_key: MESSAGE_WHATSAPP_TEMPLATE_NAME_UPDATED
      allow(RabbitmqConnection).to receive(:get_exchange).with(MESSAGE_EXCHANGE, 'message').and_return(@queue)
    end

    context 'when the WhatsappTemplateNameUpdatedPublisher is called' do
      let(:whatsapp_template){ create(:whatsapp_template) }
     
      before do
        Publishers::WhatsappTemplateNameUpdatedPublisher.call(whatsapp_template)
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq({
          "id" => whatsapp_template.id,
          "name" => whatsapp_template.name,
          "tenantId" => whatsapp_template.tenant_id
        })
      end
    end
  end
end
