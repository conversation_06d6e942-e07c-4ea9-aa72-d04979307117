# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Publishers::WorkflowExecutionStatusUpdatePublisher do
  describe '#call' do
    before do
      @user = create(:user)

      connection = BunnyMock.new
      @channel = connection.start.channel
      @exchange = @channel.topic MESSAGE_EXCHANGE
      @queue = @channel.queue MESSAGE_WORKFLOW_EXECUTION_STATUS_UPDATE
      @queue.bind @exchange, routing_key: MESSAGE_WORKFLOW_EXECUTION_STATUS_UPDATE
      allow(RabbitmqConnection).to receive(:get_exchange).with(WORKFLOW_EXCHANGE, 'workflow').and_return(@queue)
    end

    context 'when the WorkflowExecutionStatusUpdatePublisher is called' do
      data = {
        eventId: 123,
        status: SUCCESS,
        statusCode: 200,
        executionDetails: {
          statuses: [{
            phoneNumber: '+************',
            status: SUCCESS,
            errorCode: nil,
            errorMessage: nil
          }]
        }
      }

      before do
        Publishers::WorkflowExecutionStatusUpdatePublisher.call(data, { reply_to_exchange: 'ex.workflow', reply_to_event: 'workflow.whatsapp.message.send.reply'})
      end

      it 'publishes correct number of events' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes the correct payload' do
        payload = @queue.pop
        expect(JSON.parse(payload.last)).to eq({
          "eventId" => 123,
          "status" => "SUCCESS",
          "statusCode" => 200,
          "executionDetails" => {
            "statuses" => [{
              "phoneNumber" => '+************',
              "status" => "SUCCESS",
              "errorCode" => nil,
              "errorMessage" => nil
            }]
          }
        })
      end
    end
  end
end
