FactoryBot.define do
  factory :auth_data, class: 'Auth::Data' do
    expires_in { 43199 }
    expiry { (Time.now + 1.day).to_i }
    token_type { 'Bearer' }
    user_id { rand(1000) }
    access_token { SecureRandom.uuid }
    username { Faker::Internet.email }
    tenant_id { rand(100) }
    meta { { pid: 12 } }
    permissions { build_list(:auth_permission_lead_sms_true, 1)}

    trait :lead_with_sms_permission do
      permissions { build_list(:auth_permission_lead_sms_true, 1)}
    end

    trait :deal_with_sms_permission do
      permissions { build_list(:auth_permission_deal_sms_true, 1)}
    end

    trait :contact_with_sms_permission do
      permissions { build_list(:auth_permission_contact_sms_true, 1)}
    end

    trait :lead_without_sms_permission do
      permissions { build_list(:auth_permission_lead_sms_false, 1)}
    end

    trait :deal_without_sms_permission do
      permissions { build_list(:auth_permission_deal_sms_false, 1)}
    end

    trait :contact_with_sms_permission do
      permissions { build_list(:auth_permission_contact_sms_false, 1)}
    end

    trait :sms_with_delete_permission do
      permissions { build_list(:auth_permission_sms_delete_true, 1)}
    end

    trait :sms_without_delete_permission do
      permissions { build_list(:auth_permission_sms_delete_false, 1)}
    end

    trait :sms_with_write_permission do
      permissions { build_list(:auth_permission_sms_write_true, 1)}
    end

    trait :sms_without_write_permission do
      permissions { build_list(:auth_permission_sms_write_false, 1)}
    end
  end
end
