FactoryBot.define do
  factory :auth_permission, class: 'Auth::Permission' do
    id { rand(10000) }
    name { ['email'].sample }
    description { Faker::Lorem.sentence }
    limits { rand(1000) }
    units { 'count' }
    action { build(:auth_permission_action) }

    trait :lead_sms_true do
      name { 'lead' }
      action { build(:auth_permission_action, sms: true) }
    end

    trait :lead_sms_false do
      name { 'lead' }
      action { build(:auth_permission_action, sms: false) }
    end

    trait :deal_sms_true do
      name { 'deal' }
      action { build(:auth_permission_action, sms: true) }
    end

    trait :deal_sms_false do
      name { 'deal' }
      action { build(:auth_permission_action, sms: false)}
    end

    trait :contact_sms_true do
      name { 'contact' }
      action { build(:auth_permission_action, sms: true)}
    end

    trait :contact_sms_false do
      name { 'contact' }
      action { build(:auth_permission_action, sms: false)}
    end

    trait :sms_delete_true do
      name { 'sms' }
      action { build(:auth_permission_action, delete: true)}
    end

    trait :sms_delete_false do
      name { 'sms' }
      action { build(:auth_permission_action, delete: false)}
    end

    trait :sms_write_true do
      name { 'sms' }
      action { build(:auth_permission_action, write: true)}
    end

    trait :sms_write_false do
      name { 'sms' }
      action { build(:auth_permission_action, write: false)}
    end

    factory :auth_permission_lead_sms_true, traits: [:lead_sms_true]
    factory :auth_permission_lead_sms_false, traits: [:lead_sms_false]
    factory :auth_permission_deal_sms_true, traits: [:deal_sms_true]
    factory :auth_permission_deal_sms_false, traits: [:deal_sms_false]
    factory :auth_permission_contact_sms_true, traits: [:contact_sms_true]
    factory :auth_permission_contact_sms_false, traits: [:contact_sms_false]
    factory :auth_permission_sms_delete_true, traits: [:sms_delete_true]
    factory :auth_permission_sms_delete_false, traits: [:sms_delete_false]
    factory :auth_permission_sms_write_true, traits: [:sms_write_true]
    factory :auth_permission_sms_write_false, traits: [:sms_write_false]
  end
end
