FactoryBot.define do
  lead_sms_true_permission = {
    "id" => 4,
    "name" =>  "lead",
    "description" =>  "has access to lead",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "sms" => true
    }
  }

  lead_sms_false_permission = {
    "id" => 4,
    "name" =>  "lead",
    "description" =>  "has access to lead",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "sms" => false
    }
  }

  contact_sms_true_permission = {
    "id" => 17,
    "name" =>  "contact",
    "description" =>  "has access to contact",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "sms" => true,
    }
  }

  contact_sms_false_permission = {
    "id" => 17,
    "name" =>  "contact",
    "description" =>  "has access to contact",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "sms" => false
    }
  }

  deal_sms_true_permission = {
    "id" => 12,
    "name" =>  "deal",
    "description" =>  "has access to deal",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "sms" => true,
    }
  }

  deal_sms_false_permission = {
    "id" => 12,
    "name" =>  "deal",
    "description" =>  "has access to deal",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "sms" => false
    }
  }

  sms_delete_true_permission = {
    "id" => 5,
    "name" =>  "sms",
    "description" =>  "has access to sms",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "delete" => true,
      "deleteAll" => false
    }
  }

  sms_delete_all_true_permission = {
    "id" => 5,
    "name" =>  "sms",
    "description" =>  "has access to sms",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "delete" => false,
      "deleteAll" => true
    }
  }

  whatsapp_read_all_update_all_true_permission = {
    'id' => 5,
    'name' =>  'whatsappBusiness',
    'description' =>  'has access to whatsapp',
    'limits' =>  -1,
    'units' =>  'count',
    'action' =>  {
      'updateAll' => true,
      'readAll' => true
    }
  }

  whatsapp_template_read_all_update_all_true_permission = {
    'id' => 6,
    'name' =>  'whatsappTemplate',
    'description' =>  'has access to whatsapp templates',
    'limits' =>  -1,
    'units' =>  'count',
    'action' =>  {
      'updateAll' => true,
      'update' => true,
      'readAll' => true,
      'read' => true,
      'write' => true
    }
  }

  whatsapp_template_only_read_all_true_permission = {
    'id' => 7,
    'name' =>  'whatsappTemplate',
    'description' =>  'has access to whatsapp templates',
    'limits' =>  -1,
    'units' =>  'count',
    'action' =>  {
      'readAll' => true,
      'update' => true,
      'read' => true,
      'write' => true
    }
  }

  message_read_all_create_permission = {
    'id' => 8,
    'name' =>  'sms',
    'description' =>  'has access to message',
    'limits' =>  -1,
    'units' =>  'count',
    'action' =>  {
      'readAll' => true,
      'read' => true,
      'write' => true
    }
  }

  message_read_create_permission = {
    'id' => 8,
    'name' =>  'sms',
    'description' =>  'has access to message',
    'limits' =>  -1,
    'units' =>  'count',
    'action' =>  {
      'read' => true,
      'write' => true
    }
  }

  lead_read_all_permission = {
    "id" => 4,
    "name" =>  "lead",
    "description" =>  "has access to lead",
    "limits" =>  -1,
    "units" =>  "count",
    "action" =>  {
      "read" => true,
      "sms" => true,
      "readAll" => true
    }
  }


  factory :auth_token, class: 'Auth::Token' do
    transient do
      expiry { (Time.current + 1.hour).to_i }
      user_id { rand(1000) }
      tenant_id { rand(1000) }
      access_token { SecureRandom.uuid }
      username { '<EMAIL>'}
      token_data {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [lead_sms_true_permission,
            contact_sms_true_permission,
            deal_sms_true_permission,
            message_read_all_create_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id,
          "meta" => { pid: 12 }
          }
      }

      token_data_without_sms {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [lead_sms_false_permission,
            contact_sms_false_permission,
            deal_sms_false_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id,
          "meta" => { pid: 12 }
        }
      }

      token_data_with_sms_delete {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [sms_delete_true_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id,
          "meta" => { pid: 12 }
        }
      }

      token_data_with_sms_delete_all {
        {
          "expiresIn" => 43199,
          "expiry" => expiry,
          "tokenType" => "bearer",
          "accessToken" => access_token,
          "permissions" => [sms_delete_all_true_permission],
          "userId" => user_id,
          "username" => username,
          "tenantId" => tenant_id,
          "meta" => { pid: 12 }
        }
      }

      token_data_with_whatsapp_update_all_read_all {
        {
          'expiresIn' => 43199,
          'expiry' => expiry,
          'tokenType' => 'bearer',
          'accessToken' => access_token,
          'permissions' => [whatsapp_read_all_update_all_true_permission, lead_sms_true_permission, message_read_all_create_permission],
          'userId' => user_id,
          'username' => username,
          'tenantId' => tenant_id,
          'meta' => { pid: 12 }
        }
      }

      token_data_with_message_read {
        {
          'expiresIn' => 43199,
          'expiry' => expiry,
          'tokenType' => 'bearer',
          'accessToken' => access_token,
          'permissions' => [lead_sms_true_permission, message_read_create_permission],
          'userId' => user_id,
          'username' => username,
          'tenantId' => tenant_id,
          'meta' => { pid: 12 }
        }
      }

      token_data_with_whatsapp_template_update_all_read_all {
        {
          'expiresIn' => 43199,
          'expiry' => expiry,
          'tokenType' => 'bearer',
          'accessToken' => access_token,
          'permissions' => [whatsapp_template_read_all_update_all_true_permission, lead_sms_true_permission, message_read_all_create_permission, contact_sms_true_permission, deal_sms_true_permission],
          'userId' => user_id,
          'username' => username,
          'tenantId' => tenant_id,
          'meta' => { pid: 12 }
        }
      }

      token_data_with_whatsapp_template_only_read_all {
        {
          'expiresIn' => 43199,
          'expiry' => expiry,
          'tokenType' => 'bearer',
          'accessToken' => access_token,
          'permissions' => [whatsapp_template_only_read_all_true_permission, lead_sms_true_permission, message_read_all_create_permission],
          'userId' => user_id,
          'username' => username,
          'tenantId' => tenant_id,
          'meta' => { pid: 12 }
        }
      }

      token_data_with_message_and_lead_read_all {
        {
          'expiresIn' => 43199,
          'expiry' => expiry,
          'tokenType' => 'bearer',
          'accessToken' => access_token,
          'permissions' => [lead_read_all_permission, message_read_all_create_permission],
          'userId' => user_id,
          'username' => username,
          'tenantId' => tenant_id,
          'meta' => { pid: 12 }
        }
      }

      payload {
        {
          "iss" => "sell",
          "data" => token_data
        }
      }

      payload_without_sms_permission {
        {
          "iss" => "sell",
          "data" => token_data_without_sms
        }
      }

      payload_with_sms_delete_permission {
        {
          "iss" => "sell",
          "data" => token_data_with_sms_delete
        }
      }

      payload_with_sms_delete_all_permission {
        {
          "iss" => "sell",
          "data" => token_data_with_sms_delete_all
        }
      }

      payload_with_whatsapp_update_all_read_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_with_whatsapp_update_all_read_all
        }
      }

      payload_with_message_read_permission {
        {
          'iss' => 'sell',
          'data' => token_data_with_message_read
        }
      }

      payload_with_whatsapp_template_update_all_read_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_with_whatsapp_template_update_all_read_all
        }
      }

      payload_with_whatsapp_template_only_read_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_with_whatsapp_template_only_read_all
        }
      }

      payload_with_message_and_lead_read_all_permission {
        {
          'iss' => 'sell',
          'data' => token_data_with_message_and_lead_read_all
        }
      }
    end

    token { JWT.encode payload, nil, 'none' }

    trait :without_sms_permission do
      token { JWT.encode payload_without_sms_permission, nil, 'none' }
    end

    trait :with_sms_delete_permission do
      token { JWT.encode payload_with_sms_delete_permission, nil, 'none' }
    end

    trait :with_sms_delete_all_permission do
      token { JWT.encode payload_with_sms_delete_all_permission, nil, 'none' }
    end

    trait :with_whatsapp_update_all_read_all_permission do
      token { JWT.encode payload_with_whatsapp_update_all_read_all_permission, nil, 'none' }
    end

    trait :with_whatsapp_template_read_all_update_all_permission do
      token { JWT.encode payload_with_whatsapp_template_update_all_read_all_permission, nil, 'none' }
    end

    trait :with_whatsapp_template_only_read_all_permission do
      token { JWT.encode payload_with_whatsapp_template_only_read_all_permission, nil, 'none' }
    end

    trait :with_meesage_read_permission do
      token { JWT.encode payload_with_message_read_permission, nil, 'none' }
    end

    trait :with_meesage_and_lead_read_all_permission do
      token { JWT.encode payload_with_message_and_lead_read_all_permission, nil, 'none' }
    end

    factory :auth_token_invalid do
      token { 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzZWxsIiwiZGF0YSI6eyJleHBpcmVzSW4iOjQzMTk5LCJleHBpcnkiOjE1NzY0OTM3MTAsInRva2VuVHlwZSI6ImJlYXJlciIsInBlcm1pc3Npb25zIjpbeyJpZCI6NCwibmFtZSI6ImxlYWQiLCJkZXNjcmlwdGlvbiI6ImhhcyBhY2Nlc3MgdG8gbGVhZCByZXNvdXJjZSIsImxpbWl0cyI6LTEsInVuaXRzIjoiY291bnQiLCJhY3Rpb24iOnsicmVhZCI6dHJ1ZSwid3JpdGUiOnRydWUsInVwZGF0ZSI6dHJ1ZSwiZGVsZXRlIjp0cnVlLCJlbWFpbCI6ZmFsc2UsImNhbGwiOmZhbHNlLCJzbXMiOmZhbHNlLCJ0YXNrIjp0cnVlLCJub3RlIjp0cnVlLCJyZWFkQWxsIjp0cnVlLCJ1cGRhdGVBbGwiOnRydWV9fSx7ImlkIjo3LCJuYW1lIjoidGVhbSIsImRlc2NyaXB0aW9uIjoiaGFzIGFjY2VzcyB0byB0ZWFtIHJlc291cmNlIiwibGltaXRzIGlvbiI6eyJyZWFkIjp0cnVlLCJ3cml0ZSI6dHJ1ZSwidXBkYXRlIjp0cnVlLCJkZWxldGUiOnRydWUsImVtYWlsIjpmYWxzZSwiY2FsbCI6ZmFsc2UsInNtcyI6ZmFsc2UsInRhc2siOmZhbHNlLCJub3RlIjpmYWxzZSwicmVhZEFsbCI6dHJ1ZSwidXBkYXRlQWxsIjp0cnVlfX1dLCJ1c2VySWQiOiIxMiIsInVzZXJ_U3W9DSeom8' }
    end

    factory :auth_token_expired do
      expiry { (Time.now - 1.day).to_i}
      token { JWT.encode payload, nil, 'none' }
    end
  end
end
