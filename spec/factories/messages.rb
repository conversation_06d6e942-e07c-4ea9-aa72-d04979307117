# frozen_string_literal: true

FactoryBot.define do
  factory :message do
    medium { 'whatsapp' }
    message_type { 'whatsapp' }
    owner { create(:user) }
    direction { %w[incoming outgoing].sample }
    content { 'Sample text' }
    sent_at { Time.now }
    delivered_at { Time.now + 1.minute }
    read_at { Time.now + 2. minute }
    recipient_number { '12312312' }
    sender_number { '12312313' }
    status { 'sent' }
  end
end
