# frozen_string_literal: true

FactoryBot.define do
  factory :share_rule do
    name { 'Share Rule' }
    description { 'Description' }
    association :created_by, factory: :user
    updated_by { created_by }
    tenant_id { created_by.tenant_id }
    actions { { read: true, update: false } }
    from_id { build(:user, tenant_id: created_by.tenant_id) }
    from_type { 'USER' }
    to_id { build(:user, tenant_id: created_by.tenant_id) }
    to_type { 'TEAM' }
    share_all_records { false }
    entity_type { 'LEAD' }
    entity_id { rand(100) }
  end
end
