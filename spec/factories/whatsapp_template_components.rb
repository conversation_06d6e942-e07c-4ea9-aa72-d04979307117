# frozen_string_literal: true

FactoryBot.define do
  factory :whatsapp_template_component do
    component_type { 'BODY' }
    component_format { 'TEXT' }
    component_text { 'Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.' }
    component_value { nil }
    content { { "body_text" => [["a", "a", "a"]] } }
    position {  }
    tenant_id { whatsapp_template.tenant_id }

    trait :header_template_component do
      component_type { 'HEADER' }
      component_format { 'TEXT' }
      component_text { 'Our {{1}} is on!' }
      content { { "header_text" => ["a"] } }
    end

    trait :header_template_image_component do
      component_type { 'HEADER' }
      component_format { 'IMAGE' }
      media_type { 'STATIC' }
      after(:create) do |component|
        create(:template_media, tenant_id: component.tenant_id, whatsapp_template_component_id: id)
      end
    end

    trait :body_template_component

    trait :footer_template_component do
      component_type { 'FOOTER' }
      component_format { 'TEXT' }
      component_text { 'Use the buttons below to manage your marketing subscriptions' }
      content { {} }
    end

    trait :phone_number_button do
      component_type { 'BUTTON' }
      component_format { 'PHONE_NUMBER' }
      component_text { 'Call Us' }
      component_value { '+919988776655' }
      content { {} }
      position { 1 }
    end

    trait :copy_code_button do
      component_type { 'BUTTON' }
      component_format { 'COPY_CODE' }
      component_text { '250FF' }
      content { {} }
      position { 2 }
    end

    trait :url_button do
      component_type { 'BUTTON' }
      component_format { 'URL' }
      component_text { 'Shop Now' }
      component_value { 'https://www.kylas.io?referral={{1}}' }
      content { ["https://www.kylas.io?referral=a"] }
      position { 3 }
    end

    trait :quick_reply_button do
      component_type { 'BUTTON' }
      component_format { 'QUICK_REPLY' }
      component_text { 'Unsubcribe from Promos' }
      content { {} }
      position { 4 }
    end

    factory :header_component, traits: [:header_template_component]
    factory :header_image_component, traits: [:header_image_component]
    factory :body_component, traits: [:body_template_component]
    factory :footer_component, traits: [:footer_template_component]
    factory :phone_number_component, traits: [:phone_number_button]
    factory :copy_code_component, traits: [:copy_code_button]
    factory :url_component, traits: [:url_button]
    factory :quick_reply_component, traits: [:quick_reply_button]
  end
end
