FactoryBot.define do
  factory :whatsapp_template do
    entity_type { LOOKUP_LEAD }
    name { "Lead Sale Offer #{rand(10000)}" }
    category { 'MARKETING' }
    language { 'en' }
    status { 'DRAFT' }
    whatsapp_template_namespace { WhatsappTemplate.generate_namespace(name) }
    whatsapp_template_id { '' }
    connected_account
    tenant_id { connected_account.tenant_id }
    created_by { connected_account.created_by }
    updated_by { created_by }

    after(:create) do |template|
      create(:header_component, whatsapp_template: template)
      create(:body_component, whatsapp_template: template)
      create(:footer_component, whatsapp_template: template)
      create(:phone_number_component, whatsapp_template: template)
      create(:copy_code_component, whatsapp_template: template)
      create(:url_component, whatsapp_template: template)
      create(:quick_reply_component, whatsapp_template: template)
    end
  end
end
