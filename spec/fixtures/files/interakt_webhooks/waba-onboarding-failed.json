{"event": "WABA_ONBOARDING_FAILED", "isv_name_token": "interakt-isv-token", "waba_id": "155049447701554", "phone_number_id": "190233760846334", "error": {"error": {"message_txt": "Invalid parameter", "type": "OAuthException", "code": 100, "error_subcode": 2593005, "is_transient": false, "error_user_title": "Phone number is not verified", "error_user_msg": "Phone number is not verified through SMS or voice, please use embedded sign-up, WhatsApp Manager, direct sign-up or GraphAPI depending on your use case to verify your phone number first or contact support for help. Learn more", "fbtrace_id": "AbR9yXXXXXXXXXXXXXX"}}}