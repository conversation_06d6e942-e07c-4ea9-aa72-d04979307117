{"content": [{"createdAt": "2022-09-02T13:36:39.135+0000", "updatedAt": "2024-05-27T12:22:51.810+0000", "createdBy": null, "updatedBy": 7638, "recordActions": null, "metaData": null, "id": 7638, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "updatedEmail": null, "salutation": 387687, "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "8308429939", "dialCode": "+91", "primary": true}], "designation": "Product Engineer - 1", "timezone": "Asia/Calcutta", "language": "EN", "dateFormat": "MMM D, YYYY [at] h:mm a", "currency": "INR", "signature": "<div>sdf</div>", "title": null, "department": "", "failedAttempts": 0, "locked": false, "lockedAt": null, "active": true, "deactivateReason": null, "emailVerified": true, "confirmedAt": null, "confirmationTokenSentAt": null, "resetPasswordTokenSentAt": "2024-04-16T07:25:04.059+0000", "unlockTokenSentAt": null, "customFieldValues": null, "profileId": 2, "permissions": [{"id": 9, "name": "searchList", "description": "has access to search list resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 20, "name": "layout", "description": "has permission to layout resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 95, "name": "shift", "description": "has access to Shift", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 7, "name": "team", "description": "has access to team resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 82, "name": "currency", "description": "has access to Currency", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 14, "name": "conversionMapping", "description": "has permission to lead conversion mapping resource", "limits": -1, "units": "count", "action": {"read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 22, "name": "products-services", "description": "has access to Products and Services", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 13, "name": "profile", "description": "has permission to profile resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 31, "name": "email_template", "description": "has access to Email Templates", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 2, "name": "tenant", "description": "has access to tenant resource", "limits": -1, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 15, "name": "task", "description": "has permission to task resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": true, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 24, "name": "lead-capture-forms", "description": "has access to Lead Capture Forms", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 18, "name": "company", "description": "has permission to contact resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true, "reshare": true, "reassign": true}}, {"id": 23, "name": "report", "description": "has access to Reports", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 41, "name": "sms", "description": "has access to Sms", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 8, "name": "config", "description": "has access to tenant configuration resource", "limits": 100, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 11, "name": "shareRule", "description": "has permission to sharing a resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 19, "name": "availability", "description": "has permission to availability resource", "limits": -1, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 10, "name": "pipeline", "description": "has access to pipeline resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 48, "name": "export", "description": "has access to Export", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 56, "name": "quotation", "description": "has access to Quotations", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 84, "name": "security", "description": "has access to Security", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 93, "name": "whatsappBusiness", "description": "has access to WhatsApp Business", "limits": 5, "units": "count", "action": {"read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 34, "name": "meeting", "description": "has access to Meetings", "limits": 5, "units": "count", "action": {"read": false, "write": false, "update": false, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": false}}, {"id": 44, "name": "dashboard", "description": "has access to Dashboard", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 90, "name": "fieldSales", "description": "has access to Field Sales", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 17, "name": "contact", "description": "has permission to contact resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true, "reshare": true, "reassign": true}}, {"id": 54, "name": "webhook", "description": "has access to Webhooks", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 35, "name": "email", "description": "has access to Emails", "limits": 5, "units": "count", "action": {"read": true, "write": false, "update": false, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 3, "name": "user", "description": "has access to user resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 6, "name": "customField", "description": "has access to custom field resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 12, "name": "deal", "description": "has permission to deal resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true, "reshare": true, "reassign": true}}, {"id": 16, "name": "note", "description": "has permission to note resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 29, "name": "workflow", "description": "has access to Workflows", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 38, "name": "call", "description": "has access to Calls", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 73, "name": "goal", "description": "has access to Goals", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 97, "name": "whatsappTemplate", "description": "has access to Whatsapp Templates", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 4, "name": "lead", "description": "has access to lead resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}}, {"id": 71, "name": "document", "description": "has access to Documents", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}], "emailProvider": null, "tenantId": 3440, "reportingManagers": [], "country": "AU", "teams": [{"id": 158, "name": "team 24"}, {"id": 157, "name": "team"}], "tfaEnabled": false, "numberFormat": "INTERNATIONAL_NUMBER_FORMAT", "fieldSalesExecutive": false, "shift": {"id": 31068, "name": "Regular shift"}, "availabilityStatus": {"recordAssignment": {"available": true, "expirationDate": null}, "calling": {"available": true, "expirationDate": null}, "messaging": {"available": true, "expirationDate": null}}, "lastLoggedInAt": "2024-09-08T13:10:25.719+0000"}, {"createdAt": "2022-09-14T13:41:58.193+0000", "updatedAt": "2023-10-13T06:35:14.544+0000", "createdBy": 7638, "updatedBy": 7638, "recordActions": null, "metaData": null, "id": 7755, "firstName": "piyush", "lastName": "kolhe", "email": "<EMAIL>", "updatedEmail": null, "salutation": 387689, "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "**********", "dialCode": "+91", "primary": true}], "designation": "sss", "timezone": "Asia/Calcutta", "language": "EN", "dateFormat": "MMM D, YYYY [at] h:mm a", "currency": "INR", "signature": null, "title": null, "department": "prod", "failedAttempts": 0, "locked": false, "lockedAt": null, "active": false, "deactivateReason": null, "emailVerified": false, "confirmedAt": null, "confirmationTokenSentAt": null, "resetPasswordTokenSentAt": null, "unlockTokenSentAt": null, "customFieldValues": {}, "profileId": 2, "permissions": [{"id": 14, "name": "conversionMapping", "description": "has permission to lead conversion mapping resource", "limits": -1, "units": "count", "action": {"read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 22, "name": "products-services", "description": "has access to Products and Services", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 84, "name": "security", "description": "has access to Security", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 38, "name": "call", "description": "has access to Calls", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 31, "name": "email_template", "description": "has access to Email Templates", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 95, "name": "shift", "description": "has access to Shift", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 73, "name": "goal", "description": "has access to Goals", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 29, "name": "workflow", "description": "has access to Workflows", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 16, "name": "note", "description": "has permission to note resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 11, "name": "shareRule", "description": "has permission to sharing a resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 7, "name": "team", "description": "has access to team resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 12, "name": "deal", "description": "has permission to deal resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true, "reshare": true, "reassign": true}}, {"id": 13, "name": "profile", "description": "has permission to profile resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 8, "name": "config", "description": "has access to tenant configuration resource", "limits": 100, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 6, "name": "customField", "description": "has access to custom field resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 93, "name": "whatsappBusiness", "description": "has access to WhatsApp Business", "limits": 5, "units": "count", "action": {"read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 82, "name": "currency", "description": "has access to Currency", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 34, "name": "meeting", "description": "has access to Meetings", "limits": 5, "units": "count", "action": {"read": false, "write": false, "update": false, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": false}}, {"id": 97, "name": "whatsappTemplate", "description": "has access to Whatsapp Templates", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 19, "name": "availability", "description": "has permission to availability resource", "limits": -1, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 4, "name": "lead", "description": "has access to lead resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": true, "reassign": true}}, {"id": 9, "name": "searchList", "description": "has access to search list resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 54, "name": "webhook", "description": "has access to Webhooks", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 41, "name": "sms", "description": "has access to Sms", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 20, "name": "layout", "description": "has permission to layout resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 23, "name": "report", "description": "has access to Reports", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 2, "name": "tenant", "description": "has access to tenant resource", "limits": -1, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 48, "name": "export", "description": "has access to Export", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 18, "name": "company", "description": "has permission to contact resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true, "reshare": true, "reassign": true}}, {"id": 56, "name": "quotation", "description": "has access to Quotations", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 24, "name": "lead-capture-forms", "description": "has access to Lead Capture Forms", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 90, "name": "fieldSales", "description": "has access to Field Sales", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 35, "name": "email", "description": "has access to Emails", "limits": 5, "units": "count", "action": {"read": true, "write": false, "update": false, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": false, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 10, "name": "pipeline", "description": "has access to pipeline resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}, {"id": 71, "name": "document", "description": "has access to Documents", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 17, "name": "contact", "description": "has permission to contact resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "document": true, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": true, "reshare": true, "reassign": true}}, {"id": 44, "name": "dashboard", "description": "has access to Dashboard", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 15, "name": "task", "description": "has permission to task resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": true, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": true, "quotation": false, "reshare": false, "reassign": false}}, {"id": 3, "name": "user", "description": "has access to user resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reshare": false, "reassign": false}}], "emailProvider": null, "tenantId": 3440, "reportingManagers": [{"id": 7990, "name": "mayur p"}], "country": "AU", "teams": [{"id": 158, "name": "team 24"}, {"id": 157, "name": "team"}], "tfaEnabled": false, "numberFormat": "INTERNATIONAL_NUMBER_FORMAT", "fieldSalesExecutive": false, "shift": null, "availabilityStatus": null, "lastLoggedInAt": null}], "last": true, "totalPages": 1, "totalElements": 2, "sort": null, "size": 20, "number": 0, "numberOfElements": 2, "first": true}