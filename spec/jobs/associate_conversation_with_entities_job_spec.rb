# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AssociateConversationWithEntitiesJob, type: :job do
  include ActiveJob::TestHelper

  let(:user){ create(:user) }
  let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE) }
  let(:conversation){ create(:conversation, tenant_id: user.tenant_id, connected_account_id: connected_account.id, phone_number: '+********', owner_id: user.id) }

  describe '#perform' do
    before(:each) do
      conversation
    end

    context 'when all entities are available in a single page' do
      before(:each) do
        stub_request(:post, 'http://localhost:8083/v1/search/lead?page=0&size=1000&sort=createdAt,asc').with(
          body: {
            fields: %w[id phoneNumbers firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'phoneNumbers',
                  field: 'phoneNumbers',
                  type: 'string',
                  value: '989898'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it 'associates all entities with given phone_number to the conversation' do
        expect{
          described_class.new.perform(conversation.id, 'lead', { dialCode: '+91', value: '989898' })
        }.to change(ConversationLookUp, :count).by(1)
        .and change(LookUp, :count).by(1)
  
        look_up = LookUp.last
        expect(look_up.entity_type).to eq('lead')
        expect(look_up.entity_id).to eq(34343)
        expect(look_up.phone_number).to eq('+********')
        expect(look_up.name).to eq('lead first name')
  
        conversation_look_up = ConversationLookUp.last
        expect(conversation_look_up.conversation_id).to eq(conversation.id)
        expect(conversation_look_up.look_up_id).to eq(look_up.id)
      end
    end

    context 'when all entities are not available in a single page' do
      before(:each) do
        stub_request(:post, 'http://localhost:8083/v1/search/lead?page=0&size=1000&sort=createdAt,asc').with(
          body: {
            fields: %w[id phoneNumbers firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'phoneNumbers',
                  field: 'phoneNumbers',
                  type: 'string',
                  value: '989898'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json
        ).to_return(status: 200, body: {
          "content": [
            {
              "id": 11111,
              "firstName": "lead first name",
              "ownerId": 4010,
              "phoneNumbers": [
                {
                  "code": "IN",
                  "dialCode": "+91",
                  "id": 3490,
                  "type": "MOBILE",
                  "value": "9933445",
                  "primary": true
                }
              ]
            }
          ],
          "totalPages": 2,
          "totalElements": 2
        }.to_json, headers: {})

        stub_request(:post, 'http://localhost:8083/v1/search/lead?page=1&size=1000&sort=createdAt,asc').with(
          body: {
            fields: %w[id phoneNumbers firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'phoneNumbers',
                  field: 'phoneNumbers',
                  type: 'string',
                  value: '989898'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it 'associates all entities with given phone_number to the conversation' do
        expect{
          described_class.new.perform(conversation.id, 'lead', { dialCode: '+91', value: '989898' })
        }.to change(ConversationLookUp, :count).by(2)
        .and change(LookUp, :count).by(2)

        expect(LookUp.all.pluck(:entity_id)).to eq([11111, 34343])
      end
    end
  end
end
