# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CopyTemplateMediaToMessageJob, type: :job do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
  let(:message) { create(:message, tenant_id: user.tenant_id, owner: user) }
  let(:template_media) { create(:template_media, tenant_id: user.tenant_id, file_name: "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/sample_file_#{SecureRandom.uuid}.png") }
  let(:attachment) { create(:attachment, message: message, file_name: 'sample_file.png') }

  before do
    allow(Aws::S3::Resource).to receive(:new).and_return(mock_s3_resource)
    allow(mock_s3_resource).to receive(:bucket).and_return(mock_s3_bucket)
    allow(mock_s3_bucket).to receive(:object).and_return(mock_s3_object)
    allow(mock_s3_object).to receive(:content_length).and_return(1024)
  end

  let(:mock_s3_resource) { instance_double(Aws::S3::Resource) }
  let(:mock_s3_bucket) { instance_double(Aws::S3::Bucket) }
  let(:mock_s3_object) { instance_double(Aws::S3::Object) }

  describe '#perform' do
    context 'when message and template media exist' do
      before do
        message.attachments << attachment
        allow(DateTime).to receive(:now).and_return(DateTime.new(2021, 1, 1, 12, 0, 0))
      end

      it 'copies the media file to the correct destination' do
        expected_destination_key = "tenant_#{user.tenant_id}/user_#{user.id}/attachments/#{message.id}_sample_file_#{DateTime.now.to_i}.png"
        
        expect(mock_s3_object).to receive(:copy_to).with({
          bucket: S3_ATTACHMENT_BUCKET,
          key: expected_destination_key
        })

        described_class.perform_now(message.id, template_media.id)
      end

      it 'updates the attachment with correct file details' do
        expected_destination_key = "tenant_#{user.tenant_id}/user_#{user.id}/attachments/#{message.id}_sample_file_#{DateTime.now.to_i}.png"
        
        expect(mock_s3_object).to receive(:copy_to).with({
          bucket: S3_ATTACHMENT_BUCKET,
          key: expected_destination_key
        }).and_return(mock_s3_object)

        described_class.perform_now(message.id, template_media.id)
        
        attachment.reload
        expect(attachment.file_name).to eq(expected_destination_key)
        expect(attachment.size).to eq(template_media.file_size)
      end
    end

    context 'when message does not exist' do
      it 'returns without performing the job' do
        expect(S3::CopyAttachmentOnS3).not_to receive(:new)
        described_class.perform_now(nil, template_media.id)
      end
    end

    context 'when template media does not exist' do
      it 'returns without performing the job' do
        expect(S3::CopyAttachmentOnS3).not_to receive(:new)
        described_class.perform_now(message.id, nil)
      end
    end

    context 'when media URL is not present' do
      before do
        allow(template_media).to receive(:media_url).and_return({ url: nil })
      end

      it 'does not raise an error' do
        expect {
          described_class.perform_now(message.id, template_media.id)
        }.not_to raise_error
      end
    end
  end
end 