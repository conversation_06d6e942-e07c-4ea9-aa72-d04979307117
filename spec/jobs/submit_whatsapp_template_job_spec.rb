require 'rails_helper'

RSpec.describe SubmitWhatsappTemplateJob, type: :job do
  let(:user) { create(:user) }
  let(:connected_account) { create(:connected_account, created_by: user) }
  let(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: user, status: 'SUBMITTING') }

  describe '#perform_later' do
    before do
      ActiveJob::Base.queue_adapter = :test
    end

    it 'enqueues a SubmitWhatsappTemplateJob in default queue' do
      expect {
        SubmitWhatsappTemplateJob.perform_later(whatsapp_template.id)
      }.to have_enqueued_job.with(whatsapp_template.id).on_queue('default')
    end
  end

  describe '#perform' do
    context 'when whatsapp template with given id does not exist' do
      before do
        expect(Rails.logger).to receive(:error).with('Invalid Whatsapp Template Id - 123 in SubmitWhatsappTemplateJob')
      end

      it 'logs error and returns' do
        SubmitWhatsappTemplateJob.new.perform(123)
      end
    end

    context 'when status of whatsapp template is not SUBMITTING' do
      before do
        whatsapp_template.update!(status: 'PENDING')
        expect(Rails.logger).to receive(:error).with("Cannot submit template with status - PENDING for template Id - #{whatsapp_template.id}")
      end

      it 'loggs error and returns' do
        SubmitWhatsappTemplateJob.new.perform(whatsapp_template.id)
      end
    end

    context 'when template is a text template' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
        .with(
          headers: {
            Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
          },
          body: "{\"name\":\"#{whatsapp_template.whatsapp_template_namespace}\",\"language\":\"en\",\"category\":\"MARKETING\",\"components\":[{\"type\":\"HEADER\",\"format\":\"TEXT\",\"text\":\"Our {{1}} is on!\",\"example\":{\"header_text\":[\"a\"]}},{\"type\":\"BODY\",\"text\":\"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.\",\"example\":{\"body_text\":[[\"a\",\"a\",\"a\"]]}},{\"type\":\"FOOTER\",\"text\":\"Use the buttons below to manage your marketing subscriptions\"},{\"type\":\"BUTTONS\",\"buttons\":[{\"type\":\"PHONE_NUMBER\",\"text\":\"Call Us\",\"phone_number\":\"+************\"},{\"type\":\"COPY_CODE\",\"example\":\"250FF\"},{\"type\":\"URL\",\"text\":\"Shop Now\",\"url\":\"https://www.kylas.io?referral={{1}}\",\"example\":[\"https://www.kylas.io?referral=a\"]},{\"type\":\"QUICK_REPLY\",\"text\":\"Unsubcribe from Promos\"}]}]}"
        )
        .to_return(status: 200, body: file_fixture('facebook/whatsapp_template/create-template-success-response.json').read)
      end

      it 'sends template to facebook and updates status' do
        SubmitWhatsappTemplateJob.new.perform(whatsapp_template.id)
        expect(whatsapp_template.reload.status).to eq('PENDING')
      end

      context 'when facebook request fails' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: "{\"name\":\"#{whatsapp_template.whatsapp_template_namespace}\",\"language\":\"en\",\"category\":\"MARKETING\",\"components\":[{\"type\":\"HEADER\",\"format\":\"TEXT\",\"text\":\"Our {{1}} is on!\",\"example\":{\"header_text\":[\"a\"]}},{\"type\":\"BODY\",\"text\":\"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.\",\"example\":{\"body_text\":[[\"a\",\"a\",\"a\"]]}},{\"type\":\"FOOTER\",\"text\":\"Use the buttons below to manage your marketing subscriptions\"},{\"type\":\"BUTTONS\",\"buttons\":[{\"type\":\"PHONE_NUMBER\",\"text\":\"Call Us\",\"phone_number\":\"+************\"},{\"type\":\"COPY_CODE\",\"example\":\"250FF\"},{\"type\":\"URL\",\"text\":\"Shop Now\",\"url\":\"https://www.kylas.io?referral={{1}}\",\"example\":[\"https://www.kylas.io?referral=a\"]},{\"type\":\"QUICK_REPLY\",\"text\":\"Unsubcribe from Promos\"}]}]}"
          )
          .to_return(status: 400, body: file_fixture('facebook/whatsapp_template/invalid-template-response.json').read)
        end

        it 'updates status to rejected' do
          SubmitWhatsappTemplateJob.new.perform(whatsapp_template.id)
          expect(whatsapp_template.reload.status).to eq('REJECTED')
          expect(whatsapp_template.reload.reason).to eq('Invalid parameter. Message template "components" param is missing expected field(s), component of type BUTTONS is missing expected field(s) (example)')
        end
      end
    end

    context 'when template is media template' do
      let(:s3_downloader) { instance_double('S3::DownloadFileFromS3') }

      before do
        header_component = whatsapp_template.components.find_by(component_type: HEADER)
        template_media = create(:template_media, file_name: 'sample_file.png', file_size: 30303, file_type: 'image/png', tenant_id: header_component.tenant_id)
        header_component.update!(component_value: template_media.id, component_format: IMAGE, media_type: 'STATIC')
        allow(S3::DownloadFileFromS3).to receive(:new).with('sample_file.png').and_return(s3_downloader)
        allow(s3_downloader).to receive(:call)

        allow(File).to receive(:open).with('sample_file.png', 'rb').and_return(StringIO.new('File Content'))

        stub_request(:post, "https://graph.facebook.com/v19.0/#{ENV['FACEBOOK_CLIENT_ID']}/uploads?file_length=30303&file_name=sample_file.png&file_type=image/png").with(
          headers: {
            'Authorization'=>"Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('facebook/upload_template_media/upload-session-start-response.json').read, headers: {})

        stub_request(:post, "https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZW50OjZlMmZhMjhlLTBjMDYtNGRkMS04YzQ0LWI5ZTM5NjEzZGNjZj9maWxlX25hbWU9U2FtcGxlUE5HSW1hZ2VfM21iLnBuZyZmaWxlX2xlbmd0aD0zMTI0MjAxJmZpbGVfdHlwZT1pbWFnZSUyRnBuZw==?sig=ARYJiWLJenUxDYvB6js").with(
          body: "File Content",
          headers: {
          'Authorization'=>"OAuth #{ConnectedAccount.decrypt(connected_account.access_token)}",
          'Content-Type'=>'multipart/formdata',
          'File-Offset'=>'0',
          }
        ).to_return(status: 200, body: file_fixture('facebook/upload_template_media/template-media-upload-response.json').read, headers: {})

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates").with(
          body: "{\"name\":\"#{whatsapp_template.whatsapp_template_namespace}\",\"language\":\"en\",\"category\":\"MARKETING\",\"components\":[{\"type\":\"HEADER\",\"format\":\"IMAGE\",\"example\":{\"header_handle\":[\"4:U2FtcGxlUE5HSW1hZ2U=:aW1hZ2UvcG5n:ARalfyNs-W_g8DH3iRWFn43YeawePd0Q0AoJpTjvP4GaLT6buwavxAscryMY6OIpK0HQ2GGybB-jv4n5-YZ2HlncEynNqLEbGE8efcsmdWkoJQ:e:**********:***************:**************\"]}},{\"type\":\"BODY\",\"text\":\"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.\",\"example\":{\"body_text\":[[\"a\",\"a\",\"a\"]]}},{\"type\":\"FOOTER\",\"text\":\"Use the buttons below to manage your marketing subscriptions\"},{\"type\":\"BUTTONS\",\"buttons\":[{\"type\":\"PHONE_NUMBER\",\"text\":\"Call Us\",\"phone_number\":\"+************\"},{\"type\":\"COPY_CODE\",\"example\":\"250FF\"},{\"type\":\"URL\",\"text\":\"Shop Now\",\"url\":\"https://www.kylas.io?referral={{1}}\",\"example\":[\"https://www.kylas.io?referral=a\"]},{\"type\":\"QUICK_REPLY\",\"text\":\"Unsubcribe from Promos\"}]}]}",
          headers: {
            'Authorization'=>"Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('facebook/whatsapp_template/create-template-success-response.json').read, headers: {})
      end

      it 'updates media to facebook and then sends template for approval' do
        SubmitWhatsappTemplateJob.new.perform(whatsapp_template.id)
        expect(whatsapp_template.reload.status).to eq('PENDING')
      end
    end
  end
end
