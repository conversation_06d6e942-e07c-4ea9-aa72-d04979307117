# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TenantWiseUpdateWhatsappCreditsJob, type: :job do
  include ActiveJob::TestHelper

  let(:user){ create(:user) }
  let(:start_time) { Date.yesterday.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
  let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
  let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: start_time) }
  let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE, credits_revised_upto: start_time) }

  def stub_fetch_conversation_analytics_request(status_code: , connected_account: , start_time: , end_time: , response_body: )
    stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.waba_id}?fields=pricing_analytics.start(#{start_time}).end(#{end_time}).granularity(DAILY).phone_numbers([\"#{connected_account.waba_number}\"]).dimensions(%5B%22PRICING_CATEGORY%22,%22PRICING_TYPE%22,%22COUNTRY%22,%22PHONE%22%5D)")
    .with(
      headers: {
        'Content-Type'=>'application/json',
        'X-Access-Token'=>'partner-token',
        'X-Waba-Id'=> connected_account.waba_id
      }
    ).to_return(status: status_code, body: response_body)
  end

  describe '#perform' do
    before(:each) do
      stub_fetch_conversation_analytics_request(
        status_code: 200,
        connected_account: connected_account,
        start_time: start_time,
        end_time: end_time,
        response_body: file_fixture('interakt/conversation_analytics/api-success-response.json')
      )

      whatsapp_credit

      allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
    end

    it 'updates tenant whatsapp credits and add whatsapp credit history logs' do
      expect{ described_class.new.perform(user.tenant_id) }.to change(WhatsappCreditHistory, :count).by(1)
      expect(WhatsappCredit.last.consumed).to eq(2.75)
      expect(WhatsappCreditHistory.last.balance).to eq(997.25)
      expect(connected_account.reload.credits_revised_upto).to eq(end_time)
    end
  end
end
