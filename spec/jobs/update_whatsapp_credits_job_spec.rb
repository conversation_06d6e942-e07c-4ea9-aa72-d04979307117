# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateWhatsappCreditsJob, type: :job do
  include ActiveJob::TestHelper

  let(:user){ create(:user) }
  let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE) }
  let(:second_user){ create(:user) }
  let(:second_connected_account){ create(:connected_account, tenant_id: second_user.tenant_id, created_by: second_user, status: ACTIVE) }
  let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0) }

  describe '#perform' do
    before(:each) do
      connected_account
      second_connected_account
    end

    it 'schedules tenant wise update whatsapp credits job' do
      expect{ described_class.new.perform }.to have_enqueued_job(TenantWiseUpdateWhatsappCreditsJob).on_queue('default').exactly(:twice)
    end
  end
end
