# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UploadMediaAttachmentToS3Job, type: :job do
  include ActiveJob::TestHelper

  let(:user){ create(:user) }
  let(:connected_account){ create(:connected_account, tenant_id: user.tenant_id, created_by: user, status: ACTIVE) }
  let(:image_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  let(:media_url) { "https://lookaside.fbsbx.com/whatsapp_business/attachments/?mid=***************&ext=**********&hash=ATvGO4vAkFG17B3An7azeX8Wg8xiJlmg8GxrAscU1XdUFA" }

  describe '#perform' do
    context 'success' do
      before(:each) do
        connected_account
        look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
        @message = create(:message, tenant_id: user.tenant_id, owner: user, remote_id: 'wamid.***********************************************************************', direction: 0)
        @message.recipients = [look_up]
        @message.related_to = [look_up]
        create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.jpg')

        stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/media/***************")
          .with(
            headers: {
              'Content-Type'=>'application/json',
              'X-Access-Token'=>'partner-token',
              'X-Waba-Id'=> connected_account.waba_id
            }
          ).to_return(status: 200, body: file_fixture('interakt/media/get-media-url-response.json'))

        stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/media?url=#{media_url}")
          .with(
            headers: {
              'Content-Type'=>'application/json',
              'X-Access-Token'=>'partner-token',
              'X-Waba-Id'=> connected_account.waba_id
            }
          ).to_return(status: 200, body: File.open(image_file.path).read, headers: { 'content-type': 'image/jpeg', 'content-disposition': 'inline;filename=File.jpg'})


        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET).and_return(s3_instance)
        expect(s3_instance).to receive(:call)
      end

      context 'when raise_message_created_events is true' do
        before(:each) do
          expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
          expect(Publishers::MessageCreated).to receive(:call).once
        end

        it 'uploads media attachment to S3 server and update message attachment' do
          described_class.new.perform(connected_account.id, @message.id, ***************, true)

          message_attachment = @message.attachments.last
          expect(message_attachment.file_name).to include("tenant_#{@message.tenant_id}/user_#{@message.owner_id}/attachments/#{@message.id}_File_")
        end
      end

      context 'when message raise_message_created_events is false' do
        before(:each) do
          expect(Publishers::MessageReceivedFromEntityPublisher).not_to receive(:call)
          expect(Publishers::MessageCreated).not_to receive(:call)
        end

        it 'should not raise message created events' do
          described_class.new.perform(connected_account.id, @message.id, ***************, false)

          message_attachment = @message.attachments.last
          expect(message_attachment.file_name).to include("tenant_#{@message.tenant_id}/user_#{@message.owner_id}/attachments/#{@message.id}_File_")
        end
      end
    end

    context 'error' do
      context 'when connected account not found' do
        it 'logs error message on rails logger' do
          expect(Rails.logger).to receive(:error).with("UploadMediaAttachmentToS3Job | Connected account not found with given id: 234")
          expect { described_class.new.perform(234, 123, ***************, true) }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
        end
      end

      context 'when message not found' do
        before { connected_account }

        it 'logs error message on rails logger' do
          expect(Rails.logger).to receive(:error).with("UploadMediaAttachmentToS3Job | Message not found with given message id: 123, tenant_id: #{connected_account.tenant_id}")
          expect { described_class.new.perform(connected_account.id, 123, ***************, true) }.to raise_error(ExceptionHandler::NotFound, '022006||Message not found')
        end
      end
    end
  end
end
