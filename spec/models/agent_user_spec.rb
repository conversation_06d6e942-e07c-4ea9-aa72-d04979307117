# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AgentUser, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:id)
        .with_options(null: false, primary: true)
    end

    it do
      should have_db_column(:connected_account_id)
        .of_type(:integer)
        .with_options(null: false)
    end

    it do
      should have_db_column(:user_id)
        .of_type(:integer)
        .with_options(null: false)
    end

    it do
      should have_db_column(:created_at)
        .of_type(:datetime)
    end

    it do
      should have_db_column(:updated_at)
        .of_type(:datetime)
    end
  end

  describe 'associations' do
    it 'belongs to connected account' do
      agent_user_connected_account = described_class.reflect_on_association(:connected_account)
      expect(agent_user_connected_account.macro).to eq(:belongs_to)
    end

    it 'belongs to user' do
      agent_user_user = described_class.reflect_on_association(:user)
      expect(agent_user_user.macro).to eq(:belongs_to)
    end
  end
end
