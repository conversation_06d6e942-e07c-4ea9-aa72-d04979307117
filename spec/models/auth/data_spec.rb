require 'rails_helper'

RSpec.describe Auth::Data,type: :model do

  before do
    @auth_data = User::TokenParser.parse(get_test_jwt)
  end

  describe 'validations' do

    context 'with valid data' do
      it 'should be valid' do
        expect(@auth_data).to be_valid
      end
    end

    context 'with invalid data' do
      %w{expires_in access_token expiry token_type user_id tenant_id permissions}.each do |attr|
        it "should validate presence of #{attr}" do
          @auth_data.send("#{attr}=", nil)
          expect(@auth_data).not_to be_valid
        end
      end
    end
  end

  describe 'instance_methods' do
    context 'without permission name' do
      it 'should return false' do
        expect(@auth_data.can_access?(nil)).to be_falsey
      end
    end

    context 'with permission name' do
      it 'should return true if user have access' do
        expect(@auth_data.can_access?('user', 'read')).to be_truthy
      end
      it 'should return false if user have access' do
        expect(@auth_data.can_access?('user', 'delete_all')).to be_falsey
      end
    end
  end
end
