require 'rails_helper'

RSpec.describe Auth::PermissionAction,type: :model do

  before do
    auth_data = User::TokenParser.parse(get_test_jwt)
    @permission_action = auth_data.permissions.first.action
  end

  describe 'validations' do

    context 'with valid permission action' do
      it 'should be valid' do
        expect(@permission_action).to be_valid
      end
    end

    context 'with invalid data' do
      %w{read write update delete delete_all email call sms task note read_all update_all}.each do |attr|
        it "should validate presence of #{attr}" do
          @permission_action.send("#{attr}=", nil)
          expect(@permission_action).not_to be_valid
        end
      end
    end
  end
end
