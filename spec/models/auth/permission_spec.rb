require 'rails_helper'

RSpec.describe Auth::Permission,type: :model do

  before do
    auth_data = User::TokenParser.parse(get_test_jwt)
    @permission = auth_data.permissions.first
  end

  describe 'validations' do

    context 'with valid permission' do
      it 'should be valid' do
        expect(@permission).to be_valid
      end
    end

    context 'with invalid data' do
      %w{id name description limits units action}.each do |attr|
        it "should validate presence of #{attr}" do
          @permission.send("#{attr}=", nil)
          expect(@permission).not_to be_valid
        end
      end
    end
  end

  describe 'instance_methods' do
    it 'should return true if user have access' do
      expect(@permission.can_access?('read')).to be_truthy
    end
    it 'should return false if user have access' do
      expect(@permission.can_access?('delete_all')).to be_falsey
    end
  end
end
