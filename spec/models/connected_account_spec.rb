# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ConnectedAccount, type: :model do
  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:name)
          .of_type(:string)
      end

      it do
        should have_db_column(:access_token)
          .of_type(:string)
      end

      it do
        should have_db_column(:waba_number)
          .of_type(:string)
      end

      it do
        should have_db_column(:status)
          .of_type(:string)
      end

      it do
        should have_db_column(:created_by_id)
          .of_type(:integer)
      end

      it do
        should have_db_column(:updated_by_id)
          .of_type(:integer)
      end

      it do
        should have_db_column(:tenant_id)
          .of_type(:integer)
      end

      it do
        should have_db_column(:created_at)
          .of_type(:datetime)
      end

      it do
        should have_db_column(:updated_at)
          .of_type(:datetime)
      end
    end

    describe 'associations' do
      it 'has many lead agent users' do
        connected_account_lead_agent_users = described_class.reflect_on_association(:lead_agent_users)
        expect(connected_account_lead_agent_users.macro).to eq(:has_many)
        expect(connected_account_lead_agent_users.options).to eq({ class_name: 'AgentUser' })
      end

      it 'has many lead agents' do
        connected_account_lead_agents = described_class.reflect_on_association(:lead_agents)
        expect(connected_account_lead_agents.macro).to eq(:has_many)
        expect(connected_account_lead_agents.options).to eq({ through: :lead_agent_users, source: :user })
      end

      it 'has many contact agent users' do
        connected_account_contact_agent_users = described_class.reflect_on_association(:contact_agent_users)
        expect(connected_account_contact_agent_users.macro).to eq(:has_many)
        expect(connected_account_contact_agent_users.options).to eq({ class_name: 'AgentUser' })
      end

      it 'has many contact agents' do
        connected_account_contact_agents = described_class.reflect_on_association(:contact_agents)
        expect(connected_account_contact_agents.macro).to eq(:has_many)
        expect(connected_account_contact_agents.options).to eq({ through: :contact_agent_users, source: :user })
      end

      it 'has one lead field mapping' do
        connected_account_lead_field_mapping = described_class.reflect_on_association(:lead_field_mapping)
        expect(connected_account_lead_field_mapping.macro).to eq(:has_one)
        expect(connected_account_lead_field_mapping.options).to eq({ class_name: 'FieldMapping' })
      end

      it 'has one contact field mapping' do
        connected_account_contact_field_mapping = described_class.reflect_on_association(:contact_field_mapping)
        expect(connected_account_contact_field_mapping.macro).to eq(:has_one)
        expect(connected_account_contact_field_mapping.options).to eq({ class_name: 'FieldMapping' })
      end

      it 'belongs to created by' do
        connected_account_created_by = described_class.reflect_on_association(:created_by)
        expect(connected_account_created_by.macro).to eq(:belongs_to)
      end

      it 'belongs to updated by' do
        connected_account_updated_by = described_class.reflect_on_association(:updated_by)
        expect(connected_account_updated_by.macro).to eq(:belongs_to)
      end
    end

    describe 'validations' do
      context 'when user tries to add same phone number id for same tenant' do
        it 'should throw error' do
          connected_account = create(:connected_account)
          connected_account_2 = build(:connected_account, phone_number_id: connected_account.phone_number_id)
          expect(connected_account_2).to be_invalid
          expect(connected_account_2.errors.full_messages).to include('Phone number has already been taken')
        end
      end

      context 'when user tries to add entity other than lead/contact' do
        it 'should be invalid' do
          connected_account = build(:connected_account, entities_to_create: %w[lead leads])
          expect(connected_account).to be_invalid
          expect(connected_account.errors.full_messages).to include('Entities to create ["lead", "leads"] is not included in the list.')
        end
      end

      context 'when user tries to add correct entity' do
        it 'should be valid' do
          connected_account = build(:connected_account, entities_to_create: %w[lead])
          expect(connected_account).to be_valid
        end
      end

      context 'when user tries to add incorrect status' do
        it 'should be invalid' do
          connected_account = build(:connected_account, entities_to_create: %w[lead], status: 'demo')
          expect(connected_account).to be_invalid
          expect(connected_account.errors.full_messages).to include('Status demo is invalid')
        end
      end

      context 'when user tries to add correct status' do
        it 'should be valid' do
          connected_account = build(:connected_account, entities_to_create: %w[lead], status: ACTIVE)
          expect(connected_account).to be_valid
        end
      end
    end

    describe '.encrypt' do
      before do
        allow(ENV).to receive(:[]).with('MESSAGE_CREDENTIAL_ENCRYPTION_SECRET').and_return('abcdef1234567890')
        allow(ENV).to receive(:[]).with('MESSAGE_CREDENTIAL_ENCRYPTION_IV').and_return('1234567890abcdef')
      end

      it 'encrypts token' do
        expect(described_class.encrypt('123')).to eq("+1VJeDWucMyl61kzjZrmUQ==\n")
      end
    end

    describe '.descrypt' do
      before do
        allow(ENV).to receive(:[]).with('MESSAGE_CREDENTIAL_ENCRYPTION_SECRET').and_return('abcdef1234567890')
        allow(ENV).to receive(:[]).with('MESSAGE_CREDENTIAL_ENCRYPTION_IV').and_return('1234567890abcdef')
      end

      it 'decrypts token' do
        expect(described_class.decrypt("+1VJeDWucMyl61kzjZrmUQ==\n")).to eq('123')
      end
    end
  end
end
