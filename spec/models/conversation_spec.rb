# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Conversation, type: :model do
  let(:conversation) { create(:conversation) }

  describe 'associations' do
    it do
      should have_many(:conversation_look_ups)
    end

    it do
      should have_many(:look_ups)
        .through(:conversation_look_ups)
    end

    it do
      should have_many(:sub_conversations)
    end
  end

  describe 'validations' do
    context 'when phone number is repeated for a particular tenant_id and connected_account_id' do

      it 'should be invalid' do
        new_conversation = build(:conversation, connected_account_id: conversation.connected_account_id, tenant_id: conversation.tenant_id, phone_number: conversation.phone_number)

        expect(new_conversation).to be_invalid
        expect(new_conversation.errors.messages[:phone_number])
      end
    end
  end
end
