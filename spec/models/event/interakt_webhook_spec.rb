# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::InteraktWebhook do
  let(:event) { described_class.new(JSON.parse(file_fixture('interakt_webhooks/waba-onboarding-failed.json').read)) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('message.interakt.webhook')
    end
  end

  describe '#to_json' do
    it 'returns json of data' do
      expect(JSON.parse(event.to_json)).to eq({
        "event" => "WABA_ONBOARDING_FAILED",
        "isv_name_token" => "interakt-isv-token",
        "waba_id" => "***************",
        "phone_number_id" => "***************",
        "error" => {
          "error" => {
            "message_txt" => "Invalid parameter",
            "type" => "OAuthException",
            "code" => 100,
            "error_subcode" => 2593005,
            "is_transient" => false,
            "error_user_title" => "Phone number is not verified",
            "error_user_msg" => "Phone number is not verified through SMS or voice, please use embedded sign-up, WhatsApp Manager, direct sign-up or GraphAPI depending on your use case to verify your phone number first or contact support for help. Learn more",
            "fbtrace_id" => "AbR9yXXXXXXXXXXXXXX"
          }
        }
      })
    end
  end
end
