# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::WhatsappTemplateStatusUpdated do
  let(:whatsapp_template) { create(:whatsapp_template) }
  let(:event) { described_class.new(whatsapp_template) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('whatsapp.template.status.updated')
    end
  end

  describe '#to_json' do
    it 'returns json of data' do
      expect(JSON.parse(event.to_json)).to eq({
        'id' => whatsapp_template.id,
        'name' => whatsapp_template.name,
        'status' => whatsapp_template.status,
        'connectedAccount' => whatsapp_template.connected_account.display_name,
        'reason' => whatsapp_template.reason,
        'additionalInfo' => whatsapp_template.additional_info,
        'userId' => whatsapp_template.created_by_id,
        'tenantId' => whatsapp_template.tenant_id
      })
    end
  end
end
