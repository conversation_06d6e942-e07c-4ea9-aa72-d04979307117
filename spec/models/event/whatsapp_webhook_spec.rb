# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::WhatsappWebhook do
  let(:event) { described_class.new(JSON.parse(file_fixture('webhooks/whatsapp-template-approved.json').read)) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('message.whatsapp.webhook')
    end
  end

  describe '#to_json' do
    it 'returns json of data' do
      expect(event.to_json).to eq(JSON.parse(file_fixture('webhooks/whatsapp-template-approved.json').read).to_json)
    end
  end
end
