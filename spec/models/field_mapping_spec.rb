# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FieldMapping, type: :model do
  describe 'db schema' do
    it do
      should have_db_column(:id)
        .with_options(null: false, primary: true)
    end

    it do
      should have_db_column(:connected_account_id)
        .of_type(:integer)
        .with_options(null: false)
    end

    it do
      should have_db_column(:campaign)
        .of_type(:integer)
    end

    it do
      should have_db_column(:source)
        .of_type(:integer)
    end

    it do
      should have_db_column(:sub_source)
        .of_type(:string)
    end

    it do
      should have_db_column(:utm_campaign)
        .of_type(:string)
    end

    it do
      should have_db_column(:utm_content)
        .of_type(:string)
    end

    it do
      should have_db_column(:utm_medium)
        .of_type(:string)
    end

    it do
      should have_db_column(:utm_source)
        .of_type(:string)
    end

    it do
      should have_db_column(:utm_term)
        .of_type(:string)
    end

    it do
      should have_db_column(:created_at)
        .of_type(:datetime)
    end

    it do
      should have_db_column(:updated_at)
        .of_type(:datetime)
    end
  end

  describe 'associations' do
    it 'belongs to connected account' do
      field_mapping_connected_account = described_class.reflect_on_association(:connected_account)
      expect(field_mapping_connected_account.macro).to eq(:belongs_to)
    end
  end

  describe 'validations' do
    let(:field_mapping) { build(:field_mapping) }

    context 'when entity type is lead, contact' do
      it 'returns field mapping is valid' do
        expect(field_mapping).to be_valid
      end
    end

    context 'when other entity types' do
      before { field_mapping.entity_type = LOOKUP_DEAL }

      it 'returns field mapping is invalid' do
        expect(field_mapping).to be_invalid
        expect(field_mapping.errors.full_messages.to_sentence).to eq('Entity type is not included in the list')
      end
    end
  end
end
