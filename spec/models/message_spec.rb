require 'rails_helper'

RSpec.describe Message, type: :model do
  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end
      it do
        should have_db_column(:content)
          .of_type(:text)
      end
      it do
        should have_db_column(:medium)
          .of_type(:string)
      end
      it do
        should have_db_column(:message_type)
          .of_type(:string)
      end
      it do
        should have_db_column(:owner_id)
          .of_type(:integer).with_options(null: false)
      end
      it do
        should have_db_column(:sent_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:delivered_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:read_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:recipient_number)
          .of_type(:string)
      end
      it do
        should have_db_column(:sender_number)
          .of_type(:string)
      end
      it do
        should have_db_column(:status)
          .of_type(:integer)
      end
      it do
        should have_db_column(:created_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:updated_at)
          .of_type(:datetime)
      end
      it do
        should have_db_column(:tenant_id)
          .of_type(:integer)
      end
      it do
        should have_db_column(:remote_id)
          .of_type(:string)
      end
      it do
        should have_db_column(:plain_text_content)
          .of_type(:text)
      end
    end

    describe 'validations' do
      context 'when message type is invalid' do
        it 'is invalid' do
          message = build(:message, message_type: 'INVALID')
          expect(message).to be_invalid
          expect(message.errors.messages[:message_type]).to eq(['is not included in the list'])
        end
      end

      context 'when message type is blank' do
        it 'is valid' do
          message = build(:message, message_type: nil)
          expect(message).to be_valid
        end
      end

      context 'when message type is valid' do
        it 'is valid' do
          message = build(:message, message_type: WHATSAPP)
          expect(message).to be_valid
        end
      end

      context 'when message type is whatsapp business' do
        it 'validates presence of connected account id' do
          message = build(:message, message_type: WHATSAPP_BUSINESS)
          expect(message).to be_invalid
          expect(message.errors.full_messages.to_sentence).to eq("Connected account can't be blank")
        end
      end
    end

    describe 'associations' do
      it do
        should have_many(:related_look_ups)
          .conditions(related: true)
          .class_name('MessageLookUp')
      end
      it do
        should have_many(:recipient_look_ups)
          .conditions(recipient: true)
          .class_name('MessageLookUp')
      end
      it do
        should have_many(:related_to)
          .source('look_up')
          .through('related_look_ups')
      end
      it do
        should have_many(:recipients)
          .source('look_up')
          .through('recipient_look_ups')
      end
      it do
        should have_many(:attachments)
      end
      it do
        should belong_to(:owner)
          .class_name('User')
      end

      it do
        message_connected_account = Message.reflect_on_association(:connected_account)
        expect(message_connected_account.macro).to eq(:belongs_to)
        expect(message_connected_account.options).to eq({ optional: true })
      end
    end

    describe 'callbacks' do
      context 'after_create' do
        context 'when message_type is whatsapp_business' do
          let(:connected_account){create(:connected_account)}

          context 'when content is present' do
            let(:message){create(:message, content: "*Test* Product", message_type: WHATSAPP_BUSINESS, connected_account_id: connected_account.id)}

            it 'should store content in plain text' do
              expect(message.plain_text_content).to eq("Test Product")
            end
          end

          context 'when content is not present' do
            let(:message){create(:message, content: nil, message_type: WHATSAPP_BUSINESS, connected_account_id: connected_account.id)}

            it 'should not store content in plain text' do
              expect(message.plain_text_content).to eq(nil)
            end
          end
        end

        context 'when message_type is NOT whatsapp_business' do
          let(:message){create(:message, content: 'Test Prodcut', message_type: SMS)}

          it 'should not store content in plain text' do
            expect(message.plain_text_content).to eq(nil)
          end
        end
      end
    end
  end
end
