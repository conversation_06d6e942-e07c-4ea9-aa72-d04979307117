# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ShareRule, type: :model do
  describe 'entity definitions' do
    describe 'fields & validations' do
      it do
        should validate_presence_of(:tenant_id)
      end

      it do
        should validate_presence_of(:from_id)
      end

      it do
        should validate_presence_of(:to_id)
      end

      it 'validates value of from_type and to_type' do
        share_rule = build(:share_rule, from_type: 'invalid', to_type: 'invalid')
        expect(share_rule).to be_invalid
        expect(share_rule.errors.messages[:from_type]).to eq(['is not included in the list'])
        expect(share_rule.errors.messages[:to_type]).to eq(['is not included in the list'])
      end

      it 'validates uniqueness of share rule id and entity type' do
        create(:share_rule, from_type: 'USER', to_type: 'USER', share_rule_id: 123, entity_type: 'lead', from_id: 7638, to_id: 2232)
        second_share_rule = build(:share_rule, from_type: 'USER', to_type: 'USER', share_rule_id: 123, entity_type: 'lead', from_id: 7638, to_id: 2232)
        expect(second_share_rule).to be_invalid
        expect(second_share_rule.errors.messages[:share_rule_id]).to eq(["has already been taken"])
      end
    end

    describe '#associations' do
      it { should belong_to(:created_by) }

      it { should belong_to(:updated_by) }
    end
  end
end
