# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TemplateMedia do
  describe 'validations' do
    let(:template_media) { create(:template_media) }

    it 'is valid' do
      expect(template_media).to be_valid
    end

    it 'validates presence of file_name' do
      template_media.file_name = nil
      expect(template_media).to be_invalid
      expect(template_media.errors.full_messages.to_sentence).to eq('File name can\'t be blank')
    end

    it 'validates presence of file_type' do
      template_media.file_type = 'invalid'
      expect(template_media).to be_invalid
      expect(template_media.errors.full_messages.to_sentence).to eq('File type is not included in the list')
    end

    it 'validates presence of file_size' do
      template_media.file_size = nil
      expect(template_media).to be_invalid
      expect(template_media.errors.full_messages.to_sentence).to eq('File size can\'t be blank')
    end

    it 'validates presence of tenant_id' do
      template_media.tenant_id = nil
      expect(template_media).to be_invalid
      expect(template_media.errors.full_messages.to_sentence).to eq('Tenant can\'t be blank')
    end
  end

  describe 'associations' do
    it 'belongs_to whatsapp_template_component' do
      whatsapp_template_component_association = described_class.reflect_on_association(:whatsapp_template_component)
      expect(whatsapp_template_component_association.macro).to eq(:belongs_to)
    end
  end

  describe '.usage_per_tenant' do
    before do
      create(:template_media, file_name: 'file_1.png', tenant_id: 1, file_size: 100)
      create(:template_media, file_name: 'file_2.png', tenant_id: 1, file_size: 100)
      create(:template_media, file_name: 'file_1.png', tenant_id: 2, file_size: 100)
    end

    context 'when usage per tenant is called without tenant_id' do
      it 'returns usage for all tenants' do
        expect(TemplateMedia.usage_per_tenant).to match_array([
          {:tenantId=>2, :count=>100, :usageEntity=>"STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT"},
          {:tenantId=>1, :count=>200, :usageEntity=>"STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT"}
        ])
      end
    end

    context 'when usage per tenant with tenant id is called' do
      it 'returns usage for given tenant_id' do
        expect(TemplateMedia.usage_per_tenant(1)).to eq([{:tenantId=>1, :count=>200, :usageEntity=>"STORAGE_WHATSAPP_TEMPLATE_ATTACHMENT"}])
      end
    end
  end

  describe '#media_url' do
    let(:template_media) { create(:template_media, file_name: 'tenant_1/connetced_account_2/file/ab/c.png') }

    before do
      s3_instance = instance_double(S3::GetPresignedUrl)
      allow(S3::GetPresignedUrl).to receive(:new).with('tenant_1/connetced_account_2/file/ab/c.png', 'file/ab/c.png', S3_ATTACHMENT_BUCKET, nil).and_return(s3_instance)
      allow(s3_instance).to receive(:call).and_return('https://www.aws.com/file_url.png')
    end

    it 'returns presigned url' do
      expect(template_media.media_url).to eq('https://www.aws.com/file_url.png')
    end
  end
end
