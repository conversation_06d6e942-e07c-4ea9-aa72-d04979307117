# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VariableMapping do
  describe 'Validations' do
    let(:variable_mapping) { create(:variable_mapping, whatsapp_template: create(:whatsapp_template)) }

    it 'is valid' do
      expect(variable_mapping).to be_valid
    end

    context 'when tenant_id is blank' do
      before { variable_mapping.tenant_id = nil }

      it 'is invalid' do
        expect(variable_mapping).to be_invalid
      end
    end

    context 'when component type is invalid' do
      before { variable_mapping.component_type = 'invalid' }

      it 'is invalid' do
        expect(variable_mapping).to be_invalid
      end
    end

    context 'when parent_entity type is invalid' do
      before { variable_mapping.parent_entity = 'invalid' }

      it 'is invalid' do
        expect(variable_mapping).to be_invalid
      end
    end

    context 'when entity type is invalid' do
      before { variable_mapping.entity = 'ownedBy' }

      it 'is invalid' do
        expect(variable_mapping).to be_invalid
      end
    end

    context 'when field type is invalid' do
      before { variable_mapping.field_type = 'invalid' }

      it 'is invalid' do
        expect(variable_mapping).to be_invalid
      end
    end
  end

  describe 'Associations' do
    it 'belongs to whatsapp template' do
      whatsapp_template_relation = described_class.reflect_on_association(:whatsapp_template)
      expect(whatsapp_template_relation.macro).to eq(:belongs_to)
    end
  end
end
