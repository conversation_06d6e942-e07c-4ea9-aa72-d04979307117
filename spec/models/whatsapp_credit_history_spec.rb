# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappCreditHistory, type: :model do
  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:tenant_id)
          .with_options(null: false)
      end

      it do
        should have_db_column(:entry_type)
      end

      it do
        should have_db_column(:start_time)
      end

      it do
        should have_db_column(:end_time)
      end

      it do
        should have_db_column(:conversation_count)
      end

      it do
        should have_db_column(:conversation_type)
      end

      it do
        should have_db_column(:phone_number)
      end

      it do
        should have_db_column(:country)
      end

      it do
        should have_db_column(:value)
      end

      it do
        should have_db_column(:balance)
      end
    end

    describe 'validations' do
      context 'when tenant_id is not present' do
        it 'is invalid' do
          whatsapp_credit_history = build(:whatsapp_credit_history, tenant_id: nil)
          expect(whatsapp_credit_history).to be_invalid
          expect(whatsapp_credit_history.errors.messages[:tenant_id]).to eq(["can't be blank"])
        end
      end

      context 'when entry_type is invalid' do
        it 'is invalid' do
          whatsapp_credit_history = build(:whatsapp_credit_history, entry_type: 'INVALID')
          expect(whatsapp_credit_history).to be_invalid
          expect(whatsapp_credit_history.errors.messages[:entry_type]).to eq(['is not included in the list'])
        end
      end

      context 'when entry_type is valid' do
        it 'is valid' do
          whatsapp_credit_history = build(:whatsapp_credit_history, entry_type: CREDITS_ADDED)
          expect(whatsapp_credit_history).to be_valid
        end
      end
    end
  end
end
