# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappCredit, type: :model do
  describe 'Entity definitions' do
    describe 'db schema' do
      it do
        should have_db_column(:id)
          .with_options(null: false, primary: true)
      end

      it do
        should have_db_column(:tenant_id)
          .with_options(null: false)
      end

      it do
        should have_db_column(:consumed)
      end

      it do
        should have_db_column(:credits_revised_at)
      end

      it do
        should have_db_column(:parked)
      end
    end

    describe 'validations' do
      context 'when consumed is less than 0' do
        it 'is invalid' do
          whatsapp_credit = build(:whatsapp_credit, consumed: -1)
          expect(whatsapp_credit).to be_invalid
          expect(whatsapp_credit.errors.messages[:consumed]).to eq(['must be greater than or equal to 0'])
        end
      end

      context 'when consumed is greater than or equal to 0' do
        it 'is valid' do
          whatsapp_credit = build(:whatsapp_credit)
          expect(whatsapp_credit).to be_valid
        end
      end

      context 'when tenant_id is not present' do
        it 'is invalid' do
          whatsapp_credit = build(:whatsapp_credit, tenant_id: nil)
          expect(whatsapp_credit).to be_invalid
          expect(whatsapp_credit.errors.messages[:tenant_id]).to eq(["can't be blank"])
        end
      end

      context 'when parked is less than 0' do
        it 'is invalid' do
          whatsapp_credit = build(:whatsapp_credit, parked: -1)
          expect(whatsapp_credit).to be_invalid
          expect(whatsapp_credit.errors.messages[:parked]).to eq(['must be greater than or equal to 0'])
        end
      end

      context 'when parked is greater than or equal to 0' do
        it 'is valid' do
          whatsapp_credit = build(:whatsapp_credit)
          expect(whatsapp_credit).to be_valid
        end
      end
    end

    describe '.usage_per_tenant' do
      context 'when usage per tenant is called with tenant id' do
        context 'when whatsapp credits are not found for the given tenant_id' do
          it 'returns empty array' do
            expect(WhatsappCredit.usage_per_tenant(1)).to match_array([])
          end
        end

        context 'when whatsapp credtis are found for the given tenant_id' do
          before do
            create(:whatsapp_credit, total: 1000, consumed: 200, tenant_id: 1)
            create(:whatsapp_credit, total: 500, consumed: 100, tenant_id: 2)
          end

          it 'returns whatsapp credits usage details' do
            expect(WhatsappCredit.usage_per_tenant(1)).to match_array([{:count=>200.0, :tenantId=>1, :usageEntity=>"WHATSAPP_CREDITS"}])
          end
        end
      end
    end

    describe '.has_whatsapp_credits_for_bulk?' do
      context 'when whatsapp credits are not found for the given tenant_id' do
        it 'returns false' do
          expect(WhatsappCredit.has_whatsapp_credits_for_bulk?(1)).to eq(false)
        end
      end

      context 'when whatsapp credits are found for the given tenant_id' do
        context 'if balance credits are more than min threshold' do
          before do
            create(:whatsapp_credit, total: 1000, consumed: 200, tenant_id: 1)
          end
          it 'returns true' do
            expect(WhatsappCredit.has_whatsapp_credits_for_bulk?(1)).to eq(true)
          end
        end

        context 'if balance credits are less than min threshold' do
          before do
            create(:whatsapp_credit, total: 1000, consumed: 200, parked: 750, tenant_id: 1)
          end
          it 'returns false' do
            expect(WhatsappCredit.has_whatsapp_credits_for_bulk?(1)).to eq(false)
          end
        end
      end
    end

    describe '.has_whatsapp_credits?' do
      context 'when whatsapp credits are not found for the given tenant_id' do
        it 'returns false' do
          expect(WhatsappCredit.has_whatsapp_credits?(1)).to eq(false)
        end
      end

      context 'when whatsapp credits are found for the given tenant_id' do
        context 'if balance credits are greater than 0' do
          before do
            create(:whatsapp_credit, total: 1000, consumed: 200, parked: 750, tenant_id: 1)
          end
          it 'returns true' do
            expect(WhatsappCredit.has_whatsapp_credits?(1)).to eq(true)
          end
        end

        context 'if balance credits are less than or equal to 0' do
          before do
            create(:whatsapp_credit, total: 1000, consumed: 200, parked: 800, tenant_id: 1)
          end
          it 'returns false' do
            expect(WhatsappCredit.has_whatsapp_credits?(1)).to eq(false)
          end
        end
      end
    end
  end
end
