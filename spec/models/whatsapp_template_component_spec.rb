# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappTemplateComponent do
  describe 'validations' do
    let(:whatsapp_template) { create(:whatsapp_template) }
    let(:whatsapp_template_component) { whatsapp_template.components.find { |comp| comp.component_type == 'BODY' } }

    it 'is valid' do
      expect(whatsapp_template_component).to be_valid
    end

    context 'header component other than TEXT format with media' do
      it 'is valid' do
        header_component = whatsapp_template.components.find { |comp| comp.component_type == 'HEADER' }
        template_media = create(:template_media, tenant_id: header_component.tenant_id, whatsapp_template_component: header_component)
        header_component.update(component_type: 'HEADER', component_format: 'IMAGE', component_value: template_media.id, media_type: 'STATIC')
        expect(header_component).to be_valid
      end

      context 'when media_type is valid' do
        it 'is valid with STATIC media_type' do
          header_component = whatsapp_template.components.find { |comp| comp.component_type == 'HEADER' }
          template_media = create(:template_media, tenant_id: header_component.tenant_id, whatsapp_template_component: header_component)
          header_component.update(component_type: 'HEADER', component_format: 'IMAGE', component_value: template_media.id, media_type: 'STATIC')
          expect(header_component).to be_valid
        end

        it 'is valid with DYNAMIC media_type' do
          header_component = whatsapp_template.components.find { |comp| comp.component_type == 'HEADER' }
          template_media = create(:template_media, tenant_id: header_component.tenant_id, whatsapp_template_component: header_component)
          header_component.update(component_type: 'HEADER', component_format: 'IMAGE', component_value: template_media.id, media_type: 'DYNAMIC')
          expect(header_component).to be_valid
        end
      end

      context 'when media_type is invalid' do
        it 'is invalid with invalid media_type' do
          header_component = whatsapp_template.components.find { |comp| comp.component_type == 'HEADER' }
          template_media = create(:template_media, tenant_id: header_component.tenant_id, whatsapp_template_component: header_component)
          header_component.update(component_type: 'HEADER', component_format: 'IMAGE', component_value: template_media.id, media_type: 'INVALID')
          expect(header_component).to be_invalid
          expect(header_component.errors.full_messages.to_sentence).to eq('Media type is not included in the list')
        end
      end
    end

    context 'header component other than TEXT format without media' do
      it 'is invalid' do
        header_component = whatsapp_template.components.find { |comp| comp.component_type == 'HEADER' }
        header_component.component_format = 'IMAGE'
        header_component.media_type = 'STATIC'
        expect(header_component).to be_invalid
        expect(header_component.errors.full_messages.to_sentence).to eq('Template media must be present when header type is media')
      end
    end

    context 'when tenant id is blank' do
      before { whatsapp_template_component.tenant_id = '' }

      it 'validates presence of tenant id' do
        expect(whatsapp_template_component).to be_invalid
      end
    end

    context 'when component text is blank' do
      before { whatsapp_template_component.component_text = '' }

      it 'validates presence of component text' do
        expect(whatsapp_template_component).to be_invalid
      end
    end

    context 'when component type is invalid' do
      before { whatsapp_template_component.component_type = 'invalid' }

      it 'validates entity type inclusion' do
        expect(whatsapp_template_component).to be_invalid
        expect(whatsapp_template_component.errors.full_messages.to_sentence).to eq('Component type is not included in the list')
      end
    end

    context 'when component format is invalid' do
      before { whatsapp_template_component.component_format = 'PHONE_NUMBER' }

      it 'validates category inclusion' do
        expect(whatsapp_template_component).to be_invalid
        expect(whatsapp_template_component.errors.full_messages.to_sentence).to eq('Component format is not included in the list')
      end
    end

    context 'when component type is header' do
      context 'when text size is more than 60 characters' do
        let(:header_component) { build(:header_component, whatsapp_template: whatsapp_template, component_text: 61.times.map { 'a' }.join) }

        it 'validates size' do
          expect(header_component).to be_invalid
          expect(header_component.errors.full_messages.to_sentence).to eq('Header text cannot be more than 60 characters')
        end
      end

      context 'when header variable is present' do
        context 'when more than one variable' do
          let(:header_component) { build(:header_component, whatsapp_template: whatsapp_template, component_text: 'Some {{1}} Header {{1}}') }

          it 'validates variable count' do
            expect(header_component).to be_invalid
            expect(header_component.errors.full_messages.to_sentence).to eq('Header text should contain at most 1 variable Only {{1}} is allowed')
          end
        end

        context 'when variable is not 1' do
          let(:header_component) { build(:header_component, whatsapp_template: whatsapp_template, component_text: 'Some {{2}} Header') }

          it 'validates variable number' do
            expect(header_component).to be_invalid
            expect(header_component.errors.full_messages.to_sentence).to eq('Header text should contain at most 1 variable Only {{1}} is allowed')
          end
        end
      end
    end

    context 'when component type is body' do
      context 'when body length considering variable as 1 character exceeds 1024' do
        let(:body_component) { build(:body_component, whatsapp_template: whatsapp_template, component_text: "#{1023.times.map { 'a' }.join}{{1}}{{1}}") }

        it 'validates size' do
          expect(body_component).to be_invalid
          expect(body_component.errors.full_messages.to_sentence).to eq('Body text cannot be more than 1024 characters')
        end
      end
    end

    context 'when component type is footer' do
      context 'when text size is more than 60 characters' do
        let(:footer_component) { build(:footer_component, whatsapp_template: whatsapp_template, component_text: 61.times.map { 'a' }.join) }

        it 'validates size' do
          expect(footer_component).to be_invalid
          expect(footer_component.errors.full_messages.to_sentence).to eq('Footer text cannot be more than 60 characters')
        end
      end

      context 'when footer contains variable' do
        let(:footer_component) { build(:footer_component, whatsapp_template: whatsapp_template, component_text: 'Some Footer {{1}} Text') }

        it 'validates text format' do
          expect(footer_component).to be_invalid
          expect(footer_component.errors.full_messages.to_sentence).to eq('Footer text cannot have variables')
        end
      end
    end

    context 'when component type is buttons' do
      context 'when phone number button' do
        context 'when text size is more than 25 characters' do
          let(:phone_number_component) { build(:phone_number_component, whatsapp_template: whatsapp_template, component_text: 26.times.map { 'a' }.join) }

          it 'validates size' do
            expect(phone_number_component).to be_invalid
            expect(phone_number_component.errors.full_messages.to_sentence).to eq('Phone number label must be restricted to 25 characters')
          end
        end

        context 'when value size is more than 20 characters' do
          let(:phone_number_component) { build(:phone_number_component, whatsapp_template: whatsapp_template, component_value: 21.times.map { 'a' }.join) }

          it 'validates size' do
            expect(phone_number_component).to be_invalid
            expect(phone_number_component.errors.full_messages.to_sentence).to eq('Phone number label must be restricted to 25 characters')
          end
        end

        context 'when value does not start with +' do
          let(:phone_number_component) { build(:phone_number_component, whatsapp_template: whatsapp_template, component_value: '919898989898') }

          it 'validates phone' do
            expect(phone_number_component).to be_invalid
            expect(phone_number_component.errors.full_messages.to_sentence).to eq('Phone number should be valid and include dial code')
          end
        end

        context 'when value is not a valid phone number' do
          let(:phone_number_component) { build(:phone_number_component, whatsapp_template: whatsapp_template, component_value: '+15550234567') }

          it 'validates phone' do
            expect(phone_number_component).to be_invalid
            expect(phone_number_component.errors.full_messages.to_sentence).to eq('Phone number should be valid and include dial code')
          end
        end
      end

      context 'when url button' do
        context 'when text size is more than 25 characters' do
          let(:url_component) { build(:url_component, whatsapp_template: whatsapp_template, component_text: 26.times.map { 'a' }.join) }

          it 'validates size' do
            expect(url_component).to be_invalid
            expect(url_component.errors.full_messages.to_sentence).to eq('URL label must be restricted to 25 characters and url to 2000 characters')
          end
        end

        context 'when value size is more than 2000 characters' do
          let(:url_component) { build(:url_component, whatsapp_template: whatsapp_template, component_value: 2001.times.map { 'a' }.join) }

          it 'validates size' do
            expect(url_component).to be_invalid
            expect(url_component.errors.full_messages.to_sentence).to eq('URL label must be restricted to 25 characters and url to 2000 characters')
          end
        end

        context 'when value is not a valid url' do
          let(:url_component) { build(:url_component, whatsapp_template: whatsapp_template, component_value: 'www.kylas.io') }

          it 'validates url' do
            expect(url_component).to be_invalid
            expect(url_component.errors.full_messages.to_sentence).to eq('Please enter valid URL')
          end
        end

        context 'when value has more than one variable' do
          let(:url_component) { build(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io/{{1}}?referral={{2}}') }

          it 'validates variable count' do
            expect(url_component).to be_invalid
            expect(url_component.errors.full_messages.to_sentence).to eq('Variable {{1}} or {{2}} in URL will only be appended at the end of url')
          end
        end

        context 'when value does not end with variable' do
          let(:url_component) { build(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{3}}') }

          it 'validates variable placement' do
            expect(url_component).to be_invalid
            expect(url_component.errors.full_messages.to_sentence).to eq('Variable {{1}} or {{2}} in URL will only be appended at the end of url')
          end
        end
      end

      context 'when copy code button' do
        context 'when text size is more than 15 characters' do
          let(:copy_code_component) { build(:copy_code_component, whatsapp_template: whatsapp_template, component_text: 16.times.map { 'a' }.join) }

          it 'validates size' do
            expect(copy_code_component).to be_invalid
            expect(copy_code_component.errors.full_messages.to_sentence).to eq('Copy code text must be restricted to 15 characters')
          end
        end

        context 'when invalid sample text' do
          let(:copy_code_component) { build(:copy_code_component, whatsapp_template: whatsapp_template, component_text: '12OfF%') }

          it 'validates format' do
            expect(copy_code_component).to be_invalid
            expect(copy_code_component.errors.full_messages.to_sentence).to eq('Copy code text should only contain alphanumeric characters')
          end
        end
      end

      context 'when quick reply button' do
        context 'when text size is more than 25 characters' do
          let(:quick_reply_component) { build(:quick_reply_component, whatsapp_template: whatsapp_template, component_text: 26.times.map { 'a' }.join) }

          it 'validates size' do
            expect(quick_reply_component).to be_invalid
            expect(quick_reply_component.errors.full_messages.to_sentence).to eq('Quick reply text must be restricted to 25 characters')
          end
        end

        context 'when text contains variable' do
          let(:quick_reply_component) { build(:quick_reply_component, whatsapp_template: whatsapp_template, component_text: 'Quick Reply to {{1}}') }

          it 'validates text format' do
            expect(quick_reply_component).to be_invalid
            expect(quick_reply_component.errors.full_messages.to_sentence).to eq('Quick reply text cannot have variables')
          end
        end
      end
    end
  end

  describe 'associations' do
    it 'belongs to whatsapp template' do
      whatsapp_template_component_whatsapp_template = described_class.reflect_on_association(:whatsapp_template)
      expect(whatsapp_template_component_whatsapp_template.macro).to eq(:belongs_to)
    end

    it 'has_one template_media' do
      template_media_association = described_class.reflect_on_association(:template_media)
      expect(template_media_association.macro).to eq(:has_one)
    end
  end
end
