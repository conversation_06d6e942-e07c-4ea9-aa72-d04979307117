# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "Health controller", type: :request do  
  describe "#status" do
    context "when message is present" do
      before do
        ENV['TENANT_ID'] = '1'
        create(:message, tenant_id: 1)
      end

      it "should give response 200" do
        get '/v06700edc6a3b7f12/messages/health'
        expect(response.status).to eq(200)
      end
    end

    context "when message is not present" do
      it "should give response 404" do
        get '/v06700edc6a3b7f12/messages/health'
        expect(response.status).to eq(404)
      end
    end

    context "when connection not established" do
      it "should give response 503" do
        allow(Message).to receive(:find_by).and_raise(ActiveRecord::ConnectionNotEstablished)
        get '/v06700edc6a3b7f12/messages/health'
        expect(response.status).to eq(503)
      end
    end

    context "when unable to connect to database using existing connection" do
      it "should give response 503" do
        allow(Message).to receive(:find_by).and_raise(PG::ConnectionBad)
        get '/v06700edc6a3b7f12/messages/health'
        expect(response.status).to eq(503)
      end
    end 
  end
end
