require 'swagger_helper'

RSpec.describe 'Health API', type: :request do

  path '/v06700edc6a3b7f12/messages/health' do
    get 'Message from database' do
      tags 'Message'

      let(:user) { create(:user) }

      before do
        expect(ENV).to receive(:[]).with('TENANT_ID').and_return(user.tenant_id)
      end

      response '200', 'Database is up' do
        before { create(:message, tenant_id: user.tenant_id) }

        run_test!
      end

      response '404', 'Entity not present' do
        run_test!
      end

      response '503', 'Database is down' do
        before { expect(Message).to receive(:find_by).and_raise(PG::ConnectionBad) }

        run_test!
      end
    end
  end
end
