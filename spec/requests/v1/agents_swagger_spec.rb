require 'swagger_helper'

RSpec.describe 'AgentsController', type: :request do
  let(:user)              { create(:user, tenant_id: 99) }
  let(:valid_auth_token)  { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)    { valid_auth_token.token }

  path '/v1/messages/connected-accounts/{connected_account_id}/agents/{entity_type}' do
    get 'Lists Connected Account Agents' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :connected_account_id, in: :path, type: :integer, required: true
      parameter name: :entity_type, in: :path, type: :string, enum: [LOOKUP_LEAD, LOOKUP_CONTACT], required: true

      let(:connected_account_id) { create(:connected_account, tenant_id: user.tenant_id).id }
      let(:entity_type) { 'lead' }

      response '200', 'Lists connected account agents' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected account not found' do
        let(:connected_account_id) { -1 }

        run_test!
      end

      response '422', 'Invalid entity type' do
        let(:entity_type) { 'deal' }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{connected_account_id}/agents/{entity_type}/save' do
    post 'Save Connected Account Agents' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :connected_account_id, in: :path, type: :integer, required: true
      parameter name: :entity_type, in: :path, type: :string, enum: [LOOKUP_LEAD, LOOKUP_CONTACT], required: true
      parameter name: :agents_data, in: :body, schema: {
        type: :object,
        required: %w[agents],
        properties: {
          agents: {
            type: :object,
            schema: {
              id: {
                type: :integer
              },
              name: {
                type: :string
              }
            }
          }
        }
      }

      let(:connected_account_id) { create(:connected_account, tenant_id: user.tenant_id).id }
      let(:entity_type) { 'lead' }
      let(:another_user) { create(:user, tenant_id: user.tenant_id) }
      let(:agents_data) { { agents: [{ id: user.id, name: user.name }, { id: another_user.id, name: another_user.name }] } }

      response '200', 'Save connected account agents' do
        let(:user_summary_response) do
          user_response = JSON.parse(file_fixture('user-summary-response.json').read)
          user_response.first['id'] = user.id
          user_response.last['id'] = another_user.id
          user_response
        end

        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},#{another_user.id}")
            .with(
              headers: {
                Authorization: "Bearer #{valid_auth_token.token}"
              }
            )
            .to_return(status: 200, body: user_summary_response.to_json)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected account not found' do
        let(:connected_account_id) { -1 }

        run_test!
      end

      response '422', 'Invalid entity type or user' do
        before do
          stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user.id},#{another_user.id}")
            .with(
              headers: {
                Authorization: "Bearer #{valid_auth_token.token}"
              }
            )
            .to_return(status: 404, body: '')
        end

        run_test!
      end
    end
  end
end
