require 'rails_helper'

describe 'Message attachment management', type: :request do
  let(:user)                       { create(:user)}
  let(:valid_auth_token)           { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:auth_data)                  { User::TokenParser.parse(valid_auth_token.token) }
  let(:headers)                    { valid_headers(valid_auth_token) }

  describe '#show' do
    before do
      @resource = instance_double(Aws::S3::Resource)
      @bucket = instance_double(Aws::S3::Bucket)
      @obj = instance_double(Aws::S3::Object)
      @file_name = 'tenant_14/user_12/attachments/121_old_file_123123123.mp3'

      allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
      allow(@resource).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(@bucket)
      allow(@bucket).to receive(:object).with(@file_name).and_return(@obj)
      allow(@obj).to receive(:presigned_url).and_return('https://www.aws.com/files/7.mp3')

      @message = create(:message, owner: user, tenant_id: user.tenant_id)
      @attachment = create(:attachment, id: 77, message: @message, file_name: @file_name)
    end

    context 'when attachment exists' do
      it 'should get presigned URL of attachment' do
        get "/v1/messages/#{@message.id}/attachments/#{@attachment.id}", headers: headers, as: :json
        expect(response.status).to eq(200)
        expect(JSON.parse(response.body)).to eq({ "file_name" => 'old_file.mp3', "url" => 'https://www.aws.com/files/7.mp3' })
      end
    end

    context 'when attachment does not exists' do
      it 'should raise an exception for attachment not found' do
        get "/v1/messages/#{@message.id}/attachments/1111", headers: headers, as: :json
        expect(response.status).to eq(404)
        expect(response.parsed_body['errorCode']).to match(ErrorCode.not_found)
      end
    end
  end
end
