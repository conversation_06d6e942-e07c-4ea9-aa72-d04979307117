require 'swagger_helper'

RSpec.describe 'Attachment API', type: :request do
  let(:user){ User.create(id: 1, tenant_id: 99, name: '<PERSON>')}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)    { valid_auth_token.token }

  path '/v1/messages/{message_id}/attachments/{id}' do
    get 'Downloads attachment' do
      tags 'Message App'
      produces 'application/json'
      parameter name: :message_id, in: :path, type: :string
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Attachment found' do
        let!(:message)          { FactoryBot.create(:message, owner: user) }
        let!(:message_id)                { message.id }
        let!(:id) { FactoryBot.create(:attachment, message: message, file_name: @file_name).id }

        before do
          @resource = instance_double(Aws::S3::Resource)
          @bucket = instance_double(Aws::S3::Bucket)
          @obj = instance_double(Aws::S3::Object)
          @file_name = 'tenant_14/user_12/message_attachments/121_old_file_123123123.mp3'
          allow(Aws::S3::Resource).to receive(:new).and_return(@resource)
          allow(@resource).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(@bucket)
          allow(@bucket).to receive(:object).with(@file_name).and_return(@obj)
          allow(@obj).to receive(:presigned_url).and_return('https://www.aws.com/files/78682793829.mp3')
        end

        run_test!
      end

      response '404', 'Attachment not found' do
        let(:message_id)                { 'invalid' }
        let(:id) { 'invalid' }

        run_test!
      end
    end
  end
end
