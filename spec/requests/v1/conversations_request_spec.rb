require 'rails_helper'

RSpec.describe V1::ConversationsController, type: :request do
  describe '#fetch_details' do
    let(:user){ create(:user) }
    let(:connected_account) { create(:connected_account, tenant_id: auth_data.tenant_id, status: 'active') }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
    let(:look_up){ create(:look_up, entity_type: 'lead', tenant_id: user.tenant_id)}
    let(:message_conversation){ create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:conversation_look_up){ create(:conversation_look_up, conversation_id: message_conversation.id, tenant_id: user.tenant_id, look_up_id: look_up.id)}
    let(:second_user) { create(:user) }
    let(:second_user_auth_token){ build(:auth_token, :without_sms_permission, user_id: second_user.id, tenant_id: second_user.tenant_id, username: second_user.name) }
    let(:second_user_auth_data){ User::TokenParser.parse(second_user_auth_token.token) }
    let(:second_user_conversation){ create(:conversation, phone_number: '+************', tenant_id: second_user.tenant_id, connected_account_id:  connected_account.id, owner_id: user.id) }
    let(:headers){ valid_headers(valid_auth_token) }
    let(:valid_rule){ { 'type': 'related_to_lookup', 'field': 'related_to', 'id': 'related_to', 'operator': 'equal', 'value': { 'id': 34343, 'entity': 'lead' } } }
    let(:conversation_params) do
      {
        "id": message_conversation.id,
        "jsonRule": {
          "condition": 'AND',
          "rules": [valid_rule]
        }
      }.with_indifferent_access
    end

    let(:second_conversation_params) do
      {
        "id": second_user_conversation.id,
        "jsonRule": {
          "condition": 'AND',
          "rules": [valid_rule]
        }
      }.with_indifferent_access
    end

    context 'when json rule for related entities is sent' do
      context 'when user has required conversation permissions' do
        before(:each) do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token.token
          Thread.current[:user] = user

          allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          @messages = create_list(:message, 2, owner: user, tenant_id: user.tenant_id, conversation_id: message_conversation.id)
          @messages.first.update!(direction: 'outgoing', id: 1231231)
          @messages.last.update!(direction: 'incoming', id: 444444)
          conversation_look_up

          stub_request(:get, "http://localhost:8086/v1/entities/lead/masked-fields").with(
            headers: {
            'Authorization'=> "Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: [
              {
                type: 'PHONE',
                name: 'phoneNumbers',
                description: 'Lead Phone',
                filterable: true,
                sortable: true,
                required: false,
                important: true,
                pickLists: nil,
                fieldConfigurations: [
                    {
                      id: nil,
                      type:'MASKING',
                      configuration: {
                        enabled:true,
                        profileIds: [1,2,3]
                      }
                    }
                  ]
              }
            ].to_json,
            headers: {}
          )

          post "/v1/messages/conversations/#{message_conversation.id}/search", params: conversation_params, headers: headers, as: :json
        end

        it 'returns all the messages in the conversation' do
          expect(response.status).to eq(200)
          expect(response.parsed_body['content'].count).to eq(2)
          expect(response.parsed_body).to eq(
            {
              "content" => [
                {
                  "id" => 444444,
                  "content" => "Sample text",
                  "medium" => "whatsapp",
                  "direction" => "incoming",
                  "sentAt" => @messages.last.sent_at.iso8601(3),
                  "recipientNumber" => "12312312",
                  "senderNumber" => "+91****313",
                  "messageType" => "whatsapp",
                  "statusMessage" => nil,
                  "status" => "Sent",
                  "owner" => {
                    "id" => user.id, "name" => user.name
                  },
                  "relatedTo" => [{"entity"=>"lead", "id"=>look_up.entity_id, "name"=>look_up.name}],
                  "recipients" => [],
                  "attachments" => []
                },
                {
                  "id" => 1231231,
                  "content" => "Sample text",
                  "medium" => "whatsapp",
                  "direction" => "outgoing",
                  "sentAt" => @messages.first.sent_at.iso8601(3),
                  "recipientNumber" => "+91****312",
                  "senderNumber" => "12312313",
                  "messageType" => "whatsapp",
                  "statusMessage" => nil,
                  "status" => "Sent",
                  "owner" => {
                    "id" => user.id, "name" => user.name
                  },
                  "relatedTo" => [{"entity"=>"lead", "id"=>look_up.entity_id, "name"=>look_up.name}],
                  "recipients" => [],
                  "attachments" => []
                }
              ],
              "page" => {
                "no" => 1, "size" => 10
              },
              "entityPermissions" => {
                "isEntityAccessible" => true,
                "isPhoneNumberPresent" => true
              },
              "totalElements" => 2,
              "totalPages" => 1,
              "first" => true,
              "last" => true
            }
          )
        end

        it 'returns all fields' do
          expect(response.parsed_body['content'].first.keys).to match_array(%w[id content medium recipientNumber senderNumber direction sentAt owner relatedTo recipients attachments status messageType statusMessage])
        end

        context "when masking is enabled on entity phone number" do
          it 'returns masked data' do
            expect(response.parsed_body['content'].find{|m| m['id'] === 1231231}['recipientNumber']).to eq('+91****312')
            expect(response.parsed_body['content'].find{|m| m['id'] === 444444}['senderNumber']).to eq('+91****313')
          end
        end
      end

      context 'when user does not have required conversation permission' do
        before(:each) do
          Thread.current[:auth] = second_user_auth_data
          Thread.current[:token] = second_user_auth_token.token
          Thread.current[:user] = second_user

          allow_any_instance_of(GenerateToken).to receive(:call).and_return(second_user_auth_token.token)

          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers id firstName lastName ownerId] ,
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{second_user_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          headers = {
            'Authorization' => second_user_auth_token.token,
            'Content-Type' => 'application/json'
          }

          post "/v1/messages/conversations/#{second_user_conversation.id}/search", params: second_conversation_params, headers: headers, as: :json
        end

        it 'throws error' do
          expect(response.status).to eq(403)
          expect(response.parsed_body['errorCode']).to eq(ErrorCode.message_not_allowed)
        end
      end
    end
  end

  describe '#index' do
    let(:conversation_params) do
      {
        "page": 1,
        "size": 10,
        "jsonRule": nil
      }.with_indifferent_access
    end

    let(:user) { create(:user)}
    let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
    let(:valid_auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    
    let(:connected_account_id_1){create(:connected_account, tenant_id: user.tenant_id)}
    let(:look_up){create(:look_up, tenant_id: user.tenant_id, entity_type: LOOKUP_LEAD, owner_id: user.id)}
    let(:look_up_2){create(:look_up, tenant_id: user.tenant_id, entity_type: LOOKUP_LEAD, phone_number: '+************', owner_id: user.id)}
    let(:contact_lookup){create(:look_up, tenant_id: user.tenant_id, entity_type: LOOKUP_CONTACT, owner_id: user.id)}

    let(:another_user) { create(:user, tenant_id: user.tenant_id)}
    let(:another_user_auth_token){ build(:auth_token, :without_sms_permission, user_id: another_user.id, tenant_id: another_user.tenant_id, username: another_user.name) }
    let(:another_user_auth_data){ User::TokenParser.parse(another_user_auth_token.token) }

    let!(:conversation_user_owner_1) {create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_1.id, last_activity_at: 10.hours.ago, last_message_received_at: 10.hours.ago)}
    let!(:other_tenant_conversation) {create(:conversation, phone_number: '+************')}
    let!(:conversation_another_user_owner) {create(:conversation, owner_id: another_user.id, tenant_id: user.tenant_id, phone_number: '+************', connected_account_id: connected_account_id_1.id)}
    let!(:conversation_look_up){create(:conversation_look_up, conversation_id: conversation_user_owner_1.id, look_up_id: look_up.id, tenant_id: user.tenant_id)}
    let!(:conversation_look_up_2){create(:conversation_look_up, conversation_id: conversation_user_owner_1.id, look_up_id: look_up_2.id, tenant_id: user.tenant_id)}
    let!(:conversation_look_up_contact){create(:conversation_look_up, conversation_id: conversation_user_owner_1.id, look_up_id: contact_lookup.id, tenant_id: user.tenant_id)}
    let!(:contact_deal_association_1) { create(:contact_deal_association, contact_id: contact_lookup.entity_id, deal_id: 101, deal_name: 'Software License Deal', tenant_id: user.tenant_id) }
    let!(:contact_deal_association_2) { create(:contact_deal_association, contact_id: contact_lookup.entity_id, deal_id: 102, deal_name: 'Consulting Deal', tenant_id: user.tenant_id) }

    context 'when user has required conversation permissions' do
      before(:each) do
        Thread.current[:auth] = valid_auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        headers = {
          'Authorization' => valid_auth_token.token,
          'Content-Type' => 'application/json'
        }

        stub_request(:get, "http://localhost:8086/v1/entities/lead/masked-fields").with(
            headers: {
            'Authorization'=> "Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: [
              {
                type: 'PHONE',
                name: 'phoneNumbers',
                description: 'Lead Phone',
                filterable: true,
                sortable: true,
                required: false,
                important: true,
                pickLists: nil,
                fieldConfigurations: [
                    {
                      id: nil,
                      type:'MASKING',
                      configuration: {
                        enabled:true,
                        profileIds: [1,2,3]
                      }
                    }
                  ]
              }
            ].to_json,
            headers: {}
          )

        post "/v1/messages/conversations/search", params: conversation_params, headers: headers, as: :json
      end

        it 'returns all the conversations' do
          expect(response.status).to eq(200)
          expect(response.parsed_body).to eq({
            "content"=>[
              {
                "id"=>conversation_user_owner_1.id,
                "entityId"=>contact_lookup.entity_id,
                "entityName"=>contact_lookup.name,
                "entityType"=>contact_lookup.entity_type,
                "lastMessageAt"=>nil,
                "lastMessageStatus"=>nil,
                "lastMessage"=>nil,
                "lastMessageStatusInfo"=>nil,
                "connectedAccountId" => conversation_user_owner_1.connected_account_id,
                "phoneNumber"=>{
                  "value"=>"+91****888",
                  "session"=>"active"
                },
                "relatedTo"=>[
                  {
                    "name"=>contact_lookup.name,
                    "id"=>contact_lookup.entity_id,
                    "entity"=>contact_lookup.entity_type
                  },
                  {
                    "name"=>look_up.name,
                    "id"=>look_up.entity_id,
                    "entity"=>look_up.entity_type
                  },
                  {
                    "name"=>look_up_2.name,
                    "id"=>look_up_2.entity_id,
                    "entity"=>look_up_2.entity_type
                  }
                ],
                "relatedDeals" => [
                  {
                    "id" => 102,
                    "name" => "Consulting Deal"
                  },
                  {
                    "id" => 101,
                    "name" => "Software License Deal"
                  }
                ]
              },
              {
                "id"=>conversation_another_user_owner.id,
                "entityId"=>nil,
                "entityName"=>nil,
                "entityType"=>nil,
                "lastMessageAt"=>nil,
                "lastMessageStatus"=>nil,
                "lastMessage"=>nil,
                "relatedTo"=>[],
                "lastMessageStatusInfo"=>nil,
                "connectedAccountId" => conversation_another_user_owner.connected_account_id,
                "phoneNumber"=>{
                  "value"=>"+************",
                  "session"=>"inactive"
                },
                "relatedDeals" => []
              }
            ],
            "page"=>{"no"=>1, "size"=>10},
            "totalElements"=>2,
            "totalPages"=>1,
            "first"=>true,
            "last"=>true
          })
        end
      end

    context 'when user does not have required conversation permission' do
      before(:each) do
        Thread.current[:auth] = another_user_auth_data
        Thread.current[:token] = another_user_auth_token.token
        Thread.current[:user] = another_user

        headers = {
          'Authorization' => another_user_auth_token.token,
          'Content-Type' => 'application/json'
        }

        post "/v1/messages/conversations/search", params: conversation_params, headers: headers, as: :json
      end

      it 'throws error' do
        expect(response.status).to eq(401)
        expect(response.parsed_body['errorCode']).to eq('022002')
      end
    end
  end

  describe '#permissions' do
    let(:user) { create(:user) }
    let(:valid_auth_token) { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active') }
    let(:conversation) { create(:conversation, phone_number: '+************', tenant_id: user.tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
    let(:headers) { valid_headers(valid_auth_token) }
    
    context 'when entity is accessible and phone number is present' do
      before(:each) do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 123
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        get "/v1/messages/conversations/#{conversation.id}/permissions?entityType=lead&entityId=123", headers: headers
      end

      it 'returns permissions with both flags as true' do
        expect(response.status).to eq(200)
        expect(response.parsed_body).to eq({
          "isEntityAccessible" => true,
          "isPhoneNumberPresent" => true
        })
      end
    end
    
    context 'when entity is accessible but phone number is not present' do
      before(:each) do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

        # Return a response without matching phone number
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 123
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: {
          "content": [
            {
              "id": 123,
              "firstName": "John",
              "lastName": "Doe",
              "ownerId": user.id,
              "phoneNumbers": [
                {
                  "dialCode": "+1",
                  "value": "5551234567"
                }
              ]
            }
          ]
        }.to_json, headers: {})

        get "/v1/messages/conversations/#{conversation.id}/permissions?entityType=lead&entityId=123", headers: headers
      end

      it 'returns permissions with isEntityAccessible true and isPhoneNumberPresent false' do
        expect(response.status).to eq(200)
        expect(response.parsed_body).to eq({
          "isEntityAccessible" => true,
          "isPhoneNumberPresent" => false
        })
      end
    end
    
    context 'when conversation is not found' do
      before(:each) do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        get "/v1/messages/conversations/999999/permissions?entityType=lead&entityId=123", headers: headers
      end

      it 'returns a not found error' do
        expect(response.status).to eq(422)
        expect(response.parsed_body['errorCode']).to eq(ErrorCode.conversation_not_found)
      end
    end
  end

  describe '#destroy' do
    let(:user) { create(:user) }
    let(:valid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }
    let!(:conversation) { create(:conversation, owner_id: user.id, tenant_id: user.tenant_id, connected_account_id: connected_account.id) }
    let(:look_up) { create(:look_up, tenant_id: user.tenant_id, entity_type: LOOKUP_LEAD) }
    let!(:message) { create(:message, conversation_id: conversation.id, tenant_id: user.tenant_id) }
    let!(:conversation_look_up) { create(:conversation_look_up, conversation_id: conversation.id, tenant_id: user.tenant_id, look_up_id: look_up.id) }
    let!(:sub_conversation) { create(:sub_conversation, conversation_id: conversation.id) }

    context 'when user has required permissions' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        headers = {
          'Authorization' => valid_auth_token.token,
          'Content-Type' => 'application/json'
        }

        allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
        allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)

        delete "/v1/messages/conversations/#{conversation.id}", headers: headers
      end

      it 'returns success response' do
        expect(response.status).to eq(200)
      end

      it 'soft deletes the conversation' do
        expect(conversation.reload.deleted_at).to be_present
      end
    end

    context 'when user does not have required permissions' do
      let(:unauthorized_user) { create(:user) }
      let(:unauthorized_auth_token) { build(:auth_token, :without_sms_permission, user_id: unauthorized_user.id, tenant_id: unauthorized_user.tenant_id) }
      let(:unauthorized_auth_data) { User::TokenParser.parse(unauthorized_auth_token.token) }

      before do
        Thread.current[:auth] = unauthorized_auth_data
        Thread.current[:token] = unauthorized_auth_token.token
        Thread.current[:user] = unauthorized_user

        headers = {
          'Authorization' => unauthorized_auth_token.token,
          'Content-Type' => 'application/json'
        }

        delete "/v1/messages/conversations/#{conversation.id}", headers: headers
      end

      it 'returns forbidden response' do
        expect(response.status).to eq(403)
        expect(response.parsed_body['errorCode']).to eq(ErrorCode.delete_not_allowed)
      end

      it 'does not soft delete the conversation' do
        expect(conversation.reload.deleted_at).to be_nil
      end
    end

    context 'when conversation does not exist' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        headers = {
          'Authorization' => valid_auth_token.token,
          'Content-Type' => 'application/json'
        }

        delete "/v1/messages/conversations/invalid_id", headers: headers
      end

      it 'returns not found response' do
        expect(response.status).to eq(422)
        expect(response.parsed_body['errorCode']).to eq(ErrorCode.conversation_not_found)
      end
    end
  end

  describe 'POST /v1/messages/conversations' do
    let(:user) { create(:user, id: 4010) }
    let(:tenant_id) { user.tenant_id }
    let(:connected_account) { create(:connected_account, tenant_id: tenant_id, status: 'active', is_verified: true) }
    let(:second_connected_account) { create(:connected_account, tenant_id: tenant_id, status: 'active', is_verified: true) }
    let(:auth_token) { build(:auth_token, user_id: user.id, tenant_id: tenant_id, username: user.name) }
    let(:headers) { valid_headers(auth_token) }
    let(:conversation) { create(:conversation, phone_number: '+************', tenant_id: tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
    let(:entity_id) { 34343 }
    let(:entity_type) { 'lead' }
    let(:entity_phone_number) { '+************' }
    let(:entity_phone_id) { 207783 }
    let(:entity_owner_id) { user.id }
    let(:look_up) { create(:look_up, entity_id: entity_id, entity_type: entity_type, tenant_id: tenant_id, phone_number: entity_phone_number, owner_id: entity_owner_id) }
    let(:last_conversation) { create(:conversation, phone_number: entity_phone_number, tenant_id: tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }

    before do
      Thread.current[:auth] = User::TokenParser.parse(auth_token.token)
      Thread.current[:token] = auth_token.token
      Thread.current[:user] = user
      allow(AssociateConversationWithEntitiesJob).to receive(:perform_later)
    end

    context 'when entity-based and all is valid' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: file_fixture('get_lead_response.json').read,
          headers: {}
        )
      end
      it 'creates a new conversation and associates look_up' do
        post "/v1/messages/conversations", params: {
          lastConversationId: last_conversation.id,
          connectedAccountId: second_connected_account.id,
          entityId: entity_id,
          entityType: entity_type
        }.to_json, headers: headers
        expect(response.status).to eq(200)
        expect(response.parsed_body['id']).to be_present
        expect(Conversation.find(response.parsed_body['id']).phone_number).to eq(entity_phone_number)
        expect(AssociateConversationWithEntitiesJob).to have_received(:perform_later).at_least(:once)
      end
    end

    context 'when phone-based and all is valid' do
      let(:phone_conversation) { create(:conversation, phone_number: '+************', tenant_id: tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
      it 'creates a new conversation and schedules jobs' do
        post "/v1/messages/conversations", params: {
          lastConversationId: phone_conversation.id,
          connectedAccountId: second_connected_account.id
        }.to_json, headers: headers
        expect(response.status).to eq(200)
        expect(response.parsed_body['id']).to be_present
        expect(Conversation.find(response.parsed_body['id']).phone_number).to eq(phone_conversation.phone_number)
        expect(AssociateConversationWithEntitiesJob).to have_received(:perform_later).at_least(:once)
      end
    end

    context 'when conversation already exists for connected account' do
      let!(:existing_conversation) { create(:conversation, phone_number: entity_phone_number, tenant_id: tenant_id, connected_account_id: connected_account.id, owner_id: user.id) }
      it 'returns the existing conversation id' do
        post "/v1/messages/conversations", params: {
          lastConversationId: existing_conversation.id,
          connectedAccountId: connected_account.id
        }.to_json, headers: headers
        expect(response.status).to eq(200)
        expect(response.parsed_body).to eq({
          "id" => existing_conversation.id,
          "session" => "inactive",
          "lastContactedAt" => existing_conversation.last_message_received_at
        })
      end
    end

    context 'when lastConversationId is missing' do
      it 'returns 422 error' do
        post "/v1/messages/conversations", params: {
          connectedAccountId: connected_account.id
        }.to_json, headers: headers
        expect(response.status).to eq(422)
      end
    end

    context 'when lastConversationId is invalid' do
      it 'returns 422 error' do
        post "/v1/messages/conversations", params: {
          lastConversationId: 999999,
          connectedAccountId: connected_account.id
        }.to_json, headers: headers
        expect(response.status).to eq(422)
      end
    end

    context 'when connected_account is inactive or unverified' do
      let(:inactive_account) { create(:connected_account, tenant_id: tenant_id, status: 'inactive', is_verified: false) }
      it 'returns 404 error' do
        post "/v1/messages/conversations", params: {
          lastConversationId: last_conversation.id,
          connectedAccountId: inactive_account.id
        }.to_json, headers: headers
        expect(response.status).to eq(404)
      end
    end

    context 'when phone number is not present on entity' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: {
            content: [
              {
                id: entity_id,
                firstName: 'lead first name',
                ownerId: entity_owner_id,
                phoneNumbers: [
                  { dialCode: '+91', value: '**********', id: 999999 }
                ]
              }
            ]
          }.to_json,
          headers: {}
        )
      end
      it 'returns 403 error' do
        post "/v1/messages/conversations", params: {
          lastConversationId: last_conversation.id,
          connectedAccountId: second_connected_account.id,
          entityId: entity_id,
          entityType: entity_type
        }.to_json, headers: headers
        expect(response.status).to eq(403)
      end
    end

    context 'when user lacks permission (entity-based)' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: file_fixture('get_lead_response.json').read,
          headers: {}
        )
        allow_any_instance_of(User).to receive(:can_send_conversation_message?).and_return(false)
      end
      it 'returns 403 error' do
        post "/v1/messages/conversations", params: {
          lastConversationId: last_conversation.id,
          connectedAccountId: second_connected_account.id,
          entityId: entity_id,
          entityType: entity_type
        }.to_json, headers: headers
        expect(response.status).to eq(403)
      end
    end

    context 'when user lacks permission (phone-based)' do
      before do
        allow_any_instance_of(User).to receive(:can_create_conversation?).and_return(false)
      end
      it 'returns 403 error' do
        post "/v1/messages/conversations", params: {
          lastConversationId: conversation.id,
          connectedAccountId: second_connected_account.id
        }.to_json, headers: headers
        expect(response.status).to eq(403)
      end
    end
  end

  describe 'POST /v1/messages/conversations/by-entity' do
    let(:user) { create(:user, id: 4010) }
    let(:entity_id) { '123' }
    let(:entity_type) { 'lead' }
    let(:entity_name) { 'John Doe' }
    let(:phone_id) { 207783 }
    let(:phone_number) { '+**********' }
    let!(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: 'active', is_verified: true) }
    let(:entity_data) do
      {
        'id' => entity_id,
        'entityType' => entity_type,
        'firstName' => 'John',
        'lastName' => 'Doe',
        'ownerId' => user.id,
        'phoneNumbers' => [
          {
            'id' => phone_id,
            'dialCode' => '+1',
            'value' => '*********'
          }
        ]
      }
    end
    let(:auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }

    let(:valid_headers) do
      {
        'Authorization' => "Bearer #{auth_token.token}",
        'Content-Type' => 'application/json'
      }
    end

    before do
      allow_any_instance_of(EntityService).to receive(:get_by_id).and_return(entity_data)
      allow_any_instance_of(User).to receive(:can_user_read_conversation?).and_return(true)
      allow_any_instance_of(User).to receive(:can_create_conversation?).and_return(true)

      stub_request(:get, "http://localhost:8086/v1/entities/lead/masked-fields").with(
        headers: {
        'Authorization'=> "Bearer #{auth_token.token}",
        }
      ).to_return(status: 200, body: [].to_json,
        headers: {}
      )

      stub_request(:post, 'http://localhost:8083/v1/search/lead').to_return(
          status: 200,
          body: file_fixture('get_lead_response.json').read,
          headers: {}
        )
    end

    context 'when entity exists' do
      context 'when conversation exists' do
        let!(:conversation) do
          create(:conversation,
                 tenant_id: user.tenant_id,
                 phone_number: phone_number,
                 connected_account_id: connected_account.id,
                 last_message_received_at: 1.hour.ago)
        end

        context 'when conversation has multiple look-ups' do
          let!(:look_up1) { create(:look_up, entity_id: entity_id, entity_type: entity_type, tenant_id: user.tenant_id) }
          let!(:look_up2) { create(:look_up, entity_id: '456', entity_type: 'lead', tenant_id: user.tenant_id) }
          
          before do
            conversation.look_ups << look_up1
            conversation.look_ups << look_up2
          end

          it 'returns existing conversation with all look-ups' do
            post '/v1/messages/conversations/by-entity',
                 params: {
                   entityId: entity_id,
                   entityType: entity_type,
                   entityName: entity_name
                 }.to_json,
                 headers: valid_headers

            expect(response).to have_http_status(:ok)
            json_response = JSON.parse(response.body)
            expect(json_response['conversationId']).to eq(conversation.id)
            expect(json_response['entityId']).to eq(entity_id)
            expect(json_response['entityName']).to eq(entity_name)
            expect(json_response['entityType']).to eq(entity_type)
            expect(json_response['connectedAccountId']).to eq(connected_account.id)
            expect(json_response['phoneNumber']['value']).to eq(phone_number)
            expect(json_response['phoneNumber']['session']).to eq('active')
            expect(json_response['relatedTo']).to be_an(Array)
            expect(json_response['relatedTo'].length).to eq(1)
          end
        end

        context 'when conversation has no look-ups' do
          it 'raise conversation not found error' do
            post '/v1/messages/conversations/by-entity',
                 params: {
                   entityId: entity_id,
                   entityType: entity_type,
                   entityName: entity_name
                 }.to_json,
                 headers: valid_headers

            expect(response).to have_http_status(:not_found)
            json_response = JSON.parse(response.body)
            expect(json_response['message']).to include('Conversation not found')
          end
        end
      end

      context 'when conversation does not exist' do
        context 'when phone_id is provided' do
          it 'creates new conversation' do
            post '/v1/messages/conversations/by-entity',
                 params: {
                   entityId: entity_id,
                   entityType: entity_type,
                   entityName: entity_name,
                   phoneId: phone_id
                 }.to_json,
                 headers: valid_headers

            expect(response).to have_http_status(:ok)
            json_response = JSON.parse(response.body)
            expect(json_response['conversationId']).to be_present
            expect(json_response['entityId']).to eq(entity_id)
            expect(json_response['entityName']).to eq(entity_name)
            expect(json_response['entityType']).to eq(entity_type)
            expect(json_response['connectedAccountId']).to eq(connected_account.id)
            expect(json_response['phoneNumber']['value']).to eq(phone_number)
            expect(json_response['phoneNumber']['session']).to eq('inactive')
          end
        end

        context 'when phone_id is not provided' do
          it 'returns conversation not found error' do
            post '/v1/messages/conversations/by-entity',
                 params: {
                   entityId: entity_id,
                   entityType: entity_type,
                   entityName: entity_name
                 }.to_json,
                 headers: valid_headers

            expect(response).to have_http_status(:not_found)
            json_response = JSON.parse(response.body)
            expect(json_response['message']).to include('Conversation not found')
          end
        end
      end
    end

    context 'when entity does not exist' do
      before do
        allow_any_instance_of(EntityService).to receive(:get_by_id).and_return(nil)
      end

      it 'returns not found error' do
        post '/v1/messages/conversations/by-entity',
             params: {
               entityId: entity_id,
               entityType: entity_type,
               entityName: entity_name
             }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to include('Entity not found')
      end
    end

    context 'when user does not have permission' do
      before do
        allow_any_instance_of(User).to receive(:can_user_read_conversation?).and_return(false)
      end

      it 'returns message not allowed error' do
        post '/v1/messages/conversations/by-entity',
             params: {
               entityId: entity_id,
               entityType: entity_type,
               entityName: entity_name
             }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to include('Conversation not found')
      end
    end

    context 'when phone number is not found' do
      let(:entity_data) do
        {
          'id' => entity_id,
          'entityType' => entity_type,
          'firstName' => 'John',
          'lastName' => 'Doe',
          'ownerId' => user.id,
          'phoneNumbers' => []
        }
      end

      it 'returns invalid phone number error' do
        post '/v1/messages/conversations/by-entity',
             params: {
               entityId: entity_id,
               entityType: entity_type,
               entityName: entity_name
             }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to include('Conversation not found')
      end
    end

    context 'when no active connected account exists' do
      before do
        connected_account.update!(status: 'inactive')
      end

      it 'returns not found error' do
        post '/v1/messages/conversations/by-entity',
             params: {
               entityId: entity_id,
               entityType: entity_type,
               entityName: entity_name,
               phoneId: phone_id
             }.to_json,
             headers: valid_headers

        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to include('Connected account not found')
      end
    end
  end
end
