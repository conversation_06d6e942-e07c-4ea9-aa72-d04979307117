require 'swagger_helper'

RSpec.describe 'FieldMappingsController', type: :request do
  let(:user)              { create(:user, tenant_id: 99) }
  let(:valid_auth_token)  { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)    { valid_auth_token.token }

  path '/v1/messages/connected-accounts/{connected_account_id}/mapped-fields/{entity_type}' do
    get 'Connected account mapped fields for entity' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :connected_account_id, in: :path, type: :integer, required: true
      parameter name: :entity_type, in: :path, type: :string, required: true, enum: [:lead, :contact]

      let(:connected_account_id) { create(:connected_account, tenant_id: user.tenant_id).id }
      let(:entity_type) { 'lead' }

      response '200', 'Connected account field mappings for entity' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected account not found' do
        let(:connected_account_id) { -1 }

        run_test!
      end

      response '422', 'Invalid entity type' do
        let(:entity_type) { 'deal' }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{connected_account_id}/mapped-fields/{entity_type}' do
    post 'Save Connected account mapped fields for entity' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })
      parameter name: :connected_account_id, in: :path, type: :integer, required: true
      parameter name: :entity_type, in: :path, type: :string, required: true, enum: [:lead, :contact]
      parameter name: :field_data, in: :body, schema: {
        type: :object,
        properties: {
          campaign: { type: :integer },
          source: { type: :integer },
          subSource: { type: :string },
          utmCampaign: { type: :string },
          utmContent: { type: :string },
          utmMedium: { type: :string },
          utmSource: { type: :string },
          utmTerm: { type: :string }
        }
      }

      let(:connected_account_id) { create(:connected_account, tenant_id: user.tenant_id).id }
      let(:entity_type) { 'lead' }
      let(:field_data) do
        {
          campaign: 40366,
          source: 40368,
          subSource: 'New Sub Source',
          utmCampaign: 'New UTM Campaign',
          utmContent: 'New UTM Content',
          utmMedium: 'New UTM Medium',
          utmSource: 'New UTM Source',
          utmTerm: 'New UTM Term'
        }
      end

      response '200', 'Connected account field mappings for entity saved successfully' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
            .with(
              headers: {
                Authorization: "Bearer #{valid_auth_token.token}"
              }
            )
            .to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected account not found' do
        let(:connected_account_id) { -1 }

        run_test!
      end

      response '422', 'Invalid data' do
        let(:entity_type) { 'deal' }

        run_test!
      end
    end
  end
end
