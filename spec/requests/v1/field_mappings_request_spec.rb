# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::FieldMappingsController, type: :request do
  let(:user)                          { create(:user) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers)                       { valid_headers(valid_auth_token) }
  let(:invalid_auth_token)            { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:no_whatsapp_permission_headers){ valid_headers(invalid_auth_token) }
  let(:invalid_header)                { invalid_headers }

  describe '#get' do
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

        context 'when connected account is present' do
          context 'when field mapping is present' do
            let!(:field_mapping) { create(:field_mapping, connected_account: connected_account, entity_type: entity_type) }

            it 'returns field mapping' do
              get "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers

              expect(response.code).to eq('200')
              expect(response.parsed_body['campaign']).to eq(field_mapping.campaign)
              expect(response.parsed_body['source']).to eq(field_mapping.source)
              expect(response.parsed_body['subSource']).to eq(field_mapping.sub_source)
              expect(response.parsed_body['utmCampaign']).to eq(field_mapping.utm_campaign)
              expect(response.parsed_body['utmContent']).to eq(field_mapping.utm_content)
              expect(response.parsed_body['utmMedium']).to eq(field_mapping.utm_medium)
              expect(response.parsed_body['utmSource']).to eq(field_mapping.utm_source)
              expect(response.parsed_body['utmTerm']).to eq(field_mapping.utm_term)
            end
          end

          context 'when field mapping is absent' do
            it 'returns blank hash' do
              get "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers

              expect(response.code).to eq('200')
              expect(response.parsed_body).to eq({})
            end
          end
        end

        context 'when connected account is not present' do
          it 'returns error' do
            get "/v1/messages/connected-accounts/-1/mapped-fields/#{entity_type}", headers: headers

            expect(response.code).to eq('404')
            expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => "Connected Account not found" })
          end
        end

        context 'when user does not have permission' do
          it 'returns error' do
            get "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: no_whatsapp_permission_headers

            expect(response.code).to eq('401')
            expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
          end
        end

        context 'invalid user' do
          it 'returns error' do
            get "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: invalid_headers

            expect(response.code).to eq('401')
            expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
          end
        end
      end
    end

    context 'when invalid entity' do
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      it 'returns error' do
        get "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/deal", headers: headers

        expect(response.code).to eq('422')
        expect(response.parsed_body).to eq({ "errorCode" => "022003", "message" => 'Invalid entity type' })
      end
    end
  end

  describe '#create' do
    let(:params) do
      {
        campaign: campaign_id,
        source: source_id,
        subSource: 'New Sub Source',
        utmCampaign: 'New UTM Campaign',
        utmContent: 'New UTM Content',
        utmMedium: 'New UTM Medium',
        utmSource: 'New UTM Source',
        utmTerm: 'New UTM Term'
      }
    end

    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity_type|
      context "when entity type is #{entity_type}" do
        let(:campaign_id) { entity_type == LOOKUP_LEAD ? 40366 : 436488 }
        let(:source_id) { entity_type == LOOKUP_LEAD ? 40368 : 439621 }
        let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

        context 'when connected account is present' do
          before do
            stub_request(:get, "http://localhost:8086/v1/entities/#{entity_type}/fields?entityType=#{entity_type}&custom-only=false")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 200, body: file_fixture("fields/#{entity_type}-fields-response.json").read)
          end

          context 'when field mapping is present' do
            let!(:field_mapping) { create(:field_mapping, connected_account: connected_account, entity_type: entity_type) }

            it 'updates and returns field mapping' do
              expect do
                post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers, params: params.to_json
              end.to change(FieldMapping, :count).by(0)

              expect(response.code).to eq('200')
              expect(field_mapping.reload.campaign).to eq(campaign_id)
              expect(field_mapping.source).to eq(source_id)
              expect(field_mapping.sub_source).to eq('New Sub Source')
              expect(field_mapping.utm_campaign).to eq('New UTM Campaign')
              expect(field_mapping.utm_content).to eq('New UTM Content')
              expect(field_mapping.utm_medium).to eq('New UTM Medium')
              expect(field_mapping.utm_source).to eq('New UTM Source')
              expect(field_mapping.utm_term).to eq('New UTM Term')
            end
          end

          context 'when field mapping is absent' do
            it 'creates and returns field mapping' do
              expect do
                post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers, params: params.to_json
              end.to change(FieldMapping, :count).by(1)

              expect(response.code).to eq('200')
              expect(response.parsed_body['campaign']).to eq(campaign_id)
              expect(response.parsed_body['source']).to eq(source_id)
              expect(response.parsed_body['subSource']).to eq('New Sub Source')
              expect(response.parsed_body['utmCampaign']).to eq('New UTM Campaign')
              expect(response.parsed_body['utmContent']).to eq('New UTM Content')
              expect(response.parsed_body['utmMedium']).to eq('New UTM Medium')
              expect(response.parsed_body['utmSource']).to eq('New UTM Source')
              expect(response.parsed_body['utmTerm']).to eq('New UTM Term')
            end
          end
        end

        context 'when campaign id or source id is invalid or inactive' do
          before do
            stub_request(:get, "http://localhost:8086/v1/entities/#{entity_type}/fields?entityType=#{entity_type}&custom-only=false")
              .with(
                headers: {
                  Authorization: "Bearer #{valid_auth_token.token}"
                }
              )
              .to_return(status: 200, body: file_fixture("fields/#{entity_type}-fields-response.json").read)
          end

          context 'when campaign id is invalid' do
            let(:campaign_id) { 123 }

            it 'raises invalid data error' do
              post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers, params: params.to_json

              expect(response.code).to eq('422')
              expect(response.parsed_body).to eq({ "errorCode" => "022019", "message" => "Invalid or inactive campaign or source field" })
            end
          end

          context 'when campaign id inactive' do
            let(:campaign_id) { entity_type == LOOKUP_LEAD ? 281857 : 436489 }

            it 'raises invalid data error' do
              post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers, params: params.to_json

              expect(response.code).to eq('422')
              expect(response.parsed_body).to eq({ "errorCode" => "022019", "message" => "Invalid or inactive campaign or source field" })
            end
          end

          context 'when source id is invalid' do
            let(:source_id) { 123 }

            it 'raises invalid data error' do
              post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers, params: params.to_json

              expect(response.code).to eq('422')
              expect(response.parsed_body).to eq({ "errorCode" => "022019", "message" => "Invalid or inactive campaign or source field" })
            end
          end

          context 'when source id is inactive' do
            let(:source_id) { entity_type == LOOKUP_LEAD ? 40367 : 441729 }

            it 'raises invalid data error' do
              post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: headers, params: params.to_json

              expect(response.code).to eq('422')
              expect(response.parsed_body).to eq({ "errorCode" => "022019", "message" => "Invalid or inactive campaign or source field" })
            end
          end
        end

        context 'when connected account is not present' do
          it 'returns error' do
            post "/v1/messages/connected-accounts/-1/mapped-fields/#{entity_type}", headers: headers, params: params.to_json

            expect(response.code).to eq('404')
            expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => "Connected Account not found" })
          end
        end

        context 'when user does not have permission' do
          it 'returns error' do
            post "/v1/messages/connected-accounts/-1/mapped-fields/#{entity_type}", headers: no_whatsapp_permission_headers, params: params.to_json

            expect(response.code).to eq('401')
            expect(response.parsed_body).to eq({ "errorCode" => "022002", "message" => "Unauthorized access." })
          end
        end

        context 'invalid user' do
          it 'returns error' do
            post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/#{entity_type}", headers: invalid_headers, params: params.to_json

            expect(response.code).to eq('401')
            expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
          end
        end
      end
    end

    context 'when invalid entity' do
      let(:campaign_id) { 40366 }
      let(:source_id) { 40368 }
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id) }

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/mapped-fields/deal", headers: headers, params: params.to_json

        expect(response.code).to eq('422')
        expect(response.parsed_body).to eq({ "errorCode" => "022003", "message" => 'Invalid entity type' })
      end
    end
  end
end
