# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::InteraktWebhooksController, type: :request do
  describe '#handler' do
    context 'when webhook received' do
      before do
        channel = BunnyMock.new.start.channel
        exchange = channel.topic 'ex.message'
        @queue = channel.queue 'q.message.interakt.webhook.message'
        @queue.bind exchange, routing_key: 'message.interakt.webhook'
        allow(RabbitmqConnection).to receive(:get_exchange).with('ex.message', 'message').and_return(@queue)
        post '/v1/messages/whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks', headers: { 'Content-Type' => 'application/json' }, params: sample_webhook_payload.to_json
      end

      context 'when waba onboarded event' do
        let(:sample_webhook_payload) { JSON.parse(file_fixture('interakt_webhooks/waba-onboarded-successfully.json').read) }

        it 'returns ok' do
          expect(response.status).to eq(200)
          expect(response.body).to eq('')
        end

        it 'publishs event' do
          expect(@queue.message_count).to eq(1)
        end

        it 'publishes request body as is' do
          payload = @queue.pop
          expect(payload.first[:routing_key]).to eq('message.interakt.webhook')
          expect(JSON.parse(payload.last)).to eq(JSON.parse(sample_webhook_payload.to_json))
        end
      end
    end
  end

  describe '#challenge' do
    context 'when integrating webhook url for whatsapp' do
      it 'returns the facebook hub challenge' do
        get '/v1/messages/whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks?hub.challenge=12345abcde'

        expect(response.status).to eq(200)
        expect(response.headers['content-type']).to eq('text/plain; charset=utf-8')
        expect(response.body).to eq('12345abcde')
      end
    end
  end
end
