require 'swagger_helper'

RSpec.describe 'InteraktWebhooks', type: :request do
  path '/v1/messages/whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks' do
    post 'Interakt Webhook' do
      tags 'Message'
      consumes 'application/json'

      parameter name: :webhook_body, in: :body, schema: {
        type: :object,
        properties: {
          payload_dict: {
            type: :object,
            properties: {
              event: { type: :string, enum: [WABA_ONBOARDED, WABA_ONBOARDING_FAILED] },
              isv_name_token: { type: :string, description: 'Whatsapp Business Token' },
              waba_id: { type: :string },
              phone_number_id: { type: :string },
              error: { type: :object, description: 'Error object' }
            }
          }
        }
      }

      let(:webhook_body) { JSON.parse(file_fixture('interakt_webhooks/waba-onboarded-successfully.json').read) }

      response '200', 'Interakt webhook received' do
        before { expect(PublishEvent).to receive(:call).with(instance_of(Event::InteraktWebhook)) }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks' do
    get 'Interakt Challenge' do
      tags 'Message'

      response '200', 'Interakt challenge successful' do
        run_test!
      end
    end
  end
end
