# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::LayoutController, type: :request do
  let(:user)             { create(:user)}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:headers)          { valid_headers(valid_auth_token) }

  describe '#list' do
    context 'layout list json' do
      before { get '/v1/messages/whatsapp/layout/list', headers: headers }

      it 'returns leftNav, pageConfig, defaultConfig' do
        layout_list_json  = response.parsed_body
        expect(layout_list_json.keys).to match_array(%w[leftNav pageConfig defaultConfig])
      end

      it 'returns actionConfig and tableConfig in pageConfig' do
        page_config_json = response.parsed_body['pageConfig']


        expect(page_config_json.keys).to match_array(%w[actionConfig tableConfig])
      end

      it 'returns 9 fields in table config' do
        fields = response.parsed_body['pageConfig']['tableConfig']['columns']

        expect(fields.map { |f| f['id'] }).to match_array(["status", "sentFromAccount", "messageBody", "direction", "sentAt", "readAt", "receivedAt", "templateType", "template"])
        expect(fields.map { |f| f['header'] }).to match_array(["Status", "Sent from Account", "Message Body", "Direction", "Sent At", "Read At", "Received At", "Template Type", "Template"])
        expect(fields.map { |f| f['fieldType'] }).to match_array(["PICK_LIST", "LOOK_UP", "TEXT_FIELD", "PICK_LIST", "DATETIME_PICKER", "DATETIME_PICKER", "DATETIME_PICKER", "PICK_LIST", "LOOK_UP"])
      end

      it 'returns 7 filterable fields' do
        filterable_fields = response.parsed_body['pageConfig']['tableConfig']['columns'].select { |f| f['isFilterable'] }

        expect(filterable_fields.map { |f| f['id'] }).to match_array(["status", "sentFromAccount", "messageBody", "direction", "sentAt", "readAt", "receivedAt", "templateType", "template"])
      end

      it 'returns 5 look up fields' do
        lookup_fields = response.parsed_body.with_indifferent_access['pageConfig']['tableConfig']['columns'].select { |f| f['fieldType'] == 'LOOK_UP' }
        expect(lookup_fields.map { |f| f['lookup'] }).to match_array(
          [
            { 
              "entity"=>"MESSAGE", 
              "lookupUrl"=>"/messages/connected-accounts/lookup?q="
            },
            {
              "entity"=>"MESSAGE", 
              "lookupUrl"=>"/messages/whatsapp-templates/lookup?q="
            }
          ]
        )
      end
    end
  end
end
