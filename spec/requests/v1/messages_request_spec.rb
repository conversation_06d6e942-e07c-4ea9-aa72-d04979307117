require 'rails_helper'

describe 'Message management', type: :request do
  let(:user)                       { create(:user)}
  let(:another_user)               { create(:user, tenant_id: user.tenant_id)}
  let(:valid_auth_token)           { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:auth_data)                  { User::TokenParser.parse(valid_auth_token.token) }
  let(:headers)                    { valid_headers(valid_auth_token) }

  describe '#create' do
    context 'lead entity' do
      let(:related_lookup) { build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1) }
      let(:create_request){
        {
          content: 'This is my message',
          messageType: 'whatsapp',
          relatedTo: [
            {
              name: related_lookup.name,
              id: related_lookup.entity_id,
              entity: related_lookup.entity_type,
              phoneNumber: related_lookup.phone_number
            }
          ],
          recipients: [
            {
              name: related_lookup.name,
              id: related_lookup.entity_id,
              entity: related_lookup.entity_type,
              phoneNumber: related_lookup.phone_number
            }
          ]
        }
      }

      context 'with valid data' do
        before do
          expect(Publishers::MessageCreated).to receive(:call)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{related_lookup.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token.token}"
              }).
            to_return(status: 200, body: {"id": related_lookup.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": related_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})

          post '/v1/messages', params: create_request.to_json, headers: headers
        end

        it 'should create message and return its id' do
          expect(response).to have_http_status(:created)
          expect(JSON.parse(response.body)).to have_key('id')
        end

        it 'saves message type' do
          expect(Message.last.message_type).to eq('whatsapp')
        end
      end

      context 'when message type is invalid' do
        it 'raises invalid data error' do
          create_request['messageType'] = 'invalid'
          post '/v1/messages', params: create_request.to_json, headers: headers
          expect(response.parsed_body['errorCode']).to eq('022003')
          expect(response.parsed_body['message']).to eq('Message type is not included in the list')
        end
      end

      context 'with invalid token' do
        before { post '/v1/messages', params: create_request.to_json, headers: invalid_headers }

        it 'should throw invalid token error' do
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
        end
      end

      context 'when owner id is provided' do
        before do
          create_request.merge!(ownerId: another_user.id)

          stub_request(:get, SERVICE_IAM + "/v1/users/#{another_user.id}").
            with(
              headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
              }).
            to_return(status: 200, body: { "profileId" => 1, 'permissions' => [ { "id": 4, "name": "lead", "displayName": nil, "description": nil, "action": { "read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }}] }.to_json, headers: {})

          stub_request(:get, SERVICE_SALES + "/v1/leads/#{related_lookup.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer XXX-XXX-XXX"
              }).
            to_return(status: 200, body: {"id": related_lookup.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": related_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})
          allow(BuildPermissionsForToken).to receive(:call).and_return(nil)
          allow(GenerateToken).to receive(:call).and_return("XXX-XXX-XXX")
        end

        it 'creates message with specified owner' do
          expect(Publishers::MessageCreated).to receive(:call)
          post '/v1/messages', params: create_request.to_json, headers: headers

          expect(Message.find(response.parsed_body['id']).owner_id).to eq(another_user.id)
        end

        context "when owner user doesn't have permissions" do
          before do
            stub_request(:get, SERVICE_IAM + "/v1/users/#{another_user.id}").
            with(
              headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
              }).
            to_return(status: 200, body: { "profileId" => 1, 'permissions' => [ { "id": 4, "name": "lead", "displayName": nil, "description": nil, "action": { "read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": true, "note": true, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }}] }.to_json, headers: {})

            stub_request(:get, SERVICE_SALES + "/v1/leads/#{related_lookup.entity_id}").
              with(
                headers: {
                  'Authorization' => "Bearer #{valid_auth_token.token}"
                }).
              to_return(status: 200, body: {"id": related_lookup.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": related_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})
          end

          it 'creates message with current user as owner' do
            expect(Publishers::MessageCreated).to receive(:call)
            post '/v1/messages', params: create_request.to_json, headers: headers

            expect(Message.find(response.parsed_body['id']).owner_id).to eq(user.id)
          end

          context "and current user also doesn't have necessary message permission" do
            let(:valid_auth_token) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

            it 'throws error' do
              post '/v1/messages', params: create_request.to_json, headers: headers

              expect(response.parsed_body['errorCode']).to eq(ErrorCode.message_not_allowed)
            end
          end
        end
      end
    end

    context 'deal entity' do
      let(:related_lookup)   { build(:look_up, entity_type: LOOKUP_DEAL, entity_id: 1) }
      let(:recipient_lookup) { build(:look_up, entity_type: LOOKUP_CONTACT, entity_id: 1) }
      let(:create_request)   {
        {
          content: 'This is my message',
          relatedTo: [
            {
              name: related_lookup.name,
              id: related_lookup.entity_id,
              entity: related_lookup.entity_type,
            }
          ],
          recipients: [
            {
              name: recipient_lookup.name,
              id: recipient_lookup.entity_id,
              entity: recipient_lookup.entity_type,
              phoneNumber: recipient_lookup.phone_number
            }
          ]
        }
      }

      context 'with invalid data' do
        context 'when recipient is not provided' do
          before do
            create_request[:recipients] = []
            post '/v1/messages', params: create_request.to_json, headers: headers
          end

          it 'throws error' do
            expect(response.parsed_body['errorCode']).to eq(ErrorCode.recipient_not_present)
          end
        end
      end

      context 'with valid data' do
        before do
          expect(Publishers::MessageCreated).to receive(:call)
          stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
            with(
              headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
              }).
            to_return(status: 200, body: { "content": [ {"id": 1, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":recipient_lookup.phone_number,"dialCode":"+91","primary":true}]}]}.to_json, headers: {})

          stub_request(:get, SERVICE_SALES + "/v1/contacts/#{recipient_lookup.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer #{ valid_auth_token.token }"
              }).
              to_return(status: 200, body: {"id": 1, "ownerId": 4010, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":recipient_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Contact" }.to_json, headers: {})

          post '/v1/messages', params: create_request.to_json, headers: headers
        end

        it 'should create message and returns its id' do
          expect(response).to have_http_status(:created)
          expect(JSON.parse(response.body)).to have_key('id')
        end

        it 'creates message recipients and related entities correctly' do
          message = Message.find(response.parsed_body['id'])
          expect(message.recipients.count).to eq(1)
          expect(message.related_to.count).to eq(2)
          expect(message.recipients.map(&:entity)).to match_array(["#{LOOKUP_CONTACT}_#{recipient_lookup.entity_id}"])
          expect(message.recipients.map(&:owner_id)).to match_array([4010])
          expect(message.related_to.map(&:entity)).to match_array(["#{LOOKUP_CONTACT}_#{recipient_lookup.entity_id}", "#{LOOKUP_DEAL}_#{related_lookup.entity_id}"])
          expect(message.related_to.map(&:owner_id)).to match_array([4010, nil])
        end
      end

      context 'with invalid token' do
        before { post '/v1/messages', params: create_request.to_json, headers: invalid_headers }
        it 'should throw invalid token error' do
          expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
        end
      end

      context 'when owner id is provided' do
        before do
          create_request.merge!(ownerId: another_user.id)

          stub_request(:get, SERVICE_IAM + "/v1/users/#{another_user.id}").
            with(
              headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
              }).
            to_return(status: 200, body: { "profileId" => 1, 'permissions' => [ { "id": 4, "name": "deal", "displayName": nil, "description": nil, "action": { "read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": true, "task": true, "note": true, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }}] }.to_json, headers: {})

          stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
            with(
              headers: {
              "Authorization" => "Bearer XXX-XXX-XXX"
              }).
            to_return(status: 200, body: { "content": [ {"id": 1, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":recipient_lookup.phone_number,"dialCode":"+91","primary":true}]}]}.to_json, headers: {})

          stub_request(:get, SERVICE_SALES + "/v1/contacts/#{recipient_lookup.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer XXX-XXX-XXX"
              }).
              to_return(status: 200, body: {"id": 1, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":recipient_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Contact", "ownerId": 4010 }.to_json, headers: {})

          allow(BuildPermissionsForToken).to receive(:call).and_return(nil)
          allow(GenerateToken).to receive(:call).and_return("XXX-XXX-XXX")
        end

        it 'creates message with specified owner' do
          expect(Publishers::MessageCreated).to receive(:call)
          post '/v1/messages', params: create_request.to_json, headers: headers

          expect(Message.find(response.parsed_body['id']).owner_id).to eq(another_user.id)
        end

        context "and owner user doesn't have permissions" do
          before do
            stub_request(:get, SERVICE_IAM + "/v1/users/#{another_user.id}").
            with(
              headers: {
              "Authorization" => "Bearer #{ valid_auth_token.token }"
              }).
            to_return(status: 200, body: { "profileId" => 1, 'permissions' => [ { "id": 4, "name": "deal", "displayName": nil, "description": nil, "action": { "read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": true, "note": true, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }}] }.to_json, headers: {})

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
              with(
                headers: {
                "Authorization" => "Bearer #{valid_auth_token.token}"
                }).
              to_return(status: 200, body: { "content": [ {"id": 1, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":recipient_lookup.phone_number,"dialCode":"+91","primary":true}]}]}.to_json, headers: {})

            stub_request(:get, SERVICE_SALES + "/v1/contacts/#{recipient_lookup.entity_id}").
              with(
                headers: {
                  'Authorization' => "Bearer #{valid_auth_token.token}"
                }).
                to_return(status: 200, body: {"id": 1, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":recipient_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Contact", "ownerId": 4010 }.to_json, headers: {})
          end

          it 'creates message with current user as owner' do
            expect(Publishers::MessageCreated).to receive(:call)
            post '/v1/messages', params: create_request.to_json, headers: headers

            expect(Message.find(response.parsed_body['id']).owner_id).to eq(user.id)
          end

          context "and current user also doesn't have necessary message permission" do
            let(:valid_auth_token) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

            it 'throws error' do
              post '/v1/messages', params: create_request.to_json, headers: headers

              expect(response.parsed_body['errorCode']).to eq(ErrorCode.message_not_allowed)
            end
          end
        end
      end
    end
  end

  context '#search' do
    let(:valid_rule){ { 'type': 'related_to_lookup', 'field': 'related_to', 'id': 'related_to', 'operator': 'equal', 'value': { 'id': @look_up.entity_id, 'entity': @look_up.entity_type }}}
    let(:request_params){ { "jsonRule": { "condition": 'AND', "rules": [valid_rule] }} }

    [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].each do |entity|
      context "when json rule for #{entity} related entity is sent" do
        let(:valid_rule) { { 'type': 'related_to_lookup', 'field': 'related_to', 'id': 'related_to', 'operator': 'equal', 'value': { 'id': @look_up.entity_id, 'entity': @look_up.entity_type }}}

        before do
          @look_up = create(:look_up, entity_id: 1, entity_type: entity)
          @messages = create_list(:message, 3, owner: user, tenant_id: user.tenant_id)
          @messages.first.update!(direction: 'outgoing', id: 1231231)
          @messages.last.update!(direction: 'incoming', id: 444444)
          @messages.first.related_to = [@look_up]
          @messages.last.related_to = [@look_up]

          if [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].include?(entity)
            entity = LOOKUP_CONTACT if entity == LOOKUP_DEAL

            stub_request(:get, "http://localhost:8086/v1/entities/#{entity}/masked-fields").with(
              headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: [
                {
                  type: 'PHONE',
                  name: 'phoneNumbers',
                  description: 'Lead Phone',
                  filterable: true,
                  sortable: true,
                  required: false,
                  important: true,
                  pickLists: nil,
                  fieldConfigurations: [
                      {
                        id: nil,
                        type:'MASKING',
                        configuration: {
                          enabled:true,
                          profileIds: [1,2,3]
                        }
                      }
                    ]
                }
              ].to_json,
              headers: {}
            )
          end

          post "/v1/messages/search", params: request_params, headers: headers, as: :json
        end

        context 'and the user has the permission on the entity' do
          let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

          it 'returns all the messages' do
            messages = response.parsed_body['content']
            expect(messages.count).to eq 2
            expect(messages.map{|m| m['content']}).to match_array(@messages.first(2).map(&:content))
          end

          it 'returns all the fields' do
            message = response.parsed_body['content'].first
            expect(message.keys).to match_array(%w[id content medium recipientNumber senderNumber direction sentAt owner relatedTo recipients attachments status messageType statusMessage])
          end

          if [LOOKUP_LEAD, LOOKUP_CONTACT].include?(entity)
            context "when masking is enabled on #{entity} phone number" do
              it 'returns masked data' do
                response.parsed_body['content'].first
                expect(response.parsed_body['content'].find{|m| m['id'] === 1231231}['recipientNumber']).to eq('+91****312')
                expect(response.parsed_body['content'].find{|m| m['id'] === 444444}['senderNumber']).to eq('+91****313')
              end
            end
          end
        end

        context "and user doesn't have permission on the entity" do
          let(:valid_auth_token) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

          before { post "/v1/messages/search", params: request_params, headers: headers, as: :json }

          it 'throws error' do
            expect(response.parsed_body['errorCode']).to eq(ErrorCode.message_not_allowed)
          end
        end
      end
    end

    context 'when pagination is applied' do
      context "when page number is 1" do
        let(:page) { 1 }

        context "and number of messages doesn't exceed the page size" do
          let(:size) { 3 }

          before do
            @look_up = create(:look_up, entity_id: 1, entity_type: LOOKUP_LEAD)
            @messages = create_list(:message, size, owner: user, tenant_id: user.tenant_id)
            @messages.each { |m| m.related_to = [@look_up] }

            stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
              headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: [].to_json,
              headers: {}
            )

            post "/v1/messages/search?page=#{page}&size=#{size}", params: request_params, headers: headers, as: :json
          end

          it 'returns correct messages' do
            messages = response.parsed_body['content']
            expect(messages.count).to eq 3
            expect(messages.map{|m| m['content']}).to match_array(@messages.map(&:content))
          end

          it 'returns corect pagination details' do
            json = response.parsed_body
            expect(json['page']['no']).to eq(page)
            expect(json['page']['size']).to eq(size)

            expect(json['totalElements']).to eq(3)
            expect(json['totalPages']).to eq(1)
            expect(json['first']).to eq(true)
            expect(json['last']).to eq(true)
          end
        end

        context "and number of messages exceeds the page size" do
          let(:size) { 2 }

          before do
            @look_up = create(:look_up, entity_id: 1, entity_type: LOOKUP_LEAD)
            @messages = create_list(:message, 3, owner: user, tenant_id: user.tenant_id)
            @messages.each { |m| m.related_to = [@look_up] }

            stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
              headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: [].to_json,
              headers: {}
            )

            post "/v1/messages/search?page=#{page}&size=#{size}", params: request_params, headers: headers, as: :json
          end

          it 'returns correct messages' do
            messages = response.parsed_body['content']
            expect(messages.count).to eq size
            expect(messages.map{|m| m['content']}).to match_array(@messages.first(size).map(&:content))
          end

          it 'returns corect pagination details' do
            json = response.parsed_body
            expect(json['page']['no']).to eq(page)
            expect(json['page']['size']).to eq(size)

            expect(json['totalElements']).to eq(3)
            expect(json['totalPages']).to eq(2)
            expect(json['first']).to eq(true)
            expect(json['last']).to eq(false)
          end
        end
      end

      context "when page number is other than 1" do
        let(:page) { 2 }

        context "and number of messages doesn't exceed the page size" do
          let(:size) { 2 }

          before do
            @look_up = create(:look_up, entity_id: 1, entity_type: LOOKUP_LEAD)
            @messages = create_list(:message, 3, owner: user, tenant_id: user.tenant_id)
            @messages.each { |m| m.related_to = [@look_up] }

            stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
              headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: [].to_json,
              headers: {}
            )

            post "/v1/messages/search?page=#{page}&size=#{size}", params: request_params, headers: headers, as: :json
          end

          it 'returns correct messages' do
            messages = response.parsed_body['content']
            expect(messages.count).to eq 1
            expect(messages.map{|m| m['content']}).to match_array([@messages.last.content])
          end

          it 'returns corect pagination details' do
            json = response.parsed_body
            expect(json['page']['no']).to eq(page)
            expect(json['page']['size']).to eq(size)

            expect(json['totalElements']).to eq(3)
            expect(json['totalPages']).to eq(2)
            expect(json['first']).to eq(false)
            expect(json['last']).to eq(true)
          end
        end

        context "and number of messages exceed the page size" do
          let(:size) { 2 }

          before do
            @look_up = create(:look_up, entity_id: 1, entity_type: LOOKUP_LEAD)
            @messages = create_list(:message, 5, owner: user, tenant_id: user.tenant_id)
            @messages.each { |m| m.related_to = [@look_up] }

            stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
              headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: [].to_json,
              headers: {}
            )

            post "/v1/messages/search?page=#{page}&size=#{size}", params: request_params, headers: headers, as: :json
          end

          it 'returns correct messages' do
            messages = response.parsed_body['content']
            expect(messages.count).to eq 2
            expect(messages.map{|m| m['content']}).to match_array([@messages[2].content, @messages[3].content])
          end

          it 'returns corect pagination details' do
            json = response.parsed_body
            expect(json['page']['no']).to eq(page)
            expect(json['page']['size']).to eq(size)

            expect(json['totalElements']).to eq(5)
            expect(json['totalPages']).to eq(3)
            expect(json['first']).to eq(false)
            expect(json['last']).to eq(false)
          end
        end
      end
    end
  end

  describe '#destroy' do
    context 'when user is owner for message' do
      before do
        @look_up = create(:look_up, entity_id: 1, entity_type: LOOKUP_LEAD)
        @message = create(:message, owner: user, tenant_id: user.tenant_id)
        @message.recipients = [@look_up]
        @message.related_to = [@look_up]
        allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
      end

      context 'and user has delete permission on entity' do
        let(:valid_auth_token) { build(:auth_token, :with_sms_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

        before(:each) do
          expect(Publishers::MessageDeleted).to receive(:call)
          delete "/v1/messages/#{@message.id}", headers: headers, as: :json
        end

        it 'soft deletes the message' do
          expect(@message.reload.deleted_at).not_to be_nil
        end

        it 'does not delete the recipients entities' do
          expect(MessageLookUp.find_by(message_id: @message.id, look_up_id: @look_up.id)).not_to be_nil
        end
      end

      context "and user doesn't have delete permission on entity" do
        before(:each) do
          expect(Publishers::MessageDeleted).not_to receive(:call)
          delete "/v1/messages/#{@message.id}", headers: headers, as: :json
        end

        it 'throws error' do
          expect(response.parsed_body['errorCode']).to eq(ErrorCode.delete_not_allowed)
        end
      end
    end

    context 'when user is not owner for the message' do
      before do
        @look_up = create(:look_up, entity_id: 1, entity_type: LOOKUP_LEAD)
        @message = create(:message, owner: another_user, tenant_id: user.tenant_id)
        @message.recipients = [@look_up]
        allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
      end

      context 'when user has delete all permission on entity' do
        let(:valid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

        it 'soft deletes the message' do
          expect(Publishers::MessageDeleted).to receive(:call)
          delete "/v1/messages/#{@message.id}", headers: headers, as: :json
          expect(@message.reload.deleted_at).not_to be_nil
        end

        it 'does not delete the recipients entities' do
          expect(Publishers::MessageDeleted).to receive(:call)
          delete "/v1/messages/#{@message.id}", headers: headers, as: :json
          expect(MessageLookUp.find_by(message_id: @message.id, look_up_id: @look_up.id)).not_to be_nil
        end
      end

      context "when user have only delete permission on entity" do
        let(:valid_auth_token) { build(:auth_token, :with_sms_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }

        it 'throws error' do
          delete "/v1/messages/#{@message.id}", headers: headers, as: :json
          expect(response.parsed_body['errorCode']).to eq('022008')
        end
      end
    end
  end

  describe '#sync' do
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity|
      context "#{entity} entity" do
        let(:related_to) { create(:look_up, entity_type: entity, entity_id: 1, phone_number: **********) }
        let(:sync_request){
          {
            content: 'This is my message',
            direction: 'incoming',
            senderNumber: '**********',
            remoteId: 'test-message-1'
          }
        }

        context 'when Message with remote id is not already present' do
          context 'with valid data' do
            before do
              expect(Publishers::MessageCreated).to receive(:call)

              if entity == LOOKUP_LEAD
                search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'lead 1 test', tenant_id: user.tenant_id, phone_number: related_to.phone_number }] }
                expect_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
                expect_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
                expect_any_instance_of(GetLead).to receive(:call).and_return({ "id": related_to.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value": related_to.phone_number,"dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
              elsif entity == LOOKUP_CONTACT
                search_response = { matched: [{ entity: LOOKUP_CONTACT, id: 1, name: 'contact 1 test', tenant_id: user.tenant_id, phone_number: related_to.phone_number }] }
                expect_any_instance_of(SearchLeads).to receive(:call).and_return({ matched: [] })
                expect_any_instance_of(SearchContacts).to receive(:call).and_return(search_response)
                expect_any_instance_of(GetContact).to receive(:call).and_return({ "id": related_to.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value": related_to.phone_number,"dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
              end
            end

            it 'should return correct related to' do
              allow_any_instance_of(PublishEvent).to receive(:call).and_return(nil)

              post "/v1/messages/sync", params: sync_request, headers: headers, as: :json
              res = JSON.parse(response.body)
              expect(response).to have_http_status(:created)
              expect(res).to have_key('id')
              expect(res['recipients'].first['entityId']).to eq(related_to.entity_id)
              expect(res['recipients'].first['entityType']).to eq(related_to.entity_type)
            end

            if entity == LOOKUP_LEAD
              it 'publishes correct event for lead entity' do
                allow_any_instance_of(PublishEvent).to receive(:call).and_return(nil)

                expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
                post "/v1/messages/sync", params: sync_request, headers: headers, as: :json
              end
            end

            if entity == LOOKUP_CONTACT
              it "publishes event for contact entity" do
                expect(Publishers::MessageReceivedFromEntityPublisher).to receive(:call).once
                post "/v1/messages/sync", params: sync_request, headers: headers, as: :json
              end
            end
          end

          context 'with invalid token' do
            before { post '/v1/messages/sync', params: sync_request.to_json, headers: invalid_headers }
            it 'should throw invalid token error' do
              expect(response.parsed_body['errorCode']).to match(ErrorCode.invalid_token)
            end
          end
        end

        context 'when Message with remote id is already present' do
          before do
            @look_up = create(:look_up, entity_id: 10, entity_type: entity)
            @message = create(:message, tenant_id: user.tenant_id, owner: user, remote_id: 'test-message-1')
            @message.related_to = [@look_up]
            @attachment = create(:attachment, message: @message)
            related_lookup = @message.related_look_ups.last
            related_lookup.recipient = true
            related_lookup.save!
          end

          context 'with valid data' do
            before do
              if entity == LOOKUP_LEAD
                search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'lead 1 test', tenant_id: user.tenant_id, phone_number: related_to.phone_number }] }
                expect_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
                expect_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
                expect_any_instance_of(GetLead).to receive(:call).and_return({ "id": related_to.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value": related_to.phone_number,"dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
              elsif entity == LOOKUP_CONTACT
                search_response = { matched: [{ entity: LOOKUP_CONTACT, id: 1, name: 'contact 1 test', tenant_id: user.tenant_id, phone_number: related_to.phone_number }] }
                expect_any_instance_of(SearchLeads).to receive(:call).and_return({ matched: [] })
                expect_any_instance_of(SearchContacts).to receive(:call).and_return(search_response)
                expect_any_instance_of(GetContact).to receive(:call).and_return({ "id": related_to.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value": related_to.phone_number,"dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
              end
            end

            it 'should return correct related to' do
              post "/v1/messages/sync", params: sync_request, headers: headers, as: :json

              res = JSON.parse(response.body)
              expect(response).to have_http_status(:created)
              expect(res).to have_key('id')
              expect(res['recipients'].first['entityId']).to eq(related_to.entity_id)
              expect(res['recipients'].first['entityType']).to eq(related_to.entity_type)
            end

            it 'updates attachment' do
              sync_request[:attachments] = [{ _destroy: true, id: @attachment.id }]
              allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)

              post "/v1/messages/sync", params: sync_request, headers: headers, as: :json

              res = JSON.parse(response.body)
              expect(@message.attachments.count).to eq(0)
            end
          end

          context 'and same phone numbers are used in sender and recipient phone number' do
            before do
              if entity == LOOKUP_LEAD
                search_response = { matched: [{ entity: LOOKUP_LEAD, id: @look_up.entity_id, name: @look_up.name, tenant_id: user.tenant_id, phone_number: @look_up.phone_number }] }
                expect_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
                expect_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
                expect_any_instance_of(GetLead).to receive(:call).and_return({ "id": @look_up.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value": @look_up.phone_number,"dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
              elsif entity == LOOKUP_CONTACT
                search_response = { matched: [{ entity: LOOKUP_CONTACT, id: @look_up.entity_id, name: @look_up.name, tenant_id: user.tenant_id, phone_number: @look_up.phone_number }] }
                expect_any_instance_of(SearchLeads).to receive(:call).and_return({ matched: [] })
                expect_any_instance_of(SearchContacts).to receive(:call).and_return(search_response)
                expect_any_instance_of(GetContact).to receive(:call).and_return({ "id": @look_up.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value": @look_up.phone_number,"dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
              end
            end

            it 'should return correct related to' do
              post "/v1/messages/sync", params: sync_request, headers: headers, as: :json

              res = JSON.parse(response.body)
              expect(response).to have_http_status(:created)
              expect(res).to have_key('id')
              expect(res['recipients'].first['entityId']).to eq(@look_up.entity_id)
              expect(res['recipients'].first['entityType']).to eq(@look_up.entity_type)
            end
          end
        end
      end
    end
  end

  describe '#show' do
    before do
      @look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
      @message = create(:message, tenant_id: user.tenant_id, owner: user, message_type: 'whatsapp', direction: 'incoming')
      @message.recipients = [@look_up]
      @message.related_to = [@look_up]
      create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')
    end

    context 'when the user has sms permission on the entity' do
      before do
        get "/v1/messages/#{@message.id}", headers: headers, as: :json
      end

      it 'returns correct message in the response' do
        message = response.parsed_body
        expect(message['id']).to eq(@message.id)
        expect(message['content']).to eq(@message.content)
        expect(message['medium']).to eq(@message.medium)
        expect(message['sentAt']).to eq(@message.sent_at.utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ'))
        expect(message['readAt']).to eq(@message.read_at.utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ'))
        expect(message['deliveredAt']).to eq(@message.delivered_at.utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ'))
        expect(message['direction']).to eq(@message.direction)
        expect(message['status']).to eq(@message.status.capitalize)
        expect(message['senderNumber']).to eq(@message.sender_number)
        expect(message['recipientNumber']).to eq(@message.recipient_number)
        expect(message['relatedTo'].count).to eq(@message.related_to.count)
        expect(message['relatedTo'].first['id']).to eq(@message.related_to.first.entity_id)
        expect(message['recipients'].count).to eq(@message.recipients.count)
        expect(message['recipients'].first['id']).to eq(@message.recipients.first.entity_id)
        expect(message['attachments'].first['id']).to eq(@message.attachments.first.id)
        expect(message['attachments'].first['fileName']).to eq('sample3.txt')
        expect(message['attachments'].first['size']).to eq(@message.attachments.first.size)
        expect(message['messageType']).to eq('whatsapp')
      end
    end

    context "when the user doesn't have sms permission on entity" do
      let(:valid_auth_token) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
      before do
        get "/v1/messages/#{@message.id}", headers: headers, as: :json
      end

      it 'returns error response' do
        expect(response.parsed_body['errorCode']).to eq(ErrorCode.message_not_allowed)
      end
    end

    context 'when relatedTo param is passed as lead' do
      context 'and masking is enabled on phone' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
            headers: {
            'Authorization'=> "Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: [
              {
                type: 'PHONE',
                name: 'phoneNumbers',
                description: 'Lead Phone',
                filterable: true,
                sortable: true,
                required: false,
                important: true,
                pickLists: nil,
                fieldConfigurations: [
                    {
                      id: nil,
                      type:'MASKING',
                      configuration: {
                        enabled:true,
                        profileIds: [1,2,3]
                      }
                    }
                  ]
              }
            ].to_json,
            headers: {}
          )

          get "/v1/messages/#{@message.id}?relatedTo=lead", headers: headers, as: :json
        end

        it 'returns masked data' do
          expect(response.parsed_body['senderNumber']).to eq('+91****313')
        end
      end
    end
  end

  describe '#update' do
    [LOOKUP_LEAD, LOOKUP_CONTACT].each do |entity|
      context "#{entity} entity" do
        context 'when request is patch request' do
          before do
            @look_up = create(:look_up, entity_id: 10, entity_type: entity)
            @another_look_up = create(:look_up, entity_id: 11, entity_type: entity)

            @message = create(:message, tenant_id: user.tenant_id, owner: user)
            @message.related_to = [@look_up]
            rl = @message.related_look_ups.last
            rl.recipient = true
            rl.save!
          end

          context 'and few primitive fields are updated' do
            before do
              @data = {
                medium: 'sms',
                direction: 'incoming'
              }

              patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
            end

            it 'updates fields correctly' do
              message = Message.find(response.parsed_body['id'])
              expect(message.medium).to eq('sms')
              expect(message.direction).to eq('incoming')
            end
          end

          context 'when related to entity is updated' do
            before do
              @data = {
                relatedTo: [
                  {
                    id: @look_up.entity_id,
                    entity: @look_up.entity_type,
                    name: @look_up.name,
                    phoneNumber: @look_up.phone_number,
                    "_destroy": true
                  },
                  {
                    id: @another_look_up.entity_id,
                    entity: @another_look_up.entity_type,
                    name: @another_look_up.name,
                    phoneNumber: @another_look_up.phone_number,
                  }
                ],
                recipients: [
                  {
                    id: @another_look_up.entity_id,
                    entity: @another_look_up.entity_type,
                    name: @another_look_up.name,
                    phoneNumber: @another_look_up.phone_number,
                  }
                ]
              }

              stub_request(:get, SERVICE_SALES + "/v1/leads/#{@another_look_up.entity_id}").
                with(
                  headers: {
                    'Authorization' => "Bearer #{valid_auth_token.token}"
                  }).
                  to_return(status: 200, body: {"id": @another_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": @another_look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})

              stub_request(:get, SERVICE_SALES + "/v1/contacts/#{@another_look_up.entity_id}").
                with(
                  headers: {
                    'Authorization' => "Bearer #{ valid_auth_token.token }"
                  }).
                  to_return(status: 200, body: {"id": @another_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":@another_look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})
                  patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
            end

            # TODO: This test case fails randomely 1-2 times in 10 attempts. Need to look into it
            it 'updates fields correctly' do
              message = Message.find(response.parsed_body['id'])
              expect(message.recipients.count).to eq(1)
              expect(message.recipients.first.entity_id).to eq(@another_look_up.entity_id)
              expect(message.recipients.first.entity_type).to eq(@another_look_up.entity_type)
              expect(message.recipients.first.owner_id).to eq(4010)
            end
          end

          context 'when recipient entity is updated' do
            before do
              @data = {
                recipients: [
                  {
                    id: @look_up.entity_id,
                    entity: @look_up.entity_type,
                    name: @look_up.name,
                    phoneNumber: @look_up.phone_number,
                    "_destroy": true
                  },
                  {
                    id: @another_look_up.entity_id,
                    entity: @another_look_up.entity_type,
                    name: @another_look_up.name,
                    phoneNumber: @another_look_up.phone_number,
                  }
                ]
              }

              stub_request(:get, SERVICE_SALES + "/v1/leads/#{@another_look_up.entity_id}").
                with(
                  headers: {
                    'Authorization' => "Bearer #{valid_auth_token.token}"
                  }).
                  to_return(status: 200, body: {"id": @another_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": @another_look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})
              stub_request(:get, SERVICE_SALES + "/v1/contacts/#{@another_look_up.entity_id}").
                with(
                  headers: {
                    'Authorization' => "Bearer #{ valid_auth_token.token }"
                  }).
                  to_return(status: 200, body: {"id": @another_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":@another_look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})
              patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
            end

            it 'updates fields correctly' do
              message = Message.find(response.parsed_body['id'])
              expect(message.recipients.count).to eq(1)
              expect(message.recipients.first.entity_id).to eq(@another_look_up.entity_id)
              expect(message.recipients.first.entity_type).to eq(@another_look_up.entity_type)
              expect(message.recipients.first.owner_id).to eq(4010)

              expect(message.related_to.count).to eq(1)
              expect(message.related_to.first.entity_id).to eq(@another_look_up.entity_id)
              expect(message.related_to.first.entity_type).to eq(@another_look_up.entity_type)
              expect(message.related_to.first.owner_id).to eq(4010)
            end
          end

          context 'when attachment is updated' do
            before do
              @attachment = create(:attachment, message: @message)
              path = "spec/fixtures/audio_1.mp3"
              @file_name = 'audio_1.mp3'
              @data = {
                attachments: [
                  {
                    id: @attachment.id,
                    "_destroy": true
                  },
                  {
                    fileName: @file_name,
                    data: Rack::Test::UploadedFile.new('spec/fixtures/files/audio_1.mp3', 'audio/mp3')
                  }
                ]
              }

              allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
              patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
            end

            it 'updates fields correctly' do
              message = Message.find(response.parsed_body['id'])
              expect(message.attachments.count).to eq(1)
              expect(message.attachments.first.extract_file_name).to eq(@file_name)
            end
          end
        end
      end
    end

    context "deal entity" do
      context 'when request is patch request' do
        before do
          @deal_look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_DEAL)
          @another_deal_look_up = create(:look_up, entity_id: 11, entity_type: LOOKUP_DEAL)
          @contact_look_up = create(:look_up, entity_id: 10, entity_type: LOOKUP_CONTACT)
          @another_contact_look_up = create(:look_up, entity_id: 11, entity_type: LOOKUP_CONTACT)

          @message = create(:message, tenant_id: user.tenant_id, owner: user)
          @message.related_to = [@deal_look_up, @contact_look_up]
          rl = @message.related_look_ups.last
          rl.recipient = true
          rl.save!
        end

        context 'and few primitive fields are updated' do
          before do
            @data = {
              medium: 'sms',
              direction: 'incoming'
            }

            patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
          end

          it 'updates fields correctly' do
            message = Message.find(response.parsed_body['id'])
            expect(message.medium).to eq('sms')
            expect(message.direction).to eq('incoming')
          end
        end

        context 'when related to entity is updated' do
          before do
            @data = {
              relatedTo: [
                {
                  id: @deal_look_up.entity_id,
                  entity: @deal_look_up.entity_type,
                  name: @deal_look_up.name,
                  phoneNumber: @deal_look_up.phone_number,
                  "_destroy": true
                },
                {
                  id: @another_deal_look_up.entity_id,
                  entity: @another_deal_look_up.entity_type,
                  name: @another_deal_look_up.name,
                  phoneNumber: @another_deal_look_up.phone_number,
                }
             ]
            }

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
              with(
                headers: {
                "Authorization" => "Bearer #{ valid_auth_token.token }"
                }).
              to_return(status: 200, body: { "content": [ {"id": @contact_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":@contact_look_up.phone_number,"dialCode":"+91","primary":true}]}]}.to_json, headers: {})

            stub_request(:get, SERVICE_SALES + "/v1/contacts/#{@contact_look_up.entity_id}").
              with(
                headers: {
                  'Authorization' => "Bearer #{ valid_auth_token.token }"
                }).
                to_return(status: 200, body: {"id": @contact_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":@contact_look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Contact", "ownerId": 4010 }.to_json, headers: {})

            patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
          end

          it 'updates fields correctly' do
            message = Message.find(response.parsed_body['id'])
            expect(message.related_to.count).to eq(2)
            expect(message.related_to.map { |en| "#{en.entity_type}_#{en.entity_id}" }).to match_array(["#{@another_deal_look_up.entity_type}_#{@another_deal_look_up.entity_id}", "#{@contact_look_up.entity_type}_#{@contact_look_up.entity_id}"])
            expect(message.recipients.count).to eq(1)
            expect(message.recipients.map { |en| "#{en.entity_type}_#{en.entity_id}" }).to match_array(["#{@contact_look_up.entity_type}_#{@contact_look_up.entity_id}"])
          end
        end

        context 'when tried to remove recipient without adding new' do
          before do
            @data = {
              recipients: [
                {
                  id: @contact_look_up.entity_id,
                  entity: @contact_look_up.entity_type,
                  name: @contact_look_up.name,
                  phoneNumber: @contact_look_up.phone_number,
                  "_destroy": true
                }
              ]
            }

            patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
          end

          it 'throws error' do
            expect(response.parsed_body['errorCode']).to eq(ErrorCode.remove_recipient_not_allowed)
          end
        end

        context 'when recipient entity is updated' do
          before do
            @data = {
              recipients: [
                {
                  id: @contact_look_up.entity_id,
                  entity: @contact_look_up.entity_type,
                  name: @contact_look_up.name,
                  phoneNumber: @contact_look_up.phone_number,
                  "_destroy": true
                },
                {
                  id: @another_contact_look_up.entity_id,
                  entity: @another_contact_look_up.entity_type,
                  name: @another_contact_look_up.name,
                  phoneNumber: @another_contact_look_up.phone_number,
                }
              ]
            }

            stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?page=0&size=30").
              with(
                headers: {
                "Authorization" => "Bearer #{ valid_auth_token.token }"
                }).
              to_return(status: 200, body: { "content": [ {"id": @another_contact_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":@another_contact_look_up.phone_number,"dialCode":"+91","primary":true}]}]}.to_json, headers: {})

            stub_request(:get, SERVICE_SALES + "/v1/contacts/#{@another_contact_look_up.entity_id}").
              with(
                headers: {
                  'Authorization' => "Bearer #{ valid_auth_token.token }"
                }).
                to_return(status: 200, body: {"id": @another_contact_look_up.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":@another_contact_look_up.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Contact", "ownerId": 4010 }.to_json, headers: {})

            patch "/v1/messages/#{@message.id}", params: @data, headers: headers, as: :json
          end

          it 'updates fields correctly' do
            message = Message.find(response.parsed_body['id'])
            expect(message.related_to.count).to eq(2)
            expect(message.related_to.map { |en| "#{en.entity_type}_#{en.entity_id}" }).to match_array(["#{@deal_look_up.entity_type}_#{@deal_look_up.entity_id}", "#{@another_contact_look_up.entity_type}_#{@another_contact_look_up.entity_id}"])

            expect(message.recipients.count).to eq(1)
            expect(message.recipients.map { |en| "#{en.entity_type}_#{en.entity_id}" }).to match_array(["#{@another_contact_look_up.entity_type}_#{@another_contact_look_up.entity_id}"])
          end
        end
      end
    end
  end

  describe '#session_message' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }
    let(:params) do
      {
        entityType: 'lead',
        entityId: 123,
        phoneId: 111,
        messageType: 'text',
        messageBody: 'This is sample session message text.'
      }
    end

    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+91**********', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 123, entity_type: 'LEAD', actions: { 'sms': true }) }

    context 'when valid request' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+91**********',
              type: 'text',
              text: {
                body: 'This is sample session message text.'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

        conversation.update(last_message_received_at: 10.hours.ago)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'sends and creates message' do
        expect { post "/v1/messages/connected-accounts/#{connected_account.id}/session-message", headers: headers, params: params.to_json }
          .to change(Message, :count).by(1)
          .and change(MessageLookUp, :count).by(1)
          .and change(LookUp, :count).by(1)
      end

      it 'returns status created and message id' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/session-message", headers: headers, params: params.to_json

        expect(response.status).to eq(201)
        expect(response.parsed_body).to eq({ 'id' => Message.last.id })
      end
    end

    context 'when connected account not found' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/-1/session-message", headers: headers, params: params.to_json

        expect(response.status).to eq(404)
        expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => "Connected Account not found." })
      end
    end

    context 'when invalid params' do
      before { params.delete(:messageBody) }

      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/session-message", headers: headers, params: params.to_json

        expect(response.status).to eq(422)
        expect(response.parsed_body).to eq({ "errorCode" => "022014", "message" => "Please ensure that message is present and less than 4096 characters." })
      end
    end

    context 'when invalid context' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/session-message", headers: invalid_headers, params: params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#media_session_message' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }
    let(:audio_message_params) do
      {
        entityType: 'lead',
        entityId: 123,
        phoneId: 111,
        messageType: 'media',
        media: [
          {
            file: Rack::Test::UploadedFile.new('spec/fixtures/files/audio_1.mp3', 'audio/mp3'),
            type: 'audio'
          }
        ]
      }
    end

    let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
    let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
    let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+91**********', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 123, entity_type: 'LEAD', actions: { 'sms': true }) }

    context 'when valid request' do
      context 'when request is for sending audio' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            headers: {
              Authorization: "Bearer #{token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
            headers: {
              'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
            }
          ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+91**********',
              type: 'audio',
              audio: {
                id: '<MEDIA_ID>'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

          s3_instance = instance_double(S3::UploadFile)
          allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
          expect(s3_instance).to receive(:call)

          expect(File).to receive(:delete)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
          user_share_rule
          conversation.update(last_message_received_at: 10.hours.ago)
        end

        it 'sends and creates message' do
          expect { post "/v1/messages/connected-accounts/#{connected_account.id}/media-session-message", headers: headers, params: audio_message_params }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)
        end

        it 'returns status created and message id' do
          post "/v1/messages/connected-accounts/#{connected_account.id}/media-session-message", headers: headers, params: audio_message_params

          expect(response.status).to eq(201)
          expect(response.parsed_body).to eq({ 'id' => [Message.last.id] })
        end
      end
    end

    context 'when connected account not found' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/-1/media-session-message", headers: headers, params: audio_message_params

        expect(response.status).to eq(404)
        expect(response.parsed_body).to eq({ "errorCode" => "022006", "message" => "Connected Account not found." })
      end
    end

    context 'when invalid context' do
      it 'returns error' do
        post "/v1/messages/connected-accounts/#{connected_account.id}/media-session-message", headers: invalid_headers, params: audio_message_params.to_json

        expect(response.status).to eq(401)
        expect(response.parsed_body).to eq({ "errorCode" => "022001", "message" => nil })
      end
    end
  end

  describe '#mark_as_read' do
    # TODO
  end

  describe '#show_conversation_message' do
    before do
      @conversation = create(:conversation, tenant_id: user.tenant_id)
      @look_up = create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
      @message = create(:message, tenant_id: user.tenant_id, owner: user, message_type: 'whatsapp', direction: 'incoming', conversation_id: @conversation.id)
      @conversation.look_ups << @look_up
      create(:attachment, message: @message, file_name: 'tenant_1/user_1/76_sample3_1626268631.txt')

      allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

      stub_request(:post, 'http://localhost:8083/v1/search/lead')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":#{@look_up.entity_id}}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)
    end

    context 'when the user can read conversation to which the message belongs' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
          headers: {
          'Authorization'=> "Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: [
          ].to_json,
          headers: {}
        )
        get "/v1/conversations/#{@conversation.id}/messages/#{@message.id}?entityId=#{@look_up.entity_id}&entityType=#{LOOKUP_LEAD}", headers: headers, as: :json
      end

      it 'returns correct message in the response' do
        message = response.parsed_body
        expect(message['id']).to eq(@message.id)
        expect(message['content']).to eq(@message.content)
        expect(message['medium']).to eq(@message.medium)
        expect(message['sentAt']).to eq(@message.sent_at.utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ'))
        expect(message['readAt']).to eq(@message.read_at.utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ'))
        expect(message['deliveredAt']).to eq(@message.delivered_at.utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ'))
        expect(message['direction']).to eq(@message.direction)
        expect(message['status']).to eq(@message.status.capitalize)
        expect(message['senderNumber']).to eq(@message.sender_number)
        expect(message['recipientNumber']).to eq(@message.recipient_number)
        expect(message['relatedTo'].count).to eq(1)
        expect(message['relatedTo'].first['id']).to eq(@look_up.entity_id)
        expect(message['recipients'].count).to eq(0)
        expect(message['attachments'].first['id']).to eq(@message.attachments.first.id)
        expect(message['attachments'].first['fileName']).to eq('sample3.txt')
        expect(message['attachments'].first['size']).to eq(@message.attachments.first.size)
        expect(message['messageType']).to eq('whatsapp')
      end
    end

    context "when the user can't read the conversation" do
      let(:valid_auth_token) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
      before do
        get "/v1/conversations/#{@conversation.id}/messages/#{@message.id}?entityId=#{@look_up.entity_id}&entityType=#{LOOKUP_LEAD}", headers: headers, as: :json
      end

      it 'returns error response' do
        expect(response.parsed_body['errorCode']).to eq(ErrorCode.message_not_allowed)
      end
    end

    context 'when entityType param is passed as lead' do
      context 'and masking is enabled on phone' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
            headers: {
            'Authorization'=> "Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: [
              {
                type: 'PHONE',
                name: 'phoneNumbers',
                description: 'Lead Phone',
                filterable: true,
                sortable: true,
                required: false,
                important: true,
                pickLists: nil,
                fieldConfigurations: [
                    {
                      id: nil,
                      type:'MASKING',
                      configuration: {
                        enabled:true,
                        profileIds: [1,2,3]
                      }
                    }
                  ]
              }
            ].to_json,
            headers: {}
          )
          get "/v1/conversations/#{@conversation.id}/messages/#{@message.id}?entityId=#{@look_up.entity_id}&entityType=#{LOOKUP_LEAD}", headers: headers, as: :json
        end

        it 'returns masked data' do
          expect(response.parsed_body['senderNumber']).to eq('+91****313')
        end
      end
    end
  end
end
