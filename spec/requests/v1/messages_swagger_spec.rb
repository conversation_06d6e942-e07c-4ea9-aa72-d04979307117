require 'swagger_helper'

RSpec.describe 'App API', type: :request do
  let(:user) { create(:user, tenant_id: 99) }
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)    { valid_auth_token.token }
  let!(:related_lookup) { build(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1) }
  let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
  let!(:data)         { {
    content: 'My message',
    direction: 'outgoing',
    medium: 'whatsapp',
    senderNumber: Faker::PhoneNumber.cell_phone_in_e164,
    recipientNumber: Faker::PhoneNumber.cell_phone_in_e164,
    relatedTo: [
      {
        name: related_lookup.name,
        id: related_lookup.entity_id,
        entity: related_lookup.entity_type,
        phoneNumber: related_lookup.phone_number
      }
    ],
    recipients: [
      {
        name: related_lookup.name,
        id: related_lookup.entity_id,
        entity: related_lookup.entity_type,
        phoneNumber: related_lookup.phone_number
      }
    ]
  }}

  path '/v1/messages' do
    post 'Creates message' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :body
      parameter name: :data, in: :body, schema: {
        type: :object,
        required: %w[direction],
        properties: {
          content: {
            type: :text
          },
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '201', 'Message created' do
        before do
          expect(Publishers::MessageCreated).to receive(:call)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{related_lookup.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token.token}"
              }).
              to_return(status: 200, body: {"id": related_lookup.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": related_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/messages/sync' do
    post 'Creates message' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]
      parameter name: :body
      parameter name: :data, in: :body, schema: {
        type: :object,
        required: %w[direction],
        senderNumber: :string,
        recipientNumber: :string,
        direction: :string,
        properties: {
          content: {
            type: :text
          },
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '201', 'Message created' do
        before(:each) do
          expect(Publishers::MessageCreated).to receive(:call)
          stub_request(:get, SERVICE_SALES + "/v1/leads/#{related_lookup.entity_id}").
            with(
              headers: {
                'Authorization' => "Bearer #{valid_auth_token.token}"
              }).
              to_return(status: 200, body: {"id": related_lookup.entity_id, "phoneNumbers":[{"type":"MOBILE","code":"IN","value": related_lookup.phone_number,"dialCode":"+91","primary":true}], "firstName": "Test", "lastName": "Lead", "ownerId": 4010 }.to_json, headers: {})

          search_response = { matched: [{ entity: LOOKUP_LEAD, id: 1, name: 'lead 1 test', tenant_id: 11, phone_number: related_lookup.phone_number }] }
          expect_any_instance_of(SearchLeads).to receive(:call).and_return(search_response)
          expect_any_instance_of(SearchContacts).to receive(:call).and_return({ matched: [] })
          expect_any_instance_of(GetLead).to receive(:call).and_return({ "id": related_lookup.entity_id, "phoneNumbers":[{ "type":"MOBILE","code":"IN","value":"1231231233","dialCode":"+91","primary":true }], "firstName": "Test", "lastName": "Lead" })
        end
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end
  path '/v1/messages/search' do
    post 'Searches messages' do
      tags 'Message'
      consumes 'application/json'
      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      parameter name: :jsonRule, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: { type: :array, items: { type: 'string' } }
          }
        }
      }

      response '200', 'Message searched' do
        let(:page) { '1' }
        let(:size) { '2' }
        let(:jsonRule) { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "related_to_lookup", id: "related_to",
                                                                   field: "related_to", value: { id: 1, entity: LOOKUP_LEAD } }]}}}

        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
            headers: {
            'Authorization'=> "Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: [].to_json,
            headers: {}
          )
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:page)          { '1' }
        let(:size)          { '2' }
        let(:jsonRule)      { { jsonRule: { condition: "AND", rules: [{ operator: "equal", type: "related_to_lookup", id: "related_to",
                                                                        field: "related_to", value: { id: 1, entity: LOOKUP_LEAD } }]}}}

        run_test!
      end
    end
  end

  path '/v1/messages/{id}' do
    let(:id) { FactoryBot.create(:message, owner: user, tenant_id: user.tenant_id).id }

    delete 'Delete a message' do
      tags 'Message'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Message deleted' do
        let!(:valid_auth_token) { build(:auth_token, :with_sms_delete_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
        let!(:Authorization)    { valid_auth_token.token }

        before do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          expect(Publishers::MessageDeleted).to receive(:call)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:message)       { build(:message) }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:id)            { FactoryBot.create(:message, owner: user).id }
        run_test!
      end
    end
  end

  path '/v1/messages/{id}' do
    get 'Retrieves a message' do
      tags 'Message'
      produces 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Message found' do
        schema type: :object,
          properties: {
          id: { type: :integer },
          medium: { type: :string },
          content: { type: :string },
          sentAt: { type: :datetime },
          deliveredAt: { type: :datetime },
          readAt: { type: :datetime },
          direction: { type: :string },
          status: { type: :string },
          recipientNumber: { type: :string },
          senderNumber: { type: :string },
          owner: { type: :object },
          relatedTo: { type: 'array', items: { type: :object }},
          attachments: { type: 'array', items: { type: :object }}
        }

        let(:message) { create(:message, owner: user, tenant_id: user.tenant_id) }
        let(:id) { message.id }

        before do
          message.related_to << FactoryBot.create(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1)
          message.attachments << FactoryBot.create(:attachment, message: message)
        end

        run_test!
      end

      response '404', 'Message not found' do
        let(:id) { 'invalid' }

        run_test!
      end
    end
  end

  path '/v1/messages/{id}' do
    let(:id)      { FactoryBot.create(:message, owner: user).id }
    let(:look_up) { GetLookUp.call(related_entity.merge(tenant_id: user.tenant_id)) }

    patch 'Updates a message' do
      tags 'Message'
      consumes 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :message, in: :body, schema: {
        type: :object,
        properties: {
          id: { type: :integer },
          medium: { type: :string },
          content: { type: :string },
          sentAt: { type: :datetime },
          deliveredAt: { type: :datetime },
          readAt: { type: :datetime },
          direction: { type: :string },
          status: { type: :string },
          recipientNumber: { type: :string },
          senderNumber: { type: :string },
          owner: { type: :object },
          relatedTo: { type: 'array', items: { type: :object }},
          attachments: { type: 'array', items: { type: :object }}
        },
        required: ['id']
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Message updated' do
        let(:message) { create(:message, owner: user, tenant_id: user.tenant_id) }
        let(:id)      { message.id }
        let(:related_entity)   { create(:look_up, entity_type: LOOKUP_LEAD, entity_id: 10) }

        before { message.related_to << related_entity }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:message) { create(:message, owner: user, tenant_id: user.tenant_id) }
        let(:id)      { message.id }
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}/session-message' do
    post 'Send Session Message' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, required: true
      parameter name: :session_message_body, in: :body, schema: {
        type: :object,
        required: %w[entityType entityId phoneId messageType messageBody],
        properties: {
          entityType: { type: :string, enum: [LOOKUP_LEAD, LOOKUP_CONTACT] },
          entityId: { type: :integer },
          phoneId: { type: :integer },
          messageType: { type: :string, enum: ['text'] },
          messageBody: { type: :string }
        }
      }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:session_message_body) do
        {
          entityType: 'lead',
          entityId: 123,
          phoneId: 111,
          messageType: 'text',
          messageBody: 'This is sample session message text.'
        }
      end
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }
      let(:id) { connected_account.id }
      let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
      let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
      let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id, last_message_received_at: 10.hours.ago) }
      let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
      let!(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 123, entity_type: 'LEAD', actions: { 'sms': true }) }

      response '201', 'Session Message Sent' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:post, 'http://localhost:8083/v1/search/lead')
            .with(
              headers: {
                Authorization: "Bearer #{token_without_pid}",
                content_type: 'application/json'
              },
              body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
            ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
            .with(
              headers: {
                'x-access-token': 'partner-token',
                'x-waba-id': connected_account.waba_id,
                content_type: 'application/json'
              },
              body: {
                messaging_product: 'whatsapp',
                recipient_type: 'individual',
                to: '+************',
                type: 'text',
                text: {
                  body: 'This is sample session message text.'
                }
              }.to_json
            ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected account not found' do
        let(:id) { -1 }

        run_test!
      end

      response '422', 'Invalid params' do
        before { session_message_body[:entityType] = 'deal' }

        run_test!
      end
    end
  end

  path '/v1/messages/connected-accounts/{id}/media-session-message' do
    post 'Send Media Session Message' do
      tags 'Message'
      security [ bearerAuth: [] ]
      consumes 'multipart/form-data'

      parameter name: :id, in: :path, required: true
      parameter name: :entityType, in: :formData, type: :string, required: :true
      parameter name: :entityId, in: :formData, type: :integer, required: :true
      parameter name: :phoneId, in: :formData, type: :integer, required: :true
      parameter name: :messageType, in: :formData, type: :string, required: :true
      parameter name: :media, in: :formData, type: :array, items: {
        type: :object, properties: {
          file: { type: :string, format: :binary },
          type: { type: :string, format: :string }
        }
       }

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }
      let(:id) { connected_account.id }
      let(:entityType) { 'lead' }
      let(:entityId) { 123 }
      let(:phoneId) { 111 }
      let(:messageType) { 'media' }
      let(:media) {
        [
          {
            file: Rack::Test::UploadedFile.new('spec/fixtures/files/audio_1.mp3', 'audio/mp3'),
            type: 'audio'
          }
        ]
      }
      let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
      let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
      let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id, last_message_received_at: 10.hours.ago) }
      let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
      let!(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 123, entity_type: 'LEAD', actions: { 'sms': true }) }

      response '201', 'Session Message Sent' do
        before do
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            headers: {
              Authorization: "Bearer #{token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
            headers: {
              'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
            }
          ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'audio',
              audio: {
                id: '<MEDIA_ID>'
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

          s3_instance = instance_double(S3::UploadFile)
          allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
          expect(s3_instance).to receive(:call)

          expect(File).to receive(:delete)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Connected account not found' do
        let(:id) { -1 }

        run_test!
      end

      response '422', 'Invalid params' do
        let(:entityType) { 'deal' }

        run_test!
      end

      response '422', 'Insufficient whatsapp credits balance' do
        before(:each) { whatsapp_credit.update(total: 0) }

        run_test!
      end
    end
  end

  path '/v1/messages/{id}/mark-as-read' do
    # TODO
  end

  path '/v1/conversations/{conversation_id}/messages/{id}' do
    get 'Retrieves a conversation message' do
      tags 'Message'
      produces 'application/json'
      parameter name: :id, in: :path, type: :string
      parameter name: :conversation_id, in: :path, type: :string
      parameter name: :entityId, in: :query, type: :string
      parameter name: :entityType, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'Client token'
      })

      response '200', 'Message found' do
        schema type: :object,
          properties: {
          id: { type: :integer },
          medium: { type: :string },
          content: { type: :string },
          sentAt: { type: :datetime },
          deliveredAt: { type: :datetime },
          readAt: { type: :datetime },
          direction: { type: :string },
          status: { type: :string },
          recipientNumber: { type: :string },
          senderNumber: { type: :string },
          owner: { type: :object },
          relatedTo: { type: 'array', items: { type: :object }},
          attachments: { type: 'array', items: { type: :object }}
        }

        let(:conversation){ create(:conversation, tenant_id: user.tenant_id) }
        let(:message) { create(:message, owner: user, tenant_id: user.tenant_id, conversation_id: conversation.id) }
        let(:id) { message.id }
        let(:conversation_id){ conversation.id }
        let(:entityId) { '1' }
        let(:entityType) { 'lead' }

        before do
          conversation.look_ups << FactoryBot.create(:look_up, entity_type: LOOKUP_LEAD, entity_id: 1, tenant_id: user.tenant_id)
          message.attachments << FactoryBot.create(:attachment, message: message)

          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token
          Thread.current[:user] = user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
  
          allow(GenerateToken).to receive(:call).and_return(token_without_pid)
          stub_request(:get, 'http://localhost:8086/v1/entities/lead/masked-fields').with(
              headers: {
              'Authorization'=> "Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: [].to_json,
              headers: {}
            )

          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            headers: {
              Authorization: "Bearer #{token_without_pid}",
              content_type: 'application/json'
            },
            body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":1}],\"condition\":\"AND\",\"valid\":true}}"
          ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)
        end

        run_test!
      end

      response '404', 'Message not found' do
        let(:id) { 'invalid' }
        let(:conversation_id){ 'invalid' }
        let(:entityId) { '1' }
        let(:entityType) { 'lead' }

        run_test!
      end
    end
  end
end
