# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::VariableMappingsController, type: :request do
  let(:user) { create(:user) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:headers) { valid_headers(valid_auth_token) }
  let(:no_whatsapp_permission_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, user_name: user.name) }
  let(:no_whatsapp_permission_headers) { valid_headers(no_whatsapp_permission_token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:another_user) { create(:user, tenant_id: user.tenant_id) }
  let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:user_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user) }
  let(:other_template) { create(:whatsapp_template, connected_account: other_connected_account, created_by: another_user) }
  let(:variable_mapping) { create(:variable_mapping, whatsapp_template: user_template, component_type: 'HEADER', template_variable: 53) }
  let(:other_variable_mapping) { create(:variable_mapping, whatsapp_template: user_template, component_type: 'BODY', template_variable: 33) }
  let(:variable_mapping_for_other_template) { create(:variable_mapping, whatsapp_template: other_template) }

  describe '#index' do

    context 'when user has only read permission' do
      before do
        variable_mapping
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
      end

      context 'when user is template creator' do
        it 'returns variable mappings for given template' do
          get "/v1/messages/whatsapp-templates/#{user_template.id}/variable-mappings", headers: headers

          expect(response).to be_ok
          expect(response.parsed_body['variableMappings']).to match_array([
            {
              "id" => variable_mapping.id,
              "componentType" => "HEADER",
              "templateVariable" => variable_mapping.template_variable,
              "entity" => "createdBy",
              "internalName" => "firstName",
              "fallbackValue" => "John",
              "fieldType" => "TEXT_FIELD"
            }
          ])
        end
      end

      context 'when user is connected account agent' do
        before { create(:agent_user, tenant_id: user.tenant_id, connected_account_id: other_connected_account.id, user_id: user.id) }

        it 'returns variable mappings for given template' do
          variable_mapping_for_other_template
          get "/v1/messages/whatsapp-templates/#{other_template.id}/variable-mappings", headers: headers

          expect(response).to be_ok
          expect(response.parsed_body.count).to eq(1)
        end
      end
    end


    context 'when user has read all' do
      it 'returns variables for given template' do
        variable_mapping
        get "/v1/messages/whatsapp-templates/#{user_template.id}/variable-mappings", headers: headers

        expect(response).to be_ok
        expect(response.parsed_body.count).to eq(1)
      end
    end

    context 'when template not found' do
      context 'when template does not exist for tenant' do
        it 'returns not found status' do
          get "/v1/messages/whatsapp-templates/-1/variable-mappings", headers: headers

          expect(response).to be_not_found
        end
      end

      context 'when user cannot access template' do
        before do
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
        end

        it 'returns unauthorized error' do
          get "/v1/messages/whatsapp-templates/#{other_template.id}/variable-mappings", headers: headers

          expect(response).to be_unauthorized
          expect(response.parsed_body['errorCode']).to eq('022002')
          expect(response.parsed_body['message']).to eq('Unauthorized access.')
        end
      end
    end
  end

  describe '#save' do
    let(:variable_mapping_params) do
      [
        {
          id: variable_mapping.id,
          componentType: "HEADER",
          templateVariable: 53,
          entity: "createdBy",
          internalName: "firstName",
          fallbackValue: "John Updated",
          fieldType: "TEXT_FIELD"
        },
        {
          id: other_variable_mapping.id,
          componentType: "BODY",
          templateVariable: 33,
          entity: "tenant",
          internalName: "website",
          fallbackValue: "Fallback Value Updated",
          fieldType: "URL"
        }
      ]
    end

    context 'when user has permission' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
          status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
        )
        stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
      end

      it 'updates the variables' do
        post "/v1/messages/whatsapp-templates/#{user_template.id}/variable-mappings/save", headers: headers, params: { variableMappings: variable_mapping_params }.to_json

        expect(response.parsed_body['variableMappings']).to match_array([
          {
            "id" => variable_mapping.id,
            "componentType" => "HEADER",
            "templateVariable" => 53,
            "entity" => "createdBy",
            "internalName" => "firstName",
            "fallbackValue" => "John Updated",
            "fieldType" => "TEXT_FIELD"
          },
          {
            "id" => other_variable_mapping.id,
            "componentType" => "BODY",
            "templateVariable" => 33,
            "entity" => "tenant",
            "internalName" => "website",
            "fallbackValue" => "Fallback Value Updated",
            "fieldType" => "URL"
          }
        ])
      end
    end

    context 'when template is does not exist' do
      it 'returns not found error' do
        post "/v1/messages/whatsapp-templates/-1/variable-mappings/save", headers: headers, params: { variableMappings: variable_mapping_params }.to_json

        expect(response.parsed_body['errorCode']).to eq('022006')
      end
    end

    context 'when user does not have permission to update template' do
      before do
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(false)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update_all').and_return(false)
      end

      it 'raises unauthorized error' do
        post "/v1/messages/whatsapp-templates/#{user_template.id}/variable-mappings/save", headers: headers, params: { variableMappings: variable_mapping_params }.to_json
        expect(response.parsed_body['errorCode']).to eq('022002')
        expect(response.parsed_body['message']).to eq('Unauthorized access.')
      end
    end
  end
end
