require 'swagger_helper'

RSpec.describe 'VariableMappings', type: :request do
  let(:user)             { create(:user) }
  let(:valid_auth_token) { build(:auth_token, :with_whatsapp_template_only_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)   { valid_auth_token.token }

  path '/v1/messages/whatsapp-templates/{id}/variable-mappings' do
    get 'Variable Mappings' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:id) { create(:whatsapp_template, connected_account: create(:connected_account, created_by: user)).id }

      response '200', 'Whatsapp template variables fetched successfully' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Template Not Found' do
        let(:id) { -1 }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}/variable-mappings/save' do
    post 'Save Variable Mappings' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :id, in: :path, type: :integer
      parameter name: :variable_mappings, in: :body, schema: {
        type: :object,
        properties: {
          variableMappings: {
            type: :array,
            properties: {
              componentType: {
                type: :string,
                enum: [HEADER, BODY, BUTTON]
              },
              templateVariable: { type: :integer },
              entity: { type: :string },
              internalName: { type: :string },
              fallbackValue: { type: :string },
              fieldType: { type: :string },
            }
          }
        }
      }

      let(:user) { create(:user) }
      let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
      let(:user_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user) }
      let(:other_template) { create(:whatsapp_template, connected_account: other_connected_account, created_by: another_user) }
      let(:variable_mapping) { create(:variable_mapping, whatsapp_template: user_template, component_type: 'HEADER', template_variable: 53) }
      let(:other_variable_mapping) { create(:variable_mapping, whatsapp_template: user_template, component_type: 'BODY', template_variable: 33) }

      let(:id) { user_template.id }
      let(:variable_mappings) {
        {
          variableMappings: [
          {
            id: variable_mapping.id,
            componentType: "HEADER",
            templateVariable: 53,
            entity: "createdBy",
            internalName: "firstName",
            fallbackValue: "John Updated",
            fieldType: "TEXT_FIELD"
          },
          {
            id: other_variable_mapping.id,
            componentType: "BODY",
            templateVariable: 33,
            entity: "lead",
            internalName: "lastName",
            fallbackValue: "Doe Updated",
            fieldType: "TEXT_FIELD"
          }
        ]
      }
      }

      response '200', 'Template Updated' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
            status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
          )
          stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
            .with(
              headers: {
                Authorization: "Bearer #{valid_auth_token.token}"
              }
            ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid Data' do
        let(:variable_mappings) {
          {
            variableMappings: [
              {
                id: variable_mapping.id,
                componentType: "INVALID",
                templateVariable: 53,
                entity: "createdBy",
                internalName: "firstName",
                fallbackValue: "John Updated",
                fieldType: "TEXT_FIELD"
              }
            ]
          }
        }

        run_test!
      end
    end
  end
end
