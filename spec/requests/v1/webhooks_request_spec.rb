# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::WebhooksController, type: :request do
  describe '#handler' do
    let(:whatsapp_template_approved_json) { JSON.parse(file_fixture('webhooks/whatsapp-template-approved.json').read) }

    context 'when webhook received' do
      before do
        channel = BunnyMock.new.start.channel
        exchange = channel.topic 'ex.message'
        @queue = channel.queue 'message.whatsapp.webhook'
        @queue.bind exchange, routing_key: 'message.whatsapp.webhook'
        allow(RabbitmqConnection).to receive(:get_exchange).with('ex.message', 'message').and_return(@queue)
        post '/v1/messages/whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks', headers: { 'Content-Type' => 'application/json' }, params: whatsapp_template_approved_json.to_json
      end

      it 'returns ok' do
        expect(response.status).to eq(200)
        expect(response.body).to eq('')
      end

      it 'publishs event' do
        expect(@queue.message_count).to eq(1)
      end

      it 'publishes request body as is' do
        payload = @queue.pop
        expect(payload.first[:routing_key]).to eq('message.interakt.webhook')
        expect(JSON.parse(payload.last)).to eq(JSON.parse(whatsapp_template_approved_json.to_json))
      end
    end
  end

  describe '#challenge' do
    context 'when integrating webhook url for whatsapp' do
      it 'returns the facebook hub challenge' do
        get '/v1/messages/whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks?hub.challenge=12345abcde'

        expect(response.status).to eq(200)
        expect(response.headers['content-type']).to eq('text/plain; charset=utf-8')
        expect(response.body).to eq('12345abcde')
      end
    end
  end
end
