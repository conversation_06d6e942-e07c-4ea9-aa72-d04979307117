require 'swagger_helper'

RSpec.describe 'Webhooks', type: :request do
  path '/v1/messages/whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks' do
    post 'Whatsapp Webhook' do
      tags 'Message'
      consumes 'application/json'

      parameter name: :webhook_body, in: :body, schema: {
        type: :object,
        properties: {
          object: {
            type: :string,
            description: 'Facebook subscribed business object'
          },
          entry: {
            type: :array,
            properties: {
              id: {
                type: :string,
                description: 'Whatsapp Business Account ID'
              },
              changes: {
                type: :array,
                properties: {
                  field: {
                    type: :string,
                    description: 'Whatsapp business entity for which value is given below. Ex: message_template_status_update'
                  },
                  value: {
                    type: :object,
                    description: 'Changes in entity; object differs accordingly'
                  }
                }
              }
            }
          }
        }
      }

      let(:webhook_body) { JSON.parse(file_fixture('webhooks/whatsapp-template-approved.json').read) }

      response '200', 'Whatsapp webhook received' do
        before { expect(PublishEvent).to receive(:call).with(instance_of(Event::InteraktWebhook)) }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks' do
    get 'Facebook Challenge' do
      tags 'Message'

      response '200', 'Facebook challenge successful' do
        run_test!
      end
    end
  end
end
