require 'swagger_helper'

RSpec.describe 'WhatsappCredits', type: :request do
  let(:user)              { create(:user) }
  let(:valid_auth_token)  { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)    { valid_auth_token.token }

  path '/v1/messages/whatsapp-credits/summary' do
    get 'Get Summary of Whatsapp credits' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      response '200', 'Return Whatsapp Credits Summary' do
        let!(:whatsapp_credit) { create(:whatsapp_credit, tenant_id: user.tenant_id) }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-credits/history' do
    post 'Get History of Whatsapp credits' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:page) { 1 }
      let(:size) { 10 }

      response '200', 'Return Whatsapp Credits History' do
        let!(:whatsapp_credit_history) { create(:whatsapp_credit_history, tenant_id: user.tenant_id) }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-credits/status' do
    get 'Get credits status for bulk message' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      response '200', 'Return Whatsapp Credits Summary' do
        let!(:whatsapp_credit) { create(:whatsapp_credit, tenant_id: user.tenant_id) }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        run_test!
      end
    end
  end
end
