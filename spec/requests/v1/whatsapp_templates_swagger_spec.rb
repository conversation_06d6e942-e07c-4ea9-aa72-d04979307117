require 'swagger_helper'

RSpec.describe 'WhatsappTemplates', type: :request do
  let(:user)             { create(:user) }
  let(:valid_auth_token) { build(:auth_token, :with_whatsapp_template_only_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let!(:Authorization)   { valid_auth_token.token }

  path '/v1/messages/whatsapp-templates/{id}' do
    get 'Whatsapp Template' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:id) { create(:whatsapp_template, connected_account: create(:connected_account, created_by: user)).id }

      response '200', 'Whatsapp Template fetched successfully' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Template Not Found' do
        let(:id) { -1 }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/search' do
    post 'Search Whatsapp Templates' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :page, in: :query, type: :string
      parameter name: :size, in: :query, type: :string
      parameter name: :sort, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :search_body, in: :body, schema: {
        type: :object,
        properties: {
          jsonRule: {
            condition: { type: :string },
            rules: {
              type: :array,
              properties: {
                field: { type: :string },
                type: { type: :string },
                operator: { type: :string },
                value: { type: :string }
              }
            }
          }
        }
      }

      let(:page) { 1 }
      let(:size) { 10 }
      let(:sort) { 'updatedAt,desc' }
      let(:search_body) {
        {
          jsonRule: {
            rules: [{ field: 'name', type: 'string', operator: 'equal', value: 'Sale Offer' }],
            condition: 'AND',
            valid: true
          }
        }
      }

      response '200', 'Successful search' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid search' do
        let(:search_body) {
          {
            jsonRule: {
              rules: [{ field: 'tenantId', type: 'long', operator: 'equal', value: 1 }],
              condition: 'AND',
              valid: true
            }
          }
        }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates' do
    post 'Create Whatsapp Template' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :template_body, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string },
          language: { type: :string },
          category: {
            type: :string,
            enum: [MARKETING, UTILITY]
          },
          entityType: {
            type: :string,
            enum: [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT]
          },
          connectedAccount: {
            type: :object,
            properties: {
              id: { type: :string },
              name: { type: :integer }
            }
          },
          components: {
            type: :array,
            properties: {
              type: {
                type: :string,
                enum: [HEADER, BODY, FOOTER, BUTTON]
              },
              format: {
                type: :string,
                enum: [TEXT, PHONE_NUMBER, URL, QUICK_REPLY, COPY_CODE]
              },
              text: { type: :string },
              value: { type: :string },
              position: { type: :integer },
              content: { type: :object }
            }
          }
        }
      }

      let(:connected_account) { create(:connected_account, created_by: user) }
      let(:template_body) do
        parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
        parsed_params['connectedAccount']['id'] = connected_account.id
        parsed_params
      end

      response '200', 'Template Created' do
        before { create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id) }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid params' do
        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/create-and-submit' do
    post 'Create & Submit Whatsapp Template' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :template_body, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string },
          language: { type: :string },
          category: {
            type: :string,
            enum: [MARKETING, UTILITY]
          },
          entityType: {
            type: :string,
            enum: [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT]
          },
          connectedAccount: {
            type: :object,
            properties: {
              id: { type: :string },
              name: { type: :integer }
            }
          },
          components: {
            type: :array,
            properties: {
              type: {
                type: :string,
                enum: [HEADER, BODY, FOOTER, BUTTON]
              },
              format: {
                type: :string,
                enum: [TEXT, PHONE_NUMBER, URL, QUICK_REPLY, COPY_CODE]
              },
              text: { type: :string },
              value: { type: :string },
              position: { type: :integer },
              content: { type: :object }
            }
          }
        }
      }

      let(:connected_account) { create(:connected_account, created_by: user) }
      let(:template_body) do
        parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
        parsed_params['connectedAccount']['id'] = connected_account.id
        parsed_params
      end

      response '200', 'Template Created & Submitted' do
        before do
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: "{\"name\":\"seasonal_promotion_lead\",\"language\":\"en\",\"category\":\"MARKETING\",\"components\":[{\"type\":\"HEADER\",\"format\":\"TEXT\",\"text\":\"Our {{1}} is on!\",\"example\":{\"header_text\":[\"a\"]}},{\"type\":\"BODY\",\"text\":\"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.\",\"example\":{\"body_text\":[[\"a\",\"a\",\"a\"]]}},{\"type\":\"FOOTER\",\"text\":\"Use the buttons below to manage your marketing subscriptions\"},{\"type\":\"BUTTONS\",\"buttons\":[{\"type\":\"QUICK_REPLY\",\"text\":\"Unsubscribe from Promos\"},{\"type\":\"COPY_CODE\",\"example\":\"25OFF\"}]}]}"
            )
            .to_return(status: 200, body: file_fixture('facebook/whatsapp_template/create-template-success-response.json').read)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid params' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: "{\"name\":\"seasonal_promotion_lead\",\"language\":\"en\",\"category\":\"MARKETING\",\"components\":[{\"type\":\"HEADER\",\"format\":\"TEXT\",\"text\":\"Our {{1}} is on!\",\"example\":{\"header_text\":[\"a\"]}},{\"type\":\"BODY\",\"text\":\"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.\",\"example\":{\"body_text\":[[\"a\",\"a\",\"a\"]]}},{\"type\":\"FOOTER\",\"text\":\"Use the buttons below to manage your marketing subscriptions\"},{\"type\":\"BUTTONS\",\"buttons\":[{\"type\":\"QUICK_REPLY\",\"text\":\"Unsubscribe from Promos\"},{\"type\":\"COPY_CODE\",\"example\":\"25OFF\"}]}]}"
            )
            .to_return(status: 400, body: file_fixture('facebook/whatsapp_template/invalid-template-response.json').read)
        end

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}' do
    put 'Update Whatsapp Template' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :id, in: :path, type: :integer
      parameter name: :template_body, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string },
          components: {
            type: :array,
            properties: {
              id: { type: :integer },
              type: {
                type: :string,
                enum: [HEADER, BODY, FOOTER, BUTTON]
              },
              format: {
                type: :string,
                enum: [TEXT, PHONE_NUMBER, URL, QUICK_REPLY, COPY_CODE]
              },
              text: { type: :string },
              value: { type: :string },
              position: { type: :integer },
              content: { type: :object }
            }
          }
        }
      }

      let(:connected_account) { create(:connected_account, created_by: user) }
      let(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account) }
      let(:id) { whatsapp_template.id }
      let(:template_body) do
        parsed_params = JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)
        parsed_params['id'] = whatsapp_template.id
        parsed_params['components'].find { |comp| comp['type'] == 'HEADER' }['id'] = whatsapp_template.components.find_by(component_type: 'HEADER').id
        parsed_params['components'].find { |comp| comp['type'] == 'BODY' }['id'] = whatsapp_template.components.find_by(component_type: 'BODY').id
        parsed_params['components'].reject! { |comp| comp['type'] == 'FOOTER' }
        parsed_params['components'].find { |comp| comp['type'] == 'BUTTON' && comp['format'] == 'QUICK_REPLY' }['id'] = whatsapp_template.components.find_by(component_type: 'BUTTON', component_format: 'QUICK_REPLY').id
        parsed_params
      end

      response '200', 'Template Updated' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: "{\"name\":\"#{whatsapp_template.whatsapp_template_namespace}\",\"language\":\"en\",\"category\":\"MARKETING\",\"components\":[{\"type\":\"HEADER\",\"format\":\"TEXT\",\"text\":\"Our {{1}} is on!\",\"example\":{\"header_text\":[\"a\"]}},{\"type\":\"BODY\",\"text\":\"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.\",\"example\":{\"body_text\":[[\"a\",\"a\",\"a\"]]}},{\"type\":\"BUTTONS\",\"buttons\":[{\"type\":\"QUICK_REPLY\",\"text\":\"Unsubscribe from Promos\"},{\"type\":\"COPY_CODE\",\"example\":\"25OFF\"}]}]}"
            )
            .to_return(status: 200, body: file_fixture('facebook/whatsapp_template/create-template-success-response.json').read)
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappTemplateNameUpdated))
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Template Not Found' do
        let(:id) { -1 }

        run_test!
      end

      response '422', 'Invalid params' do
        before do
          template_body['components'] << { type: 'BUTTON', format: 'QUICK_REPLY', position: 3 }
        end

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}/deactivate' do
    post 'Deactivate Whatsapp Template' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:whatsapp_template) { create(:whatsapp_template, connected_account: create(:connected_account, created_by: user)) }
      let(:id) { whatsapp_template.id }

      response '200', 'Whatsapp Template Deactivated' do
        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Template Not Found' do
        let(:id) { -1 }

        run_test!
      end

      response '422', 'Invalid template status to update' do
        before { whatsapp_template.update(status: 'PENDING') }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{entity}/variables' do
    get 'Whatsapp Template Entity Variables' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :entity, in: :path, type: :string, enum: [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:entity) { 'lead' }

      response '200', 'Entity variables fetched successfully' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
            status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
          )

          stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '422', 'Invalid params' do
        let(:entity) { 'invalid' }
        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/lookup' do
    get 'Whatsapp Template Lookup' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :connectedAccountId, in: :query, type: :string
      parameter name: :entityType, in: :query, type: :string, enum: [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL]
      parameter name: :q, in: :query, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      response '200', 'Whatsapp Templates fetched successfully' do
        let(:connectedAccountId) { 12 }
        let(:entityType) { 'lead' }
        let(:q) { 'template' }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:connectedAccountId) { 12 }
        let(:entityType) { 'lead' }
        let(:q) { 'template' }

        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}/preview/{entity_type}/{entity_id}' do
    get 'Whatsapp Template Preview' do
      tags 'Message'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :string
      parameter name: :entity_type, in: :path, type: :string, enum: [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL]
      parameter name: :entity_id, in: :path, type: :string

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      let(:connected_account) { create(:connected_account, created_by: user) }
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: user, status: 'APPROVED') }

      let(:id) { whatsapp_template.id }
      let(:entity_type) { 'lead' }
      let(:entity_id) { 34343 }

      response '200', 'Whatsapp Templates preview' do
        before do
          allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
          create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
          create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
          create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
          create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
          create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')
          auth_data = User::TokenParser.parse(valid_auth_token.token)
          permissions = auth_data.permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call

          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[firstName createdBy phoneNumbers customFieldValues lastName ownerId],
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          user_resp = JSON.parse(file_fixture('get_users_response.json').read)
          user_resp['content'][2]['id'] = user.id
          stub_request(:post, "http://localhost:8081/v1/users/search").with(
            body: {
              fields: %w[timezone dateFormat firstName lastName],
              jsonRule: {
                rules: [
                  {
                    operator: 'in',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: "4010,#{user.id}"
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: user_resp.to_json, headers: {})

          stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
              headers: {
              'Accept'=>'*/*',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}/send' do
    post 'Send Template Message' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          entityType: { type: :string },
          entityId: { type: :integer },
          phoneId: { type: :integer }
        }
      }

      let(:connected_account) { create(:connected_account, created_by: user) }
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: user, status: 'APPROVED' ) }
      let(:id) { whatsapp_template.id }
      let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
      let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
      let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
      let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
      let!(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }

      context 'for valid data' do
        before do
          allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          auth_data = User::TokenParser.parse(valid_auth_token.token)

          permissions = auth_data.permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers customFieldValues firstName lastName ownerId],
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
          whatsapp_template.components.destroy_all

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'template',
              template: {
                name: whatsapp_template.whatsapp_template_namespace,
                language: {
                  code: 'en'
                },
                components: [
                  {
                    "type": "header",
                    "parameters": []
                  },
                  {
                    "type": "body",
                    "parameters": []
                  }
                ]
              }
            }.to_json,
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              'Content-Type'=>'application/json',
            }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
        end

        let(:payload) { { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 } }


        response '200', 'Successfully message sent' do
          before { expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated)) }

          run_test!
        end


        response '422', 'Invalid phone id ' do
          let(:payload) { { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 20778 } }
          before { expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MessageCreated)) }

          run_test!
        end
      end

      response '404', 'Missing template' do
        let(:id) { -1 }
        let(:payload) { { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 } }

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:payload) { { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 } }

        run_test!
      end

      response '422', 'Insufficient whatsapp credits balance' do
        before(:each) do
          whatsapp_credit.update(total: 0)
        end

        let(:payload) { { id: whatsapp_template.id, entityType: 'lead', entityId: 34343, phoneId: 207783 } }

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}/send-bulk-message' do
    post 'Send Bulk Message' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter name: :id, in: :path, type: :integer
      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          entityType: { type: :string },
          entityId: { type: :integer },
          phoneNumber: { type: :object },
          entityData: { type: :object },
          bulkJob: { type: :boolean }
        }
      }

      let(:connected_account) { create(:connected_account, created_by: user) }
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: user, status: 'APPROVED' ) }
      let(:id) { whatsapp_template.id }
      let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
      let!(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
      let!(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
      let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
      let!(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD', actions: { 'sms': true }) }

      context 'for valid data' do
        before do
          allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
          auth_data = User::TokenParser.parse(valid_auth_token.token)

          permissions = auth_data.permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          token_without_pid = GenerateToken.new(user.id, auth_data.tenant_id, permissions, true).call

          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers firstName lastName ownerId],
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
          whatsapp_template.components.destroy_all

          stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
            headers: {
            'Accept'=>'*/*',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            }
          ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: '+************',
              type: 'template',
              template: {
                name: whatsapp_template.whatsapp_template_namespace,
                language: {
                  code: 'en'
                },
                components: [
                  {
                    "type": "header",
                    "parameters": []
                  },
                  {
                    "type": "body",
                    "parameters": []
                  }
                ]
              }
            }.to_json,
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              'Content-Type'=>'application/json',
            }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
          end

        let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }
        let(:payload) do
          {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details
          }
        end

        response '200', 'Successfully message sent' do
          before { expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated)) }

          run_test!
        end
      end

      response '404', 'Missing template' do
        let(:id) { -1 }
        let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }
        let(:payload) do
          {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details
          }
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }
        let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }
        let(:payload) do
          {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
            entityData: entity_details
          }
        end

        run_test!
      end

      response '422', 'Insufficient whatsapp credits balance' do
        before(:each) do
          whatsapp_credit.update(total: 0)
        end

        before { expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappCreditsAboutToExpire)) }

        let(:entity_details) { JSON.parse(file_fixture('entity-details-response.json').read) }
        let(:payload) do
          {
            id: whatsapp_template.id,
            entityType: 'lead',
            entityId: 34343,
            phoneNumber: {
              "code": "IN",
              "dialCode": "+91",
              "id": 216106,
              "type": "WORK",
              "value": "**********",
              "primary": true
            },
          entityData: entity_details
          }
        end

        run_test!
      end
    end
  end

  path '/v1/messages/whatsapp-templates/{id}/sync-status' do
    put 'Sync Whatsapp Template Status' do
      tags 'Message'
      consumes 'application/json'
      security [ bearerAuth: [] ]

      parameter({
        :in => :header,
        :type => :string,
        :name => :Authorization,
        :required => true,
        :description => 'User token'
      })

      parameter name: :id, in: :path, type: :integer

      let(:connected_account) { create(:connected_account, created_by: user) }
      let(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account) }
      let(:id) { whatsapp_template.id }

      response '200', 'Template Updated' do
        before do
          stub_request(:get, "https://graph.facebook.com/v19.0/#{whatsapp_template.whatsapp_template_id}")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 200, body: { status: APPROVED }.to_json )
        end

        run_test!
      end

      response '401', 'Authentication failed' do
        let(:Authorization) { "Basic #{::Base64.strict_encode64('bogus:bogus')}" }

        run_test!
      end

      response '404', 'Template Not Found' do
        let(:id) { -1 }

        run_test!
      end
    end
  end
end
