require 'rails_helper'

RSpec.describe BuildPermissionsForToken do
  describe "#call" do
    before do
      @profile_permissions = {
        "name": "Temp",
        "active": true,
        "description": "",
        "permission": [
          {
            "id": 6,
            "name": "customField",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 7,
            "name": "team",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 8,
            "name": "config",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 11,
            "name": "shareRule",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 13,
            "name": "profile",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 9,
            "name": "searchList",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 3,
            "name": "user",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 14,
            "name": "conversionMapping",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 19,
            "name": "availability",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 20,
            "name": "layout",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 23,
            "name": "report",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 24,
            "name": "lead-capture-forms",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 15,
            "name": "task",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 18,
            "name": "company",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 10,
            "name": "pipeline",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 22,
            "name": "products-services",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 17,
            "name": "contact",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": true, "update": true, "delete": false, "email": true, "call": true, "sms": false, "task": false, "note": false, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 16,
            "name": "note",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": false, "deleteAll": true }
          },
          {
            "id": 12,
            "name": "deal",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": false, "note": false, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 29,
            "name": "workflow",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 38,
            "name": "call",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": true }
          },
          {
            "id": 31,
            "name": "email_template",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": true, "deleteAll": false }
          },
          {
            "id": 4,
            "name": "lead",
            "displayName": nil,
            "description": nil,
            "actions": { "read": true, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          },
          {
            "id": 35,
            "name": "email",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": true }
          },
          {
            "id": 34,
            "name": "meeting",
            "displayName": nil,
            "description": nil,
            "actions": { "read": false, "write": false, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": false, "updateAll": false, "deleteAll": false }
          }
        ]
    }
    end

    context 'when profile permissions are passed' do
      it 'builds permission data necessary to generate token' do
        result = BuildPermissionsForToken.call(JSON.parse(JSON(@profile_permissions))['permission'])

        lead_permission = result.find{|p| p[:name] == 'lead'}
        expect(lead_permission[:name]).to eq('lead')
        expect(lead_permission['description']).to eq('has access to lead resource')
        expect(lead_permission['limits']).to eq(-1)
        expect(lead_permission['units']).to eq('count')
        expect(lead_permission['action']).to eq({ "read"=>true, "write"=>false, "update"=>false, "delete"=>false, "email"=>false, "call"=>false, "sms"=>false, "task"=>false, "note"=>false, "meeting"=>false, "readAll"=>false, "updateAll"=>false, "deleteAll"=>false })

        deal_permission = result.find{|p| p[:name] == 'deal'}
        expect(deal_permission[:name]).to eq('deal')
        expect(deal_permission['description']).to eq('has access to deal resource')
        expect(deal_permission['limits']).to eq(-1)
        expect(deal_permission['units']).to eq('count')
        expect(deal_permission['action']).to eq({ "read"=>true, "write"=>true, "update"=>true, "delete"=>true, "email"=>true, "call"=>true, "sms"=>false, "task"=>false, "note"=>false, "meeting"=>true, "readAll"=>false, "updateAll"=>false, "deleteAll"=>false})

        contact_permission = result.find{|p| p[:name] == 'contact'}
        expect(contact_permission[:name]).to eq('contact')
        expect(contact_permission['description']).to eq('has access to contact resource')
        expect(contact_permission['limits']).to eq(-1)
        expect(contact_permission['units']).to eq('count')
        expect(contact_permission['action']).to eq({"read"=>true, "write"=>true, "update"=>true, "delete"=>false, "email"=>true, "call"=>true, "sms"=>false, "task"=>false, "note"=>false, "meeting"=>true, "readAll"=>false, "updateAll"=>false, "deleteAll"=>false})
      end
    end
  end
end