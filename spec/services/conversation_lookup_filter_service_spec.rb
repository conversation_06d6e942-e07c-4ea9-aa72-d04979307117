require 'rails_helper'

RSpec.describe ConversationLookupFilterService do
  let(:current_user) { create(:user) }
  let(:other_user) { create(:user, tenant_id: current_user.tenant_id) }
  let(:service) { described_class.new(current_user) }
  let(:valid_auth_token) { build(:auth_token, user_id: current_user.id, tenant_id: current_user.tenant_id, username: current_user.name ) }
  let(:valid_auth_data) { User::TokenParser.parse(valid_auth_token.token) }

  describe '#filter_all_lookups' do
    let!(:conversation1) { create(:conversation, tenant_id: current_user.tenant_id, phone_number: '+919942384499') }
    let!(:conversation2) { create(:conversation, tenant_id: current_user.tenant_id, phone_number: '+919942384455') }
    
    let!(:lead_lookup1) { create(:look_up, entity_type: 'lead', tenant_id: current_user.tenant_id, owner_id: current_user.id) }
    let!(:lead_lookup2) { create(:look_up, entity_type: 'lead', tenant_id: current_user.tenant_id, owner_id: other_user.id) }
    let!(:contact_lookup1) { create(:look_up, entity_type: 'contact', tenant_id: current_user.tenant_id, owner_id: current_user.id) }
    let!(:contact_lookup2) { create(:look_up, entity_type: 'contact', tenant_id: current_user.tenant_id, owner_id: other_user.id) }

    before do
      Thread.current[:auth] = valid_auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = current_user

      create(:conversation_look_up, conversation: conversation1, look_up: lead_lookup1)
      create(:conversation_look_up, conversation: conversation1, look_up: contact_lookup1)
      create(:conversation_look_up, conversation: conversation2, look_up: lead_lookup2)
      create(:conversation_look_up, conversation: conversation2, look_up: contact_lookup2)
    end

    context 'when user has read_all permission for both LEAD and CONTACT' do
      before do
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_LEAD).and_return(true)
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_CONTACT).and_return(true)
      end

      it 'returns all lookups for the given conversations' do
        result = service.filter_all_lookups([conversation1.id, conversation2.id])
        
        expect(result).to include(lead_lookup1, lead_lookup2, contact_lookup1, contact_lookup2)
        expect(result.size).to eq(4)
      end
    end

    context 'when user has read_all permission for only LEAD' do
      before do
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_LEAD).and_return(true)
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_CONTACT).and_return(false)
      end

      it 'returns all lead lookups and owned contact lookups' do
        result = service.filter_all_lookups([conversation1.id, conversation2.id])
        
        expect(result).to include(lead_lookup1, lead_lookup2, contact_lookup1)
        expect(result).not_to include(contact_lookup2)
        expect(result.size).to eq(3)
      end
    end

    context 'when user has shared entity access' do
      let(:shared_lead) { create(:look_up, entity_type: 'lead', tenant_id: current_user.tenant_id) }
      let(:shared_contact) { create(:look_up, entity_type: 'contact', tenant_id: current_user.tenant_id) }
      
      before do
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_LEAD).and_return(false)
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_CONTACT).and_return(false)
        
        # Mock shared entities
        shared_entities = {
          'LEAD' => { entity_ids: [shared_lead.entity_id], entity_owner_ids: [] },
          'CONTACT' => { entity_ids: [], entity_owner_ids: [shared_contact.owner_id] }
        }
        allow(current_user).to receive(:get_shared_entity_records).and_return(shared_entities)
        
        # Associate shared lookups
        create(:conversation_look_up, conversation: conversation1, look_up: shared_lead)
        create(:conversation_look_up, conversation: conversation2, look_up: shared_contact)
      end

      it 'returns owned lookups and shared lookups' do
        result = service.filter_all_lookups([conversation1.id, conversation2.id])
        
        expect(result).to include(lead_lookup1, contact_lookup1, shared_lead, shared_contact)
        expect(result).not_to include(lead_lookup2, contact_lookup2)
        expect(result.size).to eq(4)
      end
    end

    context 'when user has no special permissions' do
      before do
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_LEAD).and_return(false)
        allow(current_user).to receive(:can_read_all_entity?).with(LOOKUP_CONTACT).and_return(false)
        allow(current_user).to receive(:get_shared_entity_records).and_return({
          'LEAD' => { entity_ids: [], entity_owner_ids: [] },
          'CONTACT' => { entity_ids: [], entity_owner_ids: [] }
        })
      end

      it 'returns only owned lookups' do
        result = service.filter_all_lookups([conversation1.id, conversation2.id])
        
        expect(result).to include(lead_lookup1, contact_lookup1)
        expect(result).not_to include(lead_lookup2, contact_lookup2)
        expect(result.size).to eq(2)
      end
    end

    context 'with empty conversation_ids' do
      it 'returns empty result' do
        result = service.filter_all_lookups([])
        expect(result).to be_empty
      end
    end
  end
end 