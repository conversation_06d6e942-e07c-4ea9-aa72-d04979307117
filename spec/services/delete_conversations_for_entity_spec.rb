require 'rails_helper'

RSpec.describe DeleteConversationsForEntity do
  let(:user) { create(:user) }
  let(:tenant_id) { user.tenant_id }
  let(:user_id) { user.id }
  let(:entity_id) { rand(100) }
  let(:entity_type) { 'lead' }
  let(:data) { { user_id: user_id, tenant_id: tenant_id, entity_id: entity_id } }

  describe '#call' do
    context 'when conversation is associated with other entities' do
      let(:conversation) { create(:conversation, tenant_id: tenant_id) }
      let(:look_up) { create(:look_up, tenant_id: tenant_id, entity_type: entity_type, entity_id: entity_id) }
      let(:other_look_up) { create(:look_up, tenant_id: tenant_id, entity_type: entity_type, entity_id: SecureRandom.uuid) }

      before do
        conversation.look_ups << look_up
        conversation.look_ups << other_look_up
      end

      it 'removes only the association with the deleted entity' do
        expect {
          described_class.new(entity_type, data).call
        }.to change { conversation.conversation_look_ups.count }.by(-1)
          .and change { LookUp.count }.by(-1)

        expect(conversation.reload.look_ups).to include(other_look_up)
        expect(conversation.reload.look_ups).not_to include(look_up)
      end
    end

    context 'when conversation is only associated with the deleted entity' do
      let(:conversation) { create(:conversation, tenant_id: tenant_id) }
      let(:look_up) { create(:look_up, tenant_id: tenant_id, entity_type: entity_type, entity_id: entity_id) }
      let!(:message) { create(:message, conversation_id: conversation.id, tenant_id: tenant_id) }

      before do
        conversation.look_ups << look_up
      end

      it 'soft deletes the conversation and publishes message deleted events' do
        expect(Publishers::MessageDeleted).to receive(:call)
        expect {
          described_class.new(entity_type, data).call
        }.to change { conversation.reload.deleted_at }.from(nil)
          .and change { LookUp.count }.by(-1)
      end
    end

    context 'when there are multiple conversations' do
      let(:conversation1) { create(:conversation, tenant_id: tenant_id) }
      let!(:message1) { create(:message, conversation_id: conversation1.id, tenant_id: tenant_id) }
      let(:conversation2) { create(:conversation, tenant_id: tenant_id, phone_number: '+9112312') }
      let(:look_up) { create(:look_up, tenant_id: tenant_id, entity_type: entity_type, entity_id: entity_id) }
      let(:other_look_up) { create(:look_up, tenant_id: tenant_id, entity_type: entity_type, entity_id: SecureRandom.uuid) }

      before do
        conversation1.look_ups << look_up
        conversation2.look_ups << look_up
        conversation2.look_ups << other_look_up
      end

      it 'handles each conversation appropriately' do
        expect(Publishers::MessageDeleted).to receive(:call).with(tenant_id, user_id, anything)
        expect {
          described_class.new(entity_type, data).call
        }.to change { conversation1.reload.deleted_at }.from(nil)
          .and change { conversation2.conversation_look_ups.count }.by(-1)
          .and change { LookUp.count }.by(-1)

        expect(conversation2.reload.look_ups).to include(other_look_up)
        expect(conversation2.reload.look_ups).not_to include(look_up)
      end
    end
  end
end 