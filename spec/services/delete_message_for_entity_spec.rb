require 'rails_helper'

RSpec.describe DeleteMessageForEntity do
  let(:user) { create(:user) }
  let(:entity_type) { LOOKUP_LEAD }
  let(:message) { create(:message, owner: user, tenant_id: user.tenant_id) }
  let(:look_up) { create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id) }
  let(:data) { { entity_id: look_up.entity_id, tenant_id: user.tenant_id, user_id: user.id } }
  let(:valid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }

  before do
    Thread.current[:user] = user
    Thread.current[:auth] = auth_data
    Thread.current[:token] = valid_auth_token.token
    message.related_to = [look_up]
  end

  describe '#call' do
    context 'when user has delete permission' do
      it 'soft deletes the message' do
        expect(Publishers::MessageDeleted).to receive(:call)
        described_class.call(entity_type, data)
        expect(message.reload.deleted_at).not_to be_nil
      end

      it 'deletes the recipients entities' do
        expect(Publishers::MessageDeleted).to receive(:call)
        described_class.call(entity_type, data)
        expect(MessageLookUp.find_by(message_id: message.id, look_up_id: look_up.id)).to be_nil
      end
    end

    context 'when message is only related to the entity' do
      before do
        expect(Publishers::MessageDeleted).to receive(:call)
      end

      it 'soft deletes the message' do
        expect {
          described_class.new(entity_type, data).call
        }.to change { message.reload.deleted_at }.from(nil)
      end

      it 'destroys the look_up' do
        expect {
          described_class.new(entity_type, data).call
        }.to change { LookUp.count }.by(-1)
      end
    end

    context 'when message is related to multiple entities' do
      let!(:look_up2) { create(:look_up, entity_type: 'contact', tenant_id: user.tenant_id) }
      
      before do
        message.related_to << look_up2
      end

      it 'removes the entity from message recipients' do
        expect {
          described_class.new(entity_type, data).call
        }.to change { message.related_look_ups.count }.by(-1)
      end

      it 'does not soft delete the message' do
        expect {
          described_class.new(entity_type, data).call
        }.not_to change { message.reload.deleted_at }
      end

      it 'destroys the look_up' do
        expect {
          described_class.new(entity_type, data).call
        }.to change { LookUp.count }.by(-1)
      end
    end

    context 'when destroy_look_ups is false' do
      it 'does not destroy the look_up' do
        expect {
          described_class.new(entity_type, data, false).call
        }.not_to change { LookUp.count }
      end
    end

    context 'when message is part of a conversation' do
      let!(:conversation) { create(:conversation, tenant_id: user.tenant_id) }

      before do
        message.update(conversation_id: conversation.id)
      end

      it 'does not process the message' do
        expect {
          described_class.new(entity_type, data).call
        }.not_to change { message.reload.deleted_at }
      end
    end
  end
end
