# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EntityLabels do
  describe '#call' do
    before do
      thread = Thread.current
      thread[:token] = build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: 12, tenant_id: 32, username: 'asd').token
      stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
        status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
      )
    end

    [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].each do |entity|
      context "when entity labels are requested for #{entity}" do
        it 'should return entity labels' do
          result = described_class.new(entity).call

          expect(result.keys).to match_array(%w[displayName displayNamePlural])
        end
      end
    end
  end
end
