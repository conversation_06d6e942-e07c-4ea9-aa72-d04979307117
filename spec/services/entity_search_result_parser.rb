require 'rails_helper'

RSpec.describe EntitySearchResultParser do
  describe "#call" do
    context "with valid input - " do
      it "returns matched and unmatched data correctly in output" do
        phone_numbers = [Phonelib.parse('+917387577710'), Phonelib.parse('+917387577711')]
        data = {"content":[{ "phoneNumbers":[{ "code": "IN", "dialCode": "+91", "type": "MOBILE", "value": "7387577710",
                             "primary": true }],"name":"lead1 test","id":1 },
                             { "phoneNumbers":[{ "code": "IN", "dialCode": "+91", "type": "MOBILE", "value": "7387577712",
                              "primary": true }],"firstName":"lead2","lastName":"test","id":2 }
                          ]}.to_json

        result = EntitySearchResultParser.new(phone_numbers, JSON(data), LOOKUP_LEAD, 1).call

        expect(result[:matched].first[:entity]).to eq('lead')
        expect(result[:matched].first[:id]).to eq(1)
        expect(result[:matched].first[:phone_number]).to eq('+917387577710')
        expect(result[:matched].first[:name]).to eq('lead1 test')
        expect(result[:matched].count).to eq(1)

        expect(result[:unmatched].first).to eq('+917387577711')
        expect(result[:unmatched].count).to eq(1)
      end
    end

    context "when search result data is empty" do
      it "returns matched and unmatched data correctly in output" do
        phone_numbers = [Phonelib.parse('+917387577710'), Phonelib.parse('+917387577711')]
        raw_phone_numbers = ['+917387577710', '+917387577711']
        data = {"content": []}.to_json

        result = EntitySearchResultParser.new(phone_numbers, JSON(data), LOOKUP_LEAD, 1).call

        expect(result[:matched].count).to eq(0)

        expect(result[:unmatched]).to match_array(raw_phone_numbers)
        expect(result[:unmatched].count).to eq(2)
      end
    end
  end
end
