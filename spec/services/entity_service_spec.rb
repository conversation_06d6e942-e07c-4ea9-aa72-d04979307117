# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EntityService do
  before do
    user = create(:user, id: 12, tenant_id: 99)
    auth_data =  build(:auth_data, :lead_with_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
    thread = Thread.current
    thread[:auth] = auth_data
    thread[:user] = user
    @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
    thread[:token] = @token
  end

  describe '#get_by_id' do
    let(:user){ create(:user) }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }

    before(:each) do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token
      Thread.current[:user] = user

      permissions = Thread.current[:auth].permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
    end

    context 'when requested with valid arguments' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{@token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it 'returns entity data' do
        entity_response = EntityService.new({ entity_type: 'lead', entity_id: 34343 }).get_by_id
        expect(entity_response.dig('id')).to eq(34343)
      end
    end

    context 'when entity_id is blank' do
      it 'returns empty data' do
        expect(EntityService.new({ entity_type: 'lead' }).get_by_id).to eq({})
      end
    end

    context 'when response contains empty content' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{@token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: { content: [] }.to_json, headers: {})
      end

      it 'raises invalid error' do
        expect { EntityService.new({ entity_type: 'lead', entity_id: 34343 }).get_by_id }.to raise_error(ExceptionHandler::NotFound, '022006||lead not found')
      end
    end

    context 'when request fails' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{@token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 500, body: {}.to_json, headers: {})
      end

      it 'raises error' do
        expect { EntityService.new({entity_type: 'lead', entity_id: 34343}).get_by_id }.to raise_error(ExceptionHandler::InternalServerError, '022004')
      end
    end
  end

  describe '#create' do
    context 'when parameters are correct' do
      before do
        stub_request(:post, "http://localhost:8082/v1/leads").with(
          body: {
            lastName: "Tony Stark",
          },
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{@token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('lead-created-response.json').read, headers: {})
      end

      it 'creates given entity' do
        described_class.new({ entity_type: 'lead', payload: { lastName: 'Tony Stark' } }).create
      end
    end

    context 'when payload parameters are missing' do
      it 'does nothing' do
        expect(Rails.logger).to receive(:error).with("Incorrect Payload in EntityService create - params: {}")
        described_class.new({}).create
      end
    end

    context 'when entity_type is incorrect' do
      it 'does nothing' do
        expect(Rails.logger).to receive(:error).with("Invalid entity type in EntityService create - params: {:entity_type=>\"deal\", :payload=>{:lastName=>\"Tony Stark\"}}")
        described_class.new({ entity_type: 'deal', payload: { lastName: 'Tony Stark' } }).create
      end
    end
  end

  describe '#get_by_json_rule' do
    let(:user){ create(:user) }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }

    before(:each) do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when requested with valid arguments' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead?page=1&size=10').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'phoneNumbers',
                  field: 'phoneNumbers',
                  type: 'string',
                  value: '989098'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it 'returns entities' do
        entity_response = EntityService.new({ entity_type: 'lead', phone_number: '989098', fields: %w[phoneNumbers id], query_params: 'page=1&size=10' }).get_by_json_rule
        expect(entity_response.dig('content', 0, 'id')).to eq(34343)
      end
    end

    context 'when phone_number is blank' do
      it 'returns empty data' do
        expect(EntityService.new({ entity_type: 'lead' }).get_by_json_rule).to eq({})
      end
    end

    context 'when request fails' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'phoneNumbers',
                  field: 'phoneNumbers',
                  type: 'string',
                  value: '989098'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 500, body: {}.to_json, headers: {})
      end

      it 'raises error' do
        expect { EntityService.new({entity_type: 'lead', phone_number: '989098', fields: %w[phoneNumbers id] }).get_by_json_rule }.to raise_error(ExceptionHandler::InternalServerError, '022004')
      end
    end
  end

  describe '#get_by_id with LOOKUP_DEAL' do
    let(:user){ create(:user) }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }

    before(:each) do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token
      Thread.current[:user] = user

      permissions = Thread.current[:auth].permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
    end

    context 'when requested with valid deal arguments' do
      before do
        stub_request(:post, 'http://localhost:8083/v1/search/deal').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 239051
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{@token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_deal_response.json').read, headers: {})
      end

      it 'returns deal data' do
        entity_response = EntityService.new({ entity_type: 'deal', entity_id: 239051 }).get_by_id
        expect(entity_response.dig('id')).to eq(239051)
      end
    end

    context 'when deal entity_id is blank' do
      it 'returns empty data' do
        expect(EntityService.new({ entity_type: 'deal' }).get_by_id).to eq({})
      end
    end
  end
end
