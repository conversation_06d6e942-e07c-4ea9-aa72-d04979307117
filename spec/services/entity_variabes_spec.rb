# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EntityVariables do
  let(:user) { create(:user) }
  let(:valid_auth_token) { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data) { User::TokenParser.parse(invalid_auth_token.token) }

  describe '#get' do
    before do
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
      stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
        status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
      )
    end

    context 'with valid input for lead' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
      end

      it 'returns fields' do
        results = EntityVariables.new('lead').get
        expect(results.map { |var| var['entity'] }.uniq).to match_array(['tenant', 'createdBy', 'updatedBy', 'convertedBy', 'importedBy', 'ownerId', 'lead'])
      end
    end

    context 'with valid input for deal' do
      before do
        stub_request(:get, 'http://localhost:8090/v1/deals/fields')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/deal-fields-response.json').read)
      end

      it 'returns fields' do
        results = EntityVariables.new('deal').get
        expect(results.map { |var| var['entity'] }.uniq).to match_array(['createdBy', 'deal', 'ownedBy', 'tenant', 'updatedBy', 'importedBy'])
      end
    end

    context 'with valid input for contact' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/contact/fields?entityType=contact&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/contact-fields-response.json').read)
      end

      it 'returns fields' do
        results = EntityVariables.new('contact').get
        expect(results.map { |var| var['entity'] }.uniq).to match_array(['contact', 'createdBy', 'importedBy', 'ownerId', 'tenant', 'updatedBy'])
      end
    end

    context 'with invalid input' do
      it 'raises error' do
        expect { EntityVariables.new('invalid').get }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid Entity')
      end
    end
  end
end
