# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::AuthCode do
  let(:incorrect_code_error_payload) do
    {
      "error": {
        "message": 'This authorization code has expired.',
        "type": 'OAuthException',
        "code": 100,
        "error_subcode": 36_007,
        "fbtrace_id": 'A4NItWtMw2FflXBSo4Ress8'
      }
    }
  end

  let(:valid_output) do
    {
      "access_token" => 'EAANNzXrhnuwBOZBYasddwqwdEEJJwdqwdNSqwdqwdJDasd',
      "token_type" => 'bearer'
    }
  end

  describe '#exchange' do
    context 'when incorrect code is passed' do
      it 'returns appropriate exception' do
        stub_request(:get, "#{FACEBOOK_HOST}/oauth/access_token?client_id=#{ENV['FACEBOOK_CLIENT_ID']}&client_secret=#{ENV['FACEBOOK_CLIENT_SECRET']}&code=demo_exchange_code")
          .to_return(status: 400, body: incorrect_code_error_payload.to_json, headers: {})
        expect { described_class.new('demo_exchange_code').exchange }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||#{incorrect_code_error_payload.dig(:error, :message)}")
      end
    end

    context 'when correct code is passed' do
      it 'returns appropriate access token' do
        stub_request(:get, "#{FACEBOOK_HOST}/oauth/access_token?client_id=#{ENV['FACEBOOK_CLIENT_ID']}&client_secret=#{ENV['FACEBOOK_CLIENT_SECRET']}&code=demo_exchange_code")
          .to_return(status: 200, body: valid_output.to_json, headers: {})
        response = described_class.new('demo_exchange_code').exchange
        expect(response.class).to eq(Facebook::Response)
        expect(response.body).to eq(valid_output)
      end
    end
  end
end
