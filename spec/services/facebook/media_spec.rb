# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::Media do
  let(:connected_account) { create(:connected_account) }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }

  describe '#upload' do
    context 'when file is passed and request is successful' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})
      end

      it 'returns media id' do
        response = Facebook::Media.new(connected_account).upload(sample_png_3mb)
        expect(response.body).to eq({"id"=>"<MEDIA_ID>"})
      end
    end
  end
end
