# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::Message do
  let(:connected_account) { create(:connected_account) }
  let(:phone_number) { '+************' }

  describe '#send_session_message' do
    context 'when text message' do
      let(:message_payload) do
        {
          "preview_url": true,
          "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
        }
      end

      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: phone_number,
              type: 'text',
              text: {
                "preview_url": true,
                "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
      end

      it 'returns message id in response' do
        message_response = described_class.new(connected_account, phone_number).send_session_message('text', message_payload)

        expect(message_response.status_code).to eq('200')
        expect(message_response.body).to eq({
          "messaging_product" => "whatsapp",
          "contacts" => [
            {
              "input" => "48XXXXXXXXX",
              "wa_id" => "48XXXXXXXXX"
            }
          ],
          "messages" => [
            {
              "id" => "wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww"
            }
          ]
        })
      end
    end

    context 'when error while sending message' do
      let(:message_payload) do
        {
          "preview_url": true,
          "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
        }
      end

      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: phone_number,
              type: 'text',
              text: {
                "preview_url": true,
                "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
              }
            }.to_json
          ).to_return(status: 400, body: file_fixture('facebook/message/session-message-failure-response.json'))
      end

      it 'raises error' do
        expect {
          described_class.new(connected_account, phone_number).send_session_message('text', message_payload)
        }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||(#131006) Resource not found')
      end
    end

    context 'when connected account is blank' do
      let(:message_payload) do
        {
          "preview_url": true,
          "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
        }
      end

      it 'raises error' do
        expect {
          described_class.new(nil, phone_number).send_session_message('text', message_payload)
        }.to raise_error(ExceptionHandler::AccountNotConnectedError, '022018||Whatsapp Business Number not connected')
      end
    end
  end
end
