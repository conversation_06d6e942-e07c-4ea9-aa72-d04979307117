# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::PhoneNumber do
  let(:incorrect_access_token_error_payload) do
    JSON.parse({
      "error": {
        "message": 'Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F',
        "type": 'OAuthException',
        "code": 190,
        "fbtrace_id": 'AyJMTl1Uz3Ovcdy3BSJ1a4V'
      }
    }.to_json)
  end

  let(:incorrect_phone_number_error_payload) do
    JSON.parse({
      "error": {
        "message": "Unsupported get request. Object with ID '****************' does not exist, cannot be loaded due to missing permissions, or does not support this operation. Please read the Graph API documentation at https://developers.facebook.com/docs/graph-api",
        "type": 'GraphMethodException',
        "code": 100,
        "error_subcode": 33,
        "fbtrace_id": 'AJybo1nhpnWd0H5LkTYK9V5'
      }
    }.to_json)
  end

  let(:valid_output) do
    JSON.parse({
      "verified_name": '<PERSON><PERSON><PERSON>',
      "code_verification_status": 'VERIFIED',
      "display_phone_number": '+91 70302 40148',
      "quality_rating": 'UNKNOWN',
      "platform_type": 'NOT_APPLICABLE',
      "throughput": {
        "level": 'NOT_APPLICABLE'
      },
      "last_onboarded_time": '2024-02-28T13:03:52+0000',
      "id": '***************'
    }.to_json)
  end

  describe '#find' do
    context 'when no connected account is passed - ' do
      it 'returns appropriate exception' do
        expect { described_class.new(nil).find }.to raise_error(ExceptionHandler::AccountNotConnectedError, "022018||Whatsapp Business Number not connected")
      end
    end

    context 'when connected account is present' do
      context 'when incorrect access token is passed' do
        it 'returns appropriate exception' do
          connected_account = create(:connected_account)
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}")
            .with(
              headers: {
                'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 401, body: incorrect_access_token_error_payload.to_json, headers: {})
          expect { described_class.new(connected_account).find }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||#{incorrect_access_token_error_payload.dig('error', 'message')}")
        end
      end

      context 'when incorrect phone number id is passed' do
        it 'returns appropriate exception' do
          connected_account = create(:connected_account, phone_number_id: '****************')
          stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}")
            .with(
              headers: {
                'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 400, body: incorrect_phone_number_error_payload.to_json, headers: {})
          expect { described_class.new(connected_account).find }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||#{incorrect_phone_number_error_payload.dig('error', 'message')}")
        end
      end
    end

    context 'with valid input' do
      it 'returns appropriate phone number information' do
        connected_account = create(:connected_account, phone_number_id: '****************')
        stub_request(:get, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}")
          .with(
            headers: {
              'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            }
          )
          .to_return(status: 200, body: valid_output.to_json, headers: {})

        response = described_class.new(connected_account).find
        expect(response.body).to eq(valid_output)
      end
    end
  end

  describe '#enable2FA' do
    context 'when no connected account is passed - ' do
      it 'returns appropriate exception' do
        expect { described_class.new(nil).find }.to raise_error(ExceptionHandler::AccountNotConnectedError, "022018||Whatsapp Business Number not connected")
      end
    end

    context 'when connected account is present' do
      context 'when incorrect access token is passed' do
        it 'returns appropriate exception' do
          connected_account = create(:connected_account)
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}")
            .with(
              headers: {
                'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 401, body: incorrect_access_token_error_payload.to_json, headers: {})
          expect { described_class.new(connected_account).enable2FA('123456') }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||#{incorrect_access_token_error_payload.dig('error', 'message')}")
        end
      end

      context 'when incorrect phone number id is passed' do
        it 'returns appropriate exception' do
          connected_account = create(:connected_account, phone_number_id: '****************')
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}")
            .with(
              headers: {
                'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 400, body: incorrect_phone_number_error_payload.to_json, headers: {})
          expect { described_class.new(connected_account).enable2FA('123456') }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||#{incorrect_phone_number_error_payload.dig('error', 'message')}")
        end
      end
    end

    context 'with valid input' do
      let(:valid_output) do
        {
          "success" => true
        }
      end
      it 'returns appropriate success message' do
        connected_account = create(:connected_account, phone_number_id: '****************')
        stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}")
          .with(
            headers: {
              'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            }
          )
          .to_return(status: 200, body: valid_output.to_json, headers: {})

        response = described_class.new(connected_account).enable2FA('123456')
        expect(response.body).to eq({ "success" => true })
      end
    end
  end

  describe '#register' do
    context 'with valid input' do
      let(:valid_output) do
        {
          "success" => true
        }
      end

      it 'returns appropriate success message' do
        connected_account = create(:connected_account, phone_number_id: '****************')
        stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}/register")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            }
          )
          .to_return(status: 200, body: valid_output.to_json, headers: {})

        response = described_class.new(connected_account).register('123456')
        expect(response.body).to eq({ "success" => true })
      end
    end

    context 'when no connected account is passed - ' do
      it 'returns appropriate exception' do
        expect { described_class.new(nil).find }.to raise_error(ExceptionHandler::AccountNotConnectedError, "022018||Whatsapp Business Number not connected")
      end
    end

    context 'when connected account is present' do
      context 'when incorrect access token is passed' do
        it 'returns appropriate exception' do
          connected_account = create(:connected_account)
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}/register")
            .with(
              headers: {
                'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 401, body: incorrect_access_token_error_payload.to_json, headers: {})
          expect { described_class.new(connected_account).register('123456') }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||#{incorrect_access_token_error_payload.dig('error', 'message')}")
        end
      end

      context 'when incorrect phone number id is passed' do
        it 'returns appropriate exception' do
          connected_account = create(:connected_account, phone_number_id: '****************')
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}/register")
            .with(
              headers: {
                'Authorization' => "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 400, body: incorrect_phone_number_error_payload.to_json)
          expect { described_class.new(connected_account).register('123456') }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||Unsupported get request. Object with ID '****************' does not exist, cannot be loaded due to missing permissions, or does not support this operation. Please read the Graph API documentation at https://developers.facebook.com/docs/graph-api")
        end
      end
    end

    context 'when facebook error'do
      let(:connected_account) { create(:connected_account) }

      context 'when phone number needs reverification' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/register")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: {
                messaging_product: 'whatsapp',
                pin: '123456'
              }
            )
            .to_return(status: 400, body: file_fixture('facebook/phone_number/phone-number-reverification-error.json').read)
        end

        it 'raises error and marks connected account verified false' do
          expect { described_class.new(connected_account).register('123456') }
            .to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||(#133006) Phone number re-verification needed")
          expect(connected_account.reload.is_verified).to be_falsey
        end
      end
    end
  end

  describe '#deregister' do
    let(:connected_account) { create(:connected_account) }

    context 'when connected account is present' do
      context 'when phone number id is present and valid' do
        before do
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}/deregister")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            }
          )
          .to_return(status: 200, body: { success: true }.to_json, headers: {})
        end

        it 'deregisters phone number' do
          response = described_class.new(connected_account).deregister
          expect(response.body).to eq({ "success" => true })
        end
      end

      context 'when invalid phone number id' do
        before do
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}/deregister")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 400, body: incorrect_phone_number_error_payload.to_json, headers: {})
        end

        it 'raises third party error' do
          expect { described_class.new(connected_account).deregister }
            .to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||Unsupported get request. Object with ID '****************' does not exist, cannot be loaded due to missing permissions, or does not support this operation. Please read the Graph API documentation at https://developers.facebook.com/docs/graph-api")
        end
      end

      context 'when invalid token' do
        before do
          stub_request(:post, "#{FACEBOOK_HOST}/#{FACEBOOK_API_VERSION}/#{connected_account.phone_number_id}/deregister")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              }
            )
            .to_return(status: 401, body: incorrect_access_token_error_payload.to_json, headers: {})
        end

        it 'raises third party error' do
          expect { described_class.new(connected_account).deregister }
            .to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F")
        end
      end
    end

    context 'when connected account is not present' do
      it 'returns appropriate exception' do
        expect { described_class.new(nil).deregister }.to raise_error(ExceptionHandler::AccountNotConnectedError, "022018||Whatsapp Business Number not connected")
      end
    end
  end

  describe '#request_code' do
    let(:connected_account) { create(:connected_account) }
    let(:code_method) { 'SMS' }
    let(:language) { 'en' }

    context 'when connected account is present' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/request_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code_method: code_method,
              language: language
            }
          )
          .to_return(status: 200, body: { success: true }.to_json)
      end

      it 'returns success true' do
        code_response = described_class.new(connected_account).request_code(code_method, language)

        expect(code_response.status_code).to eq('200')
        expect(code_response.body).to eq({ 'success' => true })
      end
    end

    context 'when connected is not present' do
      it 'raises error' do
        expect { described_class.new(nil).request_code(code_method, language) }
          .to raise_error(ExceptionHandler::AccountNotConnectedError, '022018||Whatsapp Business Number not connected')
      end
    end

    context 'when facebook returns error' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/request_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code_method:code_method,
              language: language
            }
          )
          .to_return(status: 401, body: incorrect_access_token_error_payload.to_json)
      end

      it 'raises error' do
        expect { described_class.new(connected_account).request_code(code_method, language) }
          .to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F')
      end
    end
  end

  describe '#verify_code' do
    let(:connected_account) { create(:connected_account) }
    let(:otp_code) { 123456 }

    context 'when connected account is present' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code: otp_code
            }
          )
          .to_return(status: 200, body: { success: true }.to_json)
      end

      it 'returns success true' do
        code_response = described_class.new(connected_account).verify_code(otp_code)

        expect(code_response.status_code).to eq('200')
        expect(code_response.body).to eq({ 'success' => true })
      end
    end

    context 'when connected is not present' do
      it 'raises error' do
        expect { described_class.new(nil).verify_code(otp_code) }
          .to raise_error(ExceptionHandler::AccountNotConnectedError, '022018||Whatsapp Business Number not connected')
      end
    end

    context 'when facebook returns error' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/verify_code")
          .with(
            headers: {
              Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
            },
            body: {
              code: otp_code
            }
          )
          .to_return(status: 400, body: file_fixture('facebook/phone_number/verify-code-error.json').read)
      end

      it 'raises error' do
        expect { described_class.new(connected_account).verify_code(otp_code) }
          .to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Verify code error. Code couldn\'t be verified, You have already verified ownership of this phone number.')
      end
    end
  end
end
