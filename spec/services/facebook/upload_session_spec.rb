# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::UploadSession do
  let(:connected_account) { create(:connected_account) }

  describe '#start' do
    context 'when requested to start session with payload' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{ENV['FACEBOOK_CLIENT_ID']}/uploads?file_length=3124201&file_name=sample_png_3mb.png&file_type=image/png").with(
          headers: {
            Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/upload_template_media/upload-session-start-response.json').read)
      end

      it 'returns session id as response' do
        response = described_class.new({ file_name: 'sample_png_3mb.png', file_size: 31_24_201, file_type: 'image/png' }, connected_account).start
        expect(response.body.keys).to eq(['id'])
        expect(response.status_code).to eq('200')
      end
    end

    context 'when incorrect token is passed' do
      before do
        stub_request(:post, "https://graph.facebook.com/v19.0/#{ENV['FACEBOOK_CLIENT_ID']}/uploads?file_length=3124201&file_name=sample_png_3mb.png&file_type=image/png").with(
          headers: {
            Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 401, body: {
          "error": {
            "message": 'Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F',
            "type": 'OAuthException',
            "code": 190,
            "fbtrace_id": 'AyJMTl1Uz3Ovcdy3BSJ1a4V'
          }
        }.to_json)
      end

      it 'returns 401 response with appropriate message' do
        expect {
          described_class.new({ file_name: 'sample_png_3mb.png', file_size: 31_24_201, file_type: 'image/png' }, connected_account).start
        }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F')
      end
    end
  end
end
