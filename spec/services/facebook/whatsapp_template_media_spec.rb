# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::WhatsappTemplateMedia do
  let(:connected_account) { create(:connected_account) }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }

  describe '#start_upload' do
    context 'when requested to upload media' do
      before do
        allow(File).to receive(:open).with(sample_png_3mb.path, 'rb').and_return(StringIO.new('File Content'))
        stub_request(:post, 'https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZ==asdijk').with(
          headers: {
            Authorization: "OAuth #{ConnectedAccount.decrypt(connected_account.access_token)}"
          }
        ).to_return(status: 200, body: file_fixture('facebook/upload_template_media/template-media-upload-response.json').read)
      end

      it 'uploads and returns file handle' do
        response = described_class.new(sample_png_3mb.path, 'image/png', 'upload:MTphdHRhY2htZ==asdijk', connected_account).start_upload
        expect(response.body['h']).to eq('4:U2FtcGxlUE5HSW1hZ2U=:aW1hZ2UvcG5n:ARalfyNs-W_g8DH3iRWFn43YeawePd0Q0AoJpTjvP4GaLT6buwavxAscryMY6OIpK0HQ2GGybB-jv4n5-YZ2HlncEynNqLEbGE8efcsmdWkoJQ:e:**********:***************:**************')
      end
    end

    context 'when incorrect token is passed' do
      before do
        allow(File).to receive(:open).with(sample_png_3mb.path, 'rb').and_return(StringIO.new('File Content'))
        stub_request(:post, 'https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZ==asdijk').with(
          headers: {
            Authorization: "OAuth #{ConnectedAccount.decrypt(connected_account.access_token)}"
          }
        ).to_return(status: 401, body: {
          "error": {
            "message": 'Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F',
            "type": 'OAuthException',
            "code": 190,
            "fbtrace_id": 'AyJMTl1Uz3Ovcdy3BSJ1a4V'
          }
        }.to_json)
      end

      it 'returns 401 response with appropriate message' do
        expect {
          described_class.new(sample_png_3mb.path, 'image/png', 'upload:MTphdHRhY2htZ==asdijk', connected_account).start_upload
        }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Malformed access token AQAqSdyJxogQowbHwbWbybYJDrgTa146y8F')
      end
    end
  end
end
