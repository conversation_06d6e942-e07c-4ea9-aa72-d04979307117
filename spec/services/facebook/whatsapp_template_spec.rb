# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Facebook::WhatsappTemplate do
  describe '#save' do
    let(:connected_account) { create(:connected_account) }
    let(:expected_facebook_payload) do
      {
        name: whatsapp_template.whatsapp_template_namespace,
        language: whatsapp_template.language,
        category: whatsapp_template.category,
        components: [
          {
            type: 'HEADER',
            format: 'TEXT',
            text: 'Our {{1}} is on!',
            example: { 'header_text': ['a'] }
          },
          {
            type: 'BODY',
            text: 'Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.',
            example: { 'body_text': [['a', 'a', 'a']] }
          },
          {
            type: 'FOOTER',
            text: 'Use the buttons below to manage your marketing subscriptions'
          },
          {
            type: 'BUTTONS',
            buttons: [
              {
                type: 'PHONE_NUMBER',
                text: 'Call Us',
                phone_number: '+************'
              },
              {
                type: 'COPY_CODE',
                example: '250FF'
              },
              {
                type: 'URL',
                text: 'Shop Now',
                url: 'https://www.kylas.io?referral={{1}}',
                example: ['https://www.kylas.io?referral=a']
              },
              {
                type: 'QUICK_REPLY',
                text: 'Unsubcribe from Promos'
              }
            ]
          }
        ]
      }.to_json
    end

    context 'creating template' do
      let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account) }

      context 'valid' do
        context 'when one url button' do
          before do
            stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
              .with(
                headers: {
                  Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
                },
                body: expected_facebook_payload
              )
              .to_return(status: 200, body: file_fixture('facebook/whatsapp_template/create-template-success-response.json').read)
          end

          it 'returns whatsapp template updated with id and status' do
            updated_whatsapp_template = described_class.new(connected_account).save(whatsapp_template)

            expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
            expect(updated_whatsapp_template.status).to eq('PENDING')
            expect(updated_whatsapp_template.category).to eq('MARKETING')
            expect(updated_whatsapp_template.whatsapp_template_id).to eq('***************')
            expect(updated_whatsapp_template.reason).to be_nil
            expect(updated_whatsapp_template.additional_info).to be_nil
          end
        end

        context 'when two url buttons' do
          before do
            create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.kylas.io?referral={{2}}', position: 0)
            parsed_payload = JSON.parse(expected_facebook_payload)
            button_components = parsed_payload['components'].find { |component| component['type'] == 'BUTTONS' }
            button_components['buttons'].unshift(
              {
                type: 'URL',
                text: 'Shop Now',
                url: 'https://www.kylas.io?referral={{1}}',
                example: ['https://www.kylas.io?referral=a']
              }
            )
            stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
              .with(
                headers: {
                  Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
                },
                body: parsed_payload.to_json
              )
              .to_return(status: 200, body: file_fixture('facebook/whatsapp_template/create-template-success-response.json').read)
          end

          it 'returns whatsapp template updated with id and status' do
            updated_whatsapp_template = described_class.new(connected_account).save(whatsapp_template)

            expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
            expect(updated_whatsapp_template.status).to eq('PENDING')
            expect(updated_whatsapp_template.category).to eq('MARKETING')
            expect(updated_whatsapp_template.whatsapp_template_id).to eq('***************')
            expect(updated_whatsapp_template.reason).to be_nil
            expect(updated_whatsapp_template.additional_info).to be_nil
          end
        end
      end

      context 'invalid' do
        context 'when invalid url button' do
          before do
            whatsapp_template.components.find { |comp| comp.component_format == URL }.update_column(:content, nil)
            parsed_payload = JSON.parse(expected_facebook_payload)
            url_component = parsed_payload['components'].find { |comp| comp['type'] == 'BUTTONS' }['buttons'].find { |comp| comp['type'] == URL }
            url_component.delete('example')
            stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.waba_id}/message_templates")
              .with(
                headers: {
                  Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
                },
                body: parsed_payload.to_json
              )
              .to_return(status: 400, body: file_fixture('facebook/whatsapp_template/invalid-template-response.json').read)
          end

          it 'raises error' do
            expect(Rails.logger).to receive(:error).with("Error in Facebook Request - 400 - #{JSON.parse(file_fixture('facebook/whatsapp_template/invalid-template-response.json').read).inspect}"
            )
            expect { described_class.new(connected_account).save(whatsapp_template) }
              .to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Invalid parameter. Message template "components" param is missing expected field(s), component of type BUTTONS is missing expected field(s) (example)')
          end
        end
      end
    end

    context 'editing template' do
      let(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, whatsapp_template_id: '***************') }

      context 'valid' do
        before do
          stub_request(:post, "https://graph.facebook.com/v19.0/***************")
            .with(
              headers: {
                Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
              },
              body: expected_facebook_payload
            )
            .to_return(status: 200, body: file_fixture('facebook/whatsapp_template/update-template-success-response.json').read)
        end

        it 'returns whatsapp template updated with id and status' do
          updated_whatsapp_template = described_class.new(connected_account).save(whatsapp_template)

          expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
          expect(updated_whatsapp_template.changes).to eq({ "status" => %w[DRAFT PENDING] })
        end

        context 'updating rejected template' do
          before { whatsapp_template.update(status: REJECTED, reason: 'INVALID_FORMAT') }

          it 'changes status to pending and resets reason and additional info' do
            updated_whatsapp_template = described_class.new(connected_account).save(whatsapp_template)

            expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
            expect(updated_whatsapp_template.changes).to eq({ "status" => %w[REJECTED PENDING], "reason" => ['INVALID_FORMAT', nil] })
          end
        end
      end

      context 'invalid' do
        context 'when invalid url button' do
          before do
            stub_request(:post, "https://graph.facebook.com/v19.0/***************")
              .with(
                headers: {
                  Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
                },
                body: expected_facebook_payload
              )
              .to_return(status: 400, body: file_fixture('facebook/whatsapp_template/cannot-edit-template-response.json').read)
          end

          it 'raises error' do
            expect(Rails.logger).to receive(:error).with("Error in Facebook Request - 400 - #{JSON.parse(file_fixture('facebook/whatsapp_template/cannot-edit-template-response.json').read).inspect}"
            )
            expect { described_class.new(connected_account).save(whatsapp_template) }
              .to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Invalid parameter. Message template can\'t be edited, You can only edit an active template once in 24 hours.')
          end
        end
      end
    end
  end
end
