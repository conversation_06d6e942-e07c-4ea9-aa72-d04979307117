# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Fields::Contact do
  let(:user)  { create(:user) }
  let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name).token }

  describe '#fetch' do
    context 'when token is present' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/contact/fields?entityType=contact&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{token}"
            }
          )
          .to_return(status: 200, body: file_fixture('fields/contact-fields-response.json').read)
      end

      it 'returns fields' do
        fields_response = described_class.new(token).fetch

        expect(fields_response.count).to eq(49)
        expect(fields_response.map { |fr| fr['name'] }).to match_array(%w[salutation firstName lastName phoneNumbers emails dnd timezone address city state zipcode country facebook twitter linkedin company department designation stakeholder ownerId createdAt updatedAt createdBy updatedBy customTextField customParagraph customNumber customPicklist customCheckbox customDate customDateTime createdViaId createdViaName createdViaType updatedViaId updatedViaName updatedViaType cfCustomUrl campaign source subSource utmSource utmCampaign utmMedium utmContent utmTerm id score importedBy])
      end
    end

    context 'when fields api returns error' do
      context 'when api returns blank response' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/contact/fields?entityType=contact&custom-only=false')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 200, body: '')
        end

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for contact - invalid response')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when api returns 400' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/contact/fields?entityType=contact&custom-only=false')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 400, body: '')
        end

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for contact - 400')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when api returns 404' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/contact/fields?entityType=contact&custom-only=false')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 404, body: '')
        end

        it 'raises internal error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for contact - 404')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when api returns 500' do
        before do
          stub_request(:get, 'http://localhost:8086/v1/entities/contact/fields?entityType=contact&custom-only=false')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 500, body: '')
        end

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for contact - 500')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InternalServerError, '022004')
        end
      end
    end

    context 'when token is absent' do
      it 'raises authentication error' do
        expect { described_class.new(nil).fetch }.to raise_error(ExceptionHandler::AuthenticationError, '022002')
      end
    end
  end
end
