# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Fields::Deal do
  let(:user)  { create(:user) }
  let(:token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name).token }

  describe '#fetch' do
    context 'when token is present' do
      before do
        stub_request(:get, 'http://localhost:8090/v1/deals/fields')
          .with(
            headers: {
              Authorization: "Bearer #{token}"
            }
          )
          .to_return(status: 200, body: file_fixture('fields/deal-fields-response.json').read)
      end

      it 'returns fields' do
        fields_response = described_class.new(token).fetch

        expect(fields_response.count).to eq(29)
      end
    end

    context 'when fields api returns error' do
      context 'when api returns blank response' do
        before do
          stub_request(:get, 'http://localhost:8090/v1/deals/fields')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 200, body: '')
        end

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with('<PERSON> Error while fetching fields for deal - invalid response')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when api returns 400' do
        before do
          stub_request(:get, 'http://localhost:8090/v1/deals/fields')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 400, body: '')
        end

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for deal - 400')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when api returns 404' do
        before do
          stub_request(:get, 'http://localhost:8090/v1/deals/fields')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 404, body: '')
        end

        it 'raises internal error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for deal - 404')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when api returns 500' do
        before do
          stub_request(:get, 'http://localhost:8090/v1/deals/fields')
            .with(
              headers: {
                Authorization: "Bearer #{token}"
              }
            )
            .to_return(status: 500, body: '')
        end

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with('Fields Error while fetching fields for deal - 500')
          expect { described_class.new(token).fetch }.to raise_error(ExceptionHandler::InternalServerError, '022004')
        end
      end
    end

    context 'when token is absent' do
      it 'raises authentication error' do
        expect { described_class.new(nil).fetch }.to raise_error(ExceptionHandler::AuthenticationError, '022002')
      end
    end
  end
end
