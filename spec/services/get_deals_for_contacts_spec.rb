# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetDealsForContacts do
  let(:user) { create(:user) }
  let(:contact_ids) { [1, 2, 3] }
  let(:service) { described_class.new(contact_ids) }

  describe '#call' do
    before do
      Thread.current[:user] = user
    end

    context 'when contact_ids are present' do
      let!(:contact_deal_association1) { create(:contact_deal_association, contact_id: 1, deal_id: 101, deal_name: 'Deal 1', tenant_id: user.tenant_id) }
      let!(:contact_deal_association2) { create(:contact_deal_association, contact_id: 1, deal_id: 102, deal_name: 'Deal 2', tenant_id: user.tenant_id) }
      let!(:contact_deal_association3) { create(:contact_deal_association, contact_id: 2, deal_id: 201, deal_name: 'Deal 3', tenant_id: user.tenant_id) }

      it 'returns deals grouped by contact_id' do
        result = service.call

        expect(result).to eq({
          1 => [
            { id: 102, name: 'Deal 2' },
            { id: 101, name: 'Deal 1' }
          ],
          2 => [
            { id: 201, name: 'Deal 3' }
          ]
        })
      end

      it 'does not include deals for contacts not in the list' do
        create(:contact_deal_association, contact_id: 999, deal_id: 999, deal_name: 'Unrelated Deal', tenant_id: user.tenant_id)
        
        result = service.call

        expect(result.keys).to match_array([1, 2])
      end
    end

    context 'when contact_ids is blank' do
      let(:contact_ids) { [] }

      it 'returns empty hash' do
        result = service.call
        expect(result).to eq({})
      end
    end

    context 'when no deals found for contacts' do
      it 'returns empty hash' do
        result = service.call
        expect(result).to eq({})
      end
    end
  end
end 