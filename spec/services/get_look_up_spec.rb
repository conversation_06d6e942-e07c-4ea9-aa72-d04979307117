require 'rails_helper'

RSpec.describe GetLookUp do
  describe '#call' do
    let(:user){ User.create(id: 1, tenant_id: 99, name: '<PERSON>')}

    before do
      Thread.current[:user] = user
    end

    context 'valid input' do
      context 'for existing entity' do
        before do
          @look_up = create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name,
            phone_number: @look_up.phone_number
          }
        end

        it 'returns the existing look_up' do
          expect(
            GetLookUp.call(@input)
          ).to be_eql(@look_up)
        end

        it 'does not create a new look_up' do
          expect {
            GetLookUp.call(@input)
          }.not_to change { LookUp.count }
        end
      end

      context 'for new entity' do
        before do
          @look_up = build(:look_up, entity_type: LOOKUP_LEAD)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name
          }
        end

        it 'returns a look_up object' do
          look_up = GetLookUp.call(@input)
          expect(
            look_up
          ).to be_truthy
          expect(
            look_up.class
          ) == LookUp
        end

        it 'does not create a new look_up object' do
          expect {
            GetLookUp.call(@input)
          }.not_to change { LookUp.count }
        end
      end

    end
    context 'invalid input' do
      it 'raises error for any missing id' do
        expect{
          GetLookUp.call(
            {
              id:"",
              tenant_id: 1,
              entity:"lead",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
      it 'raises error for any missing tenant_id' do
        expect{
          GetLookUp.call(
            {
              id:"1",
              entity:"",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
      it 'raises error for any missing entity' do
        expect{
          GetLookUp.call(
            {
              id:"1",
              entity:"",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
      # TODO: fix me
      it 'raises error for any missing name' do
        pending("Need to fix this test case")
        expect{
          GetLookUp.call(
            {
              id:"1",
              entity:"lead",
              "name":""
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
    end
  end
end
