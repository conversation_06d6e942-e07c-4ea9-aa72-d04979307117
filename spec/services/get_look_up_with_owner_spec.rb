require 'rails_helper'

RSpec.describe GetLookUpWithOwner do
  describe '#call' do
    let(:user){ User.create(id: 1, tenant_id: 99, name: '<PERSON>')}
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    before do
      Thread.current[:user] = user
    end

    context 'valid input' do
      context 'for existing entity' do
        before do
          @look_up = create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name,
            phone_number: @look_up.phone_number
          }
        end

        it 'returns the existing look_up with owner' do
          expect(
            GetLookUpWithOwner.call(@input)
          ).to be_eql(@look_up)
        end

        it 'does not create a new look_up' do
          expect {
            GetLookUpWithOwner.call(@input)
          }.not_to change { LookUp.count }
        end
      end

      context 'for new entity' do
        before do
          @look_up = build(:look_up, entity_type: LOOKUP_LEAD)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name
          }

          allow(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

          stub_request(:get, SERVICE_SALES + "/v1/leads/#{@look_up.entity_id}").
          with(
            headers: {
            "Authorization" => "Bearer #{ valid_auth_token.token }"
            }).
          to_return(status: 200, body: { "id": 1234, "ownerId": 1, "recordActions": { "read": true, "update": true, "delete": true, "email": true, "call": false, "sms": false, "task": true, "note": true, "meeting": true }}.to_json, headers: {})
        end

        it 'returns a look_up object with owner' do
          look_up = GetLookUpWithOwner.call(@input)
          expect(
            look_up
          ).to be_truthy
          expect(
            look_up.class
          ) == LookUp
        end

        it 'does creates a new look_up object' do
          expect {
            GetLookUpWithOwner.call(@input)
          }.to change { LookUp.count }.by(1)
        end
      end

      context 'when owner_id is missing' do
        before do
          @look_up = create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: nil)
          @input = {
            id: @look_up.entity_id,
            tenant_id: @look_up.tenant_id,
            entity: @look_up.entity_type,
            name: @look_up.name
          }
          allow(GetLead).to receive(:call).and_return({ 'ownerId' => 123 })
        end

        it 'calls the API to get owner_id and stores it' do
          look_up = GetLookUpWithOwner.call(@input)
          expect(look_up.owner_id).to eq(123)
          expect(GetLead).to have_received(:call).with(@look_up.entity_id, anything)
        end
      end

    end
    context 'invalid input' do
      it 'raises error for any missing id' do
        expect{
          GetLookUpWithOwner.call(
            {
              id:"",
              tenant_id: 1,
              entity:"lead",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
      it 'raises error for any missing tenant_id' do
        expect{
          GetLookUpWithOwner.call(
            {
              id:"1",
              entity:"",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
      it 'raises error for any missing entity' do
        expect{
          GetLookUpWithOwner.call(
            {
              id:"1",
              entity:"",
              "name":"test"
            }
          )
        }.to raise_error(
          ExceptionHandler::InvalidDataError,
          ErrorCode.invalid_data
        )
      end
    end
  end
end 