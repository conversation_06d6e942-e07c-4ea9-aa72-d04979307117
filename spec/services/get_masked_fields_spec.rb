# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetMaskedFields do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = User::TokenParser.parse(@token)
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'with valid input - ' do
      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ @token }"
          }
        ).to_return(
          status: 200,
          body: [
            {
              type: 'PHONE',
              name: 'phoneNumbers',
              description: 'Lead Phone',
              filterable: true,
              sortable: true,
              required: false,
              important: true,
              pickLists: nil,
              fieldConfigurations: [
                  {
                    id: nil,
                    type:'MASKING',
                    configuration: {
                      enabled:true,
                      profileIds: [1,2,3]
                    }
                  }
                ]
            }
          ].to_json,
          headers: {}
        )
      end

      it 'returns fields that are masked' do
        result = GetMaskedFields.new(LOOKUP_LEAD).call
        expect(result.count).to eq(1)
        expect(result[0]['type']).to eq('PHONE')
      end
    end

    context 'when token is not present in thread' do
      before do
        Thread.current[:token] = nil
      end

      it 'raises unauthorized error' do
        expect { GetMaskedFields.new(LOOKUP_LEAD).call }.to raise_error(ExceptionHandler::AuthenticationError, '022002')
      end
    end

    context 'when fields are not availale' do
      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ @token }"
          }
        ).to_return(
          status: 404,
          body: {}.to_json,
          headers: {}
        )
      end

      it 'raises invalid data error' do
        expect { GetMaskedFields.new(LOOKUP_LEAD).call }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
      end
    end

    context 'when something goes wrong' do
      before do
        stub_request(:get, "#{SERVICE_CONFIG}/v1/entities/lead/masked-fields").with(
          headers: {
            'Authorization' => "Bearer #{ @token }"
          }
        ).to_return(
          status: 500,
          body: {}.to_json,
          headers: {}
        )
      end

      it 'raises invalid data error' do
        expect { GetMaskedFields.new(LOOKUP_LEAD).call }.to raise_error(ExceptionHandler::InternalServerError, '022004')
      end
    end
  end
end
