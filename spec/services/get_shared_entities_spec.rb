# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetSharedEntities, type: :service do
  describe '#call' do
    let(:user)                          { create(:user) }
    let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data)                     { User::TokenParser.parse(valid_auth_token.token) }

    let(:sample_response) {
      {
        "accessByOwners": {},
        "accessByRecords": {
          "246547": {
            "read": true,
            "write": false,
            "update": false,
            "delete": false,
            "email": false,
            "call": true,
            "sms": false,
            "task": false,
            "note": false,
            "meeting": false,
            "readAll": false,
            "updateAll": false,
            "deleteAll": false,
            "quotation": false
          }
        }
      }
    }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
      @entity = LOOKUP_LEAD
      @action = 'read'
    end

    context 'invalid request' do
      context 'when invalid entity' do
        it 'should return blank hash' do
          expect(GetSharedEntities.new('company', @action).call).to eq({})
        end
      end

      context 'when invalid permission' do
        before do
          stub_request(:get, SERVICE_CONFIG + "/v1/internal/share/access/#{@entity.upcase}/#{'invalid'.upcase}").
            with(
              headers: {
              "Authorization" => "Bearer #{valid_auth_token.token}"
            }).
            to_return(status: 400, headers: {})
        end

        it 'should return 400 BAD REQUEST' do
          expect{ GetSharedEntities.new(@entity, 'invalid').call }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
        end
      end

      context 'when invalid token' do
        before { Thread.current[:token] = nil }

        it 'should raise unauthorised error' do
          expect{ GetSharedEntities.new(@entity, @action).call }.to raise_error(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized)
        end

        after { Thread.current[:token] = valid_auth_token }
      end
    end

    context 'valid request' do
      before do
        stub_request(:get, SERVICE_CONFIG + "/v1/internal/share/access/#{@entity.upcase}/#{@action.upcase}").
          with(
            headers: {
            "Authorization" => "Bearer #{valid_auth_token.token}"
          }).
          to_return(status: 200, body: sample_response.to_json, headers: {})
      end

      it 'should return json response' do
        expect(GetSharedEntities.new(@entity, @action).call).to eq(sample_response.with_indifferent_access)
      end
    end
  end
end
