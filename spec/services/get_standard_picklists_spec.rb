# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetStandardPicklists do
  describe '#call' do
    before do
      token = build(:auth_token)
      Thread.current[:token] = token
      stub_request(:get, SERVICE_CONFIG + "/v1/picklists/standard").
        with(
          headers: {
            "Authorization" => "Bearer #{token}"
          }).
        to_return(status: 200, body: file_fixture('standard_picklists.json'), headers: {})
    end

    context "within same request if #{described_class} is called multiple times" do
      it 'should call fetch data from API only once' do
        described_class.standard_picklists = nil

        described_class.call
        described_class.call
        described_class.call
        described_class.call

        expect(a_request(:get, SERVICE_CONFIG + "/v1/picklists/standard")).to have_been_made.once
      end

      it 'should not fetch data from API again' do
        described_class.call
        described_class.call
        described_class.call
        described_class.call
      
        expect(a_request(:get, SERVICE_CONFIG + "/v1/picklists/standard")).not_to have_been_made
      end
    end
  end
end
