# frozen_string_literal: true

require 'rails_helper'

RSpec.describe GetTenant do
  describe '#call' do
    before do
      user = create(:user, id: 12, tenant_id: 99)
      auth_data =  build(:auth_data, :lead_with_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:user] = user
      @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token
    end

    context 'when requested with valid arguments' do
      before do
        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{@token}",
          }
        ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})
      end

      it 'returns tenant data' do
        entity_response = described_class.call
        expect(entity_response.dig('id')).to eq(2174)
      end
    end

    context 'when request fails with 500 code' do
      before do
        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{@token}",
          }
        ).to_return(status: 500, body: {}.to_json, headers: {})
      end

      it 'logs and raises error' do
        expect(Rails.logger).to receive(:error).with('GetTenant - 500')
        expect { described_class.call }.to raise_error(ExceptionHandler::InternalServerError, '022004')
      end
    end

    context 'when request fails with 404 code' do
      before do
        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{@token}",
          }
        ).to_return(status: 404, body: {}.to_json, headers: {})
      end

      it 'logs and raises error' do
        expect(Rails.logger).to receive(:error).with('GetTenant - 404')
        expect { described_class.call }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
      end
    end

    context 'when request fails with 400 code' do
      before do
        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{@token}",
          }
        ).to_return(status: 400, body: {}.to_json, headers: {})
      end

      it 'logs and raises error' do
        expect(Rails.logger).to receive(:error).with('GetTenant - 400')
        expect { described_class.call }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
      end
    end
  end
end
