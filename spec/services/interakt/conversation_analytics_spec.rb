# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interakt::ConversationAnalytics, type: :service do
  describe '#fetch' do
    let(:connected_account) { create(:connected_account) }

    def stub_fetch_conversation_analytics_request(status_code: , connected_account: , start_time: , end_time: , response_body: , waba_number: )
      stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.waba_id}?fields=pricing_analytics.start(#{start_time}).end(#{end_time}).granularity(DAILY).phone_numbers([\"#{waba_number}\"]).dimensions(%5B%22PRICING_CATEGORY%22,%22PRICING_TYPE%22,%22COUNTRY%22,%22PHONE%22%5D)")
      .with(
        headers: {
          'Content-Type'=>'application/json',
          'X-Access-Token'=>'partner-token',
          'X-Waba-Id'=> connected_account.waba_id
        }
      ).to_return(status: status_code, body: response_body)
    end

    context 'when conversation details fetched successfully' do
      before do
        stub_fetch_conversation_analytics_request(
          status_code: 200,
          connected_account: connected_account,
          start_time: Time.now.to_i,
          end_time: 24.hours.ago.to_i,
          response_body: file_fixture('interakt/conversation_analytics/api-success-response.json'),
          waba_number: connected_account.waba_number
        )
      end

      it 'returns success true' do
        response = described_class.new(connected_account, Time.now.to_i, 24.hours.ago.to_i).fetch

        expect(response.status_200?).to be_truthy
        expect(response.body).to eq(
          {
            "pricing_analytics"=> {
              "data"=> [
                {
                  "data_points"=> [{
                    "start"=>**********,
                    "end"=>**********,
                    "volume"=>1,
                    "phone_number"=>"************",
                    "country"=>"KW",
                    "pricing_type"=>"REGULAR",
                    "pricing_category"=>"MARKETING",
                    "cost"=>2.4993
                  }]
                }
              ]
            },
            "id"=>"***************"
          }
        )
      end
    end

    context 'when waba number is in other format' do
      before do
        connected_account.update!(waba_number: '+1647-812-0555')

        stub_fetch_conversation_analytics_request(
          status_code: 200,
          connected_account: connected_account,
          start_time: Time.now.to_i,
          end_time: 24.hours.ago.to_i,
          response_body: file_fixture('interakt/conversation_analytics/api-success-response.json'),
          waba_number: '+***********'
        )
      end

      it 'returns success true' do
        response = described_class.new(connected_account, Time.now.to_i, 24.hours.ago.to_i).fetch

        expect(response.status_200?).to be_truthy
        expect(response.body).to eq(
          {
            "pricing_analytics"=> {
              "data"=> [
                {
                  "data_points"=> [{
                    "start"=>**********,
                    "end"=>**********,
                    "volume"=>1,
                    "phone_number"=>"************",
                    "country"=>"KW",
                    "pricing_type"=>"REGULAR",
                    "pricing_category"=>"MARKETING",
                    "cost"=>2.4993
                  }]
                }
              ]
            },
            "id"=>"***************"
          }
        )
      end
    end

    context 'when invalid api token' do
      before do
        stub_fetch_conversation_analytics_request(
          status_code: 400,
          connected_account: connected_account,
          start_time: Time.now.to_i,
          end_time: 24.hours.ago.to_i,
          response_body: file_fixture('interakt/conversation_analytics/invalid-token-response.json'),
          waba_number: connected_account.waba_number
        )
      end

      it 'raises error' do
        expect { described_class.new(connected_account, Time.now.to_i, 24.hours.ago.to_i).fetch }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Invalid request')
      end
    end
  end
end
