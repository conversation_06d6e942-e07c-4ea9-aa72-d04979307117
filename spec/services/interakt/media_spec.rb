# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interakt::Media, type: :request do
  let(:connected_account) { create(:connected_account) }

  describe '#get_media_url' do
    context 'success' do
      context 'when media url fetched sucessfully from meta media id' do
        before do
          stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/media/***************")
          .with(
            headers: {
              'Content-Type'=>'application/json',
              'X-Access-Token'=>'partner-token',
              'X-Waba-Id'=> connected_account.waba_id
            }
          ).to_return(status: 200, body: file_fixture('interakt/media/get-media-url-response.json'))
        end

        it 'returns media object' do
          response = described_class.new(connected_account).get_media_url('***************')

          expect(response.status_code).to eq('200')
          expect(response.body).to eq(
            {
              "url": "https:\/\/lookaside.fbsbx.com\/whatsapp_business\/attachments\/?mid=***************&ext=**********&hash=ATvGO4vAkFG17B3An7azeX8Wg8xiJlmg8GxrAscU1XdUFA",
              "mime_type": "image\/jpeg",
              "sha256": "8d0b4590b64af564d0062ab179eb3accb14f8188982bc1a38771f27f3f9712e5",
              "file_size": 23235,
              "id": "***************",
              "messaging_product": "whatsapp"
            }.with_indifferent_access
          )
        end
      end
    end

    context 'error' do
      context 'when media id is not valid' do
        before do
          stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/media/invalid-media-id")
          .with(
            headers: {
              'Content-Type'=>'application/json',
              'X-Access-Token'=>'partner-token',
              'X-Waba-Id'=> connected_account.waba_id
            }
          ).to_return(status: 400, body: file_fixture('interakt/media/get-media-url-error-response.json'))
        end

        it 'raises error' do
          expect { described_class.new(connected_account).get_media_url('invalid-media-id') }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||Unsupported get request. Object with ID '***************1' does not exist, cannot be loaded due to missing permissions, or does not support this operation.Please read the Graph API documentation at https: //developers.facebook.com/docs/graph-api")
        end
      end
    end
  end

  describe '#download_media_from_url' do
    let(:image_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
    let(:media_url) { "https://lookaside.fbsbx.com/whatsapp_business/attachments/?mid=****************%26ext=**********%26hash=ATu0KzxlP6bXnk1S6U16aIXWvhcq3XQJpuK3fAcID0BHAA" }

    context 'success' do
      context 'when media downloaded successfully from media url' do
        before do
          stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/media?url=#{media_url}")
          .with(
            headers: {
              'Content-Type'=>'application/json',
              'X-Access-Token'=>'partner-token',
              'X-Waba-Id'=> connected_account.waba_id
            }
          ).to_return(status: 200, body: File.open(image_file.path).read)
        end

        it 'returns media file' do
          response = described_class.new(connected_account).download_from_media_url(media_url)

          expect(response.code).to eq('200')
          expect(response.body).to eq(File.open(image_file.path).read)
        end
      end
    end

    context 'error' do
      context 'when media url is not valid' do
        before do
          stub_request(:get, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/media?url=#{media_url}")
          .with(
            headers: {
              'Content-Type'=>'application/json',
              'X-Access-Token'=>'partner-token',
              'X-Waba-Id'=> connected_account.waba_id
            }
          ).to_return(status: 400, body: 'Meta Error')
        end

        it 'raises error' do
          expect { described_class.new(connected_account).download_from_media_url(media_url) }.to raise_error(ExceptionHandler::ThirdPartyAPIError, "022017||Meta Error")
        end
      end
    end
  end
end
