# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interakt::Message do
  let(:connected_account) { create(:connected_account) }
  let(:phone_number) { '+************' }

  describe '#send_session_message' do
    context 'when text message' do
      let(:message_payload) do
        {
          "preview_url": true,
          "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
        }
      end

      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: phone_number,
              type: 'text',
              text: {
                "preview_url": true,
                "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
              }
            }.to_json
          ).to_return(status: 200, body: file_fixture('interakt/message/session-message-success-response.json'))
      end

      it 'returns message id in response' do
        message_response = described_class.new(connected_account).send_session_message('text', message_payload, phone_number)

        expect(message_response.status_code).to eq('200')
        expect(message_response.body).to eq({
          "messaging_product" => "whatsapp",
          "contacts" => [
            {
              "input" => "48XXXXXXXXX",
              "wa_id" => "48XXXXXXXXX"
            }
          ],
          "messages" => [
            {
              "id" => "wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww"
            }
          ]
        })
      end
    end

    context 'when error while sending message' do
      let(:message_payload) do
        {
          "preview_url": true,
          "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
        }
      end

      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: phone_number,
              type: 'text',
              text: {
                "preview_url": true,
                "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
              }
            }.to_json
          ).to_return(status: 400, body: file_fixture('interakt/message/session-message-failure-response.json'))
      end

      it 'raises error' do
        expect {
          described_class.new(connected_account).send_session_message('text', message_payload, phone_number)
        }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||(#131006) Resource not found. unknown contact')
      end
    end

    context 'when connected account is blank' do
      let(:message_payload) do
        {
          "preview_url": true,
          "body": "As requested, here'\''s the link to our latest product: https://www.meta.com/quest/quest-3/"
        }
      end

      it 'raises error' do
        expect {
          described_class.new(nil).send_session_message('text', message_payload, phone_number)
        }.to raise_error(ExceptionHandler::AccountNotConnectedError, '022018||Whatsapp Business Number not connected')
      end
    end
  end

  describe '#mark_message_as_read' do
    context 'when message id is incoming' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              status: 'read',
              message_id: '123'
            }.to_json
          ).to_return(status: 200, body: file_fixture('interakt/message/mark-message-as-read-success.json').read)
      end

      it 'marks message as read and returns success true' do
        read_response = described_class.new(connected_account).mark_message_as_read('123')

        expect(read_response.status_200?).to be_truthy
        expect(read_response.body).to eq({ 'success' => true })
      end
    end

    context 'when message id is not incoming' do
      before do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              status: 'read',
              message_id: '123'
            }.to_json
          ).to_return(status: 400, body: file_fixture('interakt/message/mark-message-as-read-error.json').read)
      end

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("Error in Interakt Request - 400 - #{JSON.parse(file_fixture('interakt/message/mark-message-as-read-error.json').read).to_s}")
        expect { described_class.new(connected_account).mark_message_as_read('123') }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||(#100) Invalid parameter. Message supplied to mark message as read API with message ID: wamid.******************************************************** is outgoing. Please use an incoming message ID.')
      end
    end
  end
end
