# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interakt::Onboarding do
  describe '#signup' do
    let(:connected_account) { create(:connected_account) }

    context 'when waba onboarded successfully to interakt' do
      before do
        stub_request(:post, "https://api.interakt.ai/v1/organizations/tp-signup/")
          .with(
            headers: {
              content_type: 'application/json',
              Authorization: 'partner-token'
            },
            body: {
              entry: [
                {
                  changes: [
                    {
                      value: {
                        event: 'PARTNER_ADDED',
                        waba_info: {
                          waba_id: connected_account&.waba_id,
                          solution_id: 'solution-id',
                          phone_number: connected_account&.waba_number
                        }
                      }
                    }
                  ]
                }
              ],
              object: 'tech_partner'
            }.to_json
          ).to_return(status: 200, body: file_fixture('interakt/onboarding/waba-onboarding-api-success.json'))
      end

      it 'returns success true' do
        interakt_response = described_class.new(connected_account).signup

        expect(interakt_response.status_200?).to be_truthy
        expect(interakt_response.body).to eq({ 'success' => 'true' })
      end
    end

    context 'when invalid api token' do
      before do
        stub_request(:post, "https://api.interakt.ai/v1/organizations/tp-signup/")
          .with(
            headers: {
              content_type: 'application/json',
              Authorization: 'partner-token'
            },
            body: {
              entry: [
                {
                  changes: [
                    {
                      value: {
                        event: 'PARTNER_ADDED',
                        waba_info: {
                          waba_id: connected_account&.waba_id,
                          solution_id: 'solution-id',
                          phone_number: connected_account&.waba_number
                        }
                      }
                    }
                  ]
                }
              ],
              object: 'tech_partner'
            }.to_json
          ).to_return(status: 403, body: file_fixture('interakt/onboarding/invalid-api-key-response.json'))
      end

      it 'raises error' do
        expect { described_class.new(connected_account).signup }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||Invalid API key')
      end
    end
  end
end
