require 'rails_helper'

RSpec.describe Message::DeleteMessageService do
  before do
    @user = create(:user, id: 1, tenant_id: 2)
    @user_from_other_tenant = create(:user, id: 20, tenant_id: 3)
    auth_data =  build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)

    thread = Thread.current
    thread[:auth] = auth_data
    thread[:user] = @user

    @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
    thread[:token] = @token
  end

  describe '#call' do
    context 'when sufficient permissions are avaliable to delete message' do
      let(:permission) { :sms_with_delete_permission }

      before { @message = create(:message, owner: @user, tenant_id: @user.tenant_id) }

      context 'when skip_event_publishing is false (default)' do
        it 'deletes message and child entities and publishes event' do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          Message::DeleteMessageService.call(@message.id)

          expect(Message.find_by_id(@message.id)).to be_nil
          expect(MessageLookUp.find_by_message_id(@message.id)).to be_nil
          expect(Attachment.find_by_message_id(@message.id)).to be_nil
          expect(Publishers::MessageDeleted).to have_received(:call)
        end
      end

      context 'when skip_event_publishing is true' do
        it 'deletes message and child entities but does not publish event' do
          allow(Publishers::TenantUsagePublisher).to receive(:call).and_return(nil)
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          Message::DeleteMessageService.new(@message.id, true, {}, true).call

          expect(Message.find_by_id(@message.id)).to be_nil
          expect(MessageLookUp.find_by_message_id(@message.id)).to be_nil
          expect(Attachment.find_by_message_id(@message.id)).to be_nil
          expect(Publishers::MessageDeleted).not_to have_received(:call)
        end
      end

      context 'when message has attachments' do
        before { @attachment = create(:attachment, message: @message) }

        it 'deletes attachments from S3' do
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
          Message::DeleteMessageService.call(@message.id)

          expect(S3::DeleteFileFromS3).to have_received(:call).with([@attachment.file_name], S3_ATTACHMENT_BUCKET)
        end
      end
    end

    context 'when message belongs to different tenant' do
      let(:permission) { :sms_with_delete_permission }

      before{ @message = create(:message, owner: @user_from_other_tenant, tenant_id: @user_from_other_tenant.tenant_id)}

      it 'throws not found error' do
        expect{ Message::DeleteMessageService.call(@message.id) }.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
      end
    end

    context 'when delete permission is not available' do
      let(:permission) { :sms_without_delete_permission }

      before{ @message = create(:message, owner: @user, tenant_id: @user.tenant_id)}

      it 'throws delete not allowed error' do
        expect{ Message::DeleteMessageService.call(@message.id) }.to raise_error(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed)
      end
    end
  end

  describe '#soft_delete' do
    context 'when sufficient permissions are available to delete message' do
      let(:permission) { :sms_with_delete_permission }
      let(:look_up) { create(:look_up, entity_type: LOOKUP_LEAD) }
      before do
        @message = create(:message, owner: @user, tenant_id: @user.tenant_id)
        @message.related_to << look_up
        @message.attachments << FactoryBot.create(:attachment, message: @message)
      end

      it 'soft deletes message and publishes event' do
        allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
        Message::DeleteMessageService.new(@message.id).soft_delete

        expect(@message.reload.deleted_at).not_to be_nil
        expect(MessageLookUp.find_by_message_id(@message.id)).not_to be_nil
        expect(Attachment.find_by_message_id(@message.id)).not_to be_nil
        expect(Publishers::MessageDeleted).to have_received(:call)
      end

      context 'when message has attachments' do
        before { @attachment = create(:attachment, message: @message) }

        it 'does not delete attachments from S3' do
          allow(Publishers::MessageDeleted).to receive(:call).and_return(nil)
          allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
          Message::DeleteMessageService.new(@message.id).soft_delete

          expect(S3::DeleteFileFromS3).not_to have_received(:call)
        end
      end
    end

    context 'when message belongs to different tenant' do
      let(:permission) { :sms_with_delete_permission }

      before{ @message = create(:message, owner: @user_from_other_tenant, tenant_id: @user_from_other_tenant.tenant_id)}

      it 'throws not found error' do
        expect{ Message::DeleteMessageService.new(@message.id).soft_delete }.to raise_error(ExceptionHandler::NotFound, ErrorCode.not_found)
      end
    end

    context 'when delete permission is not available' do
      let(:permission) { :sms_without_delete_permission }

      before{ @message = create(:message, owner: @user, tenant_id: @user.tenant_id)}

      it 'throws delete not allowed error' do
        expect{ Message::DeleteMessageService.new(@message.id).soft_delete }.to raise_error(ExceptionHandler::DeleteNotAllowedError, ErrorCode.delete_not_allowed)
      end
    end
  end
end
