# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Message::GetConversationMessageDetailsService do
  describe '#call' do
    let(:user){ create(:user) }
    let(:another_user){ create(:user, tenant_id: user.tenant_id) }
    let(:conversation){ create(:conversation, tenant_id: user.tenant_id, owner_id: another_user.id) }
    let(:message){ create(:message, conversation_id: conversation.id, tenant_id: user.tenant_id) }
    let(:look_up){ create(:look_up, tenant_id: user.tenant_id, entity_type: 'lead') }
    let(:valid_auth_token){ build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:invalid_auth_token){ build(:auth_token, :with_meesage_read_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
    let(:auth_data){ User::TokenParser.parse(valid_auth_token.token) }
    let(:invalid_auth_data){ User::TokenParser.parse(invalid_auth_token.token) }

    context 'Success' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: look_up.entity_id
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it 'returns message when entity_id and entity_type are present' do
        conversation.look_ups << look_up

        params = {
          entity_id: look_up.entity_id,
          entity_type: look_up.entity_type,
          conversation_id: conversation.id,
          message_id: message.id
        }

        message, related_to = Message::GetConversationMessageDetailsService.call(params)
        expect(message.id).to eq message.id
        expect(related_to.first).to eq look_up
      end

      it 'returns message when entity_id and entity_type are null and user has permission' do
        allow(user).to receive(:can_user_read_conversation_unrelated_to_entity?).with(conversation.owner_id).and_return(true)

        params = {
          entity_id: nil,
          entity_type: nil,
          conversation_id: conversation.id,
          message_id: message.id
        }

        message, related_to = Message::GetConversationMessageDetailsService.call(params)
        expect(message.id).to eq message.id
        expect(related_to).to eq conversation.look_ups
      end
    end

    context "Insufficient permission" do
      before do
        Thread.current[:auth] = invalid_auth_data
        Thread.current[:token] = invalid_auth_token.token
        Thread.current[:user] = user

        allow_any_instance_of(GenerateToken).to receive(:call).and_return(invalid_auth_token.token)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: look_up.entity_id
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{invalid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it "throws forbidden error when entity_id and entity_type are present" do
        conversation.look_ups << look_up

        params = {
          entity_id: look_up.entity_id,
          entity_type: look_up.entity_type,
          conversation_id: conversation.id,
          message_id: message.id
        }

        expect{ Message::GetConversationMessageDetailsService.call(params) }.to raise_error(ExceptionHandler::MessageNotAllowedError).with_message(ErrorCode.message_not_allowed)
      end

      it "throws forbidden error when entity_id and entity_type are null and user lacks permission" do
        allow(user).to receive(:can_user_read_conversation_unrelated_to_entity?).with(conversation.owner_id).and_return(false)

        params = {
          entity_id: nil,
          entity_type: nil,
          conversation_id: conversation.id,
          message_id: message.id
        }

        expect{ Message::GetConversationMessageDetailsService.call(params) }.to raise_error(ExceptionHandler::MessageNotAllowedError).with_message(ErrorCode.message_not_allowed)
      end
    end

    context "message is not found" do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user
      end

      it "throws not found error" do
        conversation.look_ups << look_up

        params = {
          entity_id: look_up.entity_id,
          entity_type: look_up.entity_type,
          conversation_id: conversation.id,
          message_id: 343434
        }

        expect{ Message::GetConversationMessageDetailsService.call(params) }.to raise_error(ExceptionHandler::NotFound).with_message(ErrorCode.not_found)
      end
    end

    context "entity details api fails" do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        allow_any_instance_of(GenerateToken).to receive(:call).and_return(valid_auth_token.token)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers id firstName lastName ownerId] ,
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: look_up.entity_id
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 400, body: file_fixture('get_lead_response.json').read, headers: {})
      end

      it "throws error" do
        conversation.look_ups << look_up

        params = {
          entity_id: look_up.entity_id,
          entity_type: look_up.entity_type,
          conversation_id: conversation.id,
          message_id: message.id
        }

        expect{ Message::GetConversationMessageDetailsService.call(params) }.to raise_error(ExceptionHandler::InvalidDataError).with_message(ErrorCode.invalid_data)
      end
    end
  end
end