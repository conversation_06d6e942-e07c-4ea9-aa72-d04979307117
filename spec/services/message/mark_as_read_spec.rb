# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Message::MarkAsRead do
  describe '#call' do
    let(:user) { create(:user) }
    let(:connected_account) { create(:connected_account, created_by: user, status: 'active') }
    let(:message) { create(:message, message_type: 'whatsapp_business', owner: user, tenant_id: user.tenant_id, connected_account: connected_account, remote_id: '123', direction: 'incoming') }

    before do
      Thread.current[:auth] = build(:auth_data, :lead_with_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name)
      Thread.current[:user] = user
      message.related_to << create(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
    end

    context 'when incoming whatsapp business message' do
      before  do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              status: 'read',
              message_id: '123'
            }.to_json
          ).to_return(status: 200, body: file_fixture('interakt/message/mark-message-as-read-success.json').read)
      end

      it 'marks message as read' do
        returned_message = described_class.new(message.id).call

        expect(returned_message.read_at).to be_present
        expect(returned_message.status).to eq('read')
      end
    end

    context 'when message type is not whatsapp business' do
      before { message.update(message_type: 'whatsapp') }

      it 'raises error' do
        expect { described_class.new(message.id).call }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Cannot mark outgoing or other message type as read. Please choose only an incoming message.')
      end
    end

    context 'when message direction is not incoming' do
      before { message.update(direction: 'outgoing') }

      it 'raises error' do
        expect { described_class.new(message.id).call }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Cannot mark outgoing or other message type as read. Please choose only an incoming message.')
      end
    end

    context 'when connected account is not active' do
      before { connected_account.update(status: 'inactive') }

      it 'raises error' do
        expect { described_class.new(message.id).call }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Please activate account.')
      end
    end

    context 'when error while marking message as read' do
      before  do
        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
          .with(
            headers: {
              'x-access-token': 'partner-token',
              'x-waba-id': connected_account.waba_id,
              content_type: 'application/json'
            },
            body: {
              messaging_product: 'whatsapp',
              status: 'read',
              message_id: '123'
            }.to_json
          ).to_return(status: 400, body: file_fixture('interakt/message/mark-message-as-read-error.json').read)
      end

      it 'raises error' do
        expect { described_class.new(message.id).call }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||(#100) Invalid parameter. Message supplied to mark message as read API with message ID: wamid.******************************************************** is outgoing. Please use an incoming message ID.')
      end
    end
  end
end
