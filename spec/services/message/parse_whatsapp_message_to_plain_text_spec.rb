# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Message::ParseWhatsappMessageToPlainText do
  describe '#call' do
    context 'parse' do
      it 'parses all inputs correctly' do
       input_output = JSON.parse(file_fixture('/whatsapp_parsed_messages.json').read)

       input_output.each do |key, value|
        expect(described_class.new(key).call).to eq(value)
       end
      end
    end
  end
end
