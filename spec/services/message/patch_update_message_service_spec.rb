require 'rails_helper'

RSpec.describe Message::PatchUpdateMessageService do
  describe '#call' do
    before do
      @user = create(:user, id: 12, tenant_id: 99)

      thread = Thread.current
      thread[:auth] = auth_data
      thread[:user] = @user

      token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token

      @message = create(:message, tenant_id: @user.tenant_id, owner: @user)
      @message.related_to << create(:look_up, entity_id: 10, entity_type: LOOKUP_LEAD)
      @file_1_name = 'tenant_99/user_11/22_old_file_1.jpg'

      @old_attachment = create(:attachment, message: @message, file_name: @file_1_name)
      @new_attachment = File.new("#{Rails.root}/spec/fixtures/files/audio_1.mp3")
    
      @params = {
        id: @message.id,
        medium: 'sms',
        sent_at: "2020-11-20T01:50:00.000Z",
        delivered_at: "2020-11-20T01:52:00.000Z",
        read_at: "2020-11-20T01:53:00.000Z",
        content: 'Updated content',
        direction: 'incoming',
        recipient_number: '+7387577710',
        sender_number: '+7387577712',
        status: 'read',
        attachments: [{ data: @new_recording, fileName: 'test-file.mp3' }],
      }

      allow(S3::DeleteFileFromS3).to receive(:call).with([@file_1_name], S3_ATTACHMENT_BUCKET).and_return(nil)
      allow(Attachment::CreateService).to receive_message_chain([:new, :call]).and_return(nil)

      @params = ActionController::Parameters.new(@params)
      @params.permit!
    end

    context 'when user has SMS write permission for WhatsApp business message' do
      let(:auth_data) { build(:auth_data, :sms_with_write_permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name) }

      before do
        @connected_account = create(:connected_account, tenant_id: @user.tenant_id, created_by: @user)
        @message.update(message_type: WHATSAPP_BUSINESS, message_type: 'whatsapp_business', connected_account_id: @connected_account.id)  
      end

      it 'updates the message successfully' do
        message = Message::PatchUpdateMessageService.call(@params)
        expect(message.medium).to eq(@params[:medium])
        expect(message.content).to eq(@params[:content])
        expect(message.direction).to eq(@params[:direction])
        expect(message.recipient_number).to eq(@params[:recipient_number])
        expect(message.sender_number).to eq(@params[:sender_number])
        expect(message.sent_at).to eq(@params[:sent_at])
        expect(message.delivered_at).to eq(@params[:delivered_at])
        expect(message.read_at).to eq(@params[:read_at])
      end
    end

    context 'when user has no SMS write permission for WhatsApp business message' do
      let(:auth_data) { build(:auth_data, :sms_without_write_permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name) }

      it 'raises MessageNotAllowedError' do
        expect { 
          Message::PatchUpdateMessageService.call(@params)
        }.to raise_error(ExceptionHandler::MessageNotAllowedError)
      end
    end

    context 'when message permission is available on the entity' do
      let(:auth_data) { build(:auth_data, :lead_with_sms_permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name) }

      context 'and tried to update the message' do
        it 'updates the message successfully' do
          message = Message::PatchUpdateMessageService.call(@params)
          expect(message.medium).to eq(@params[:medium])
          expect(message.content).to eq(@params[:content])
          expect(message.direction).to eq(@params[:direction])
          expect(message.recipient_number).to eq(@params[:recipient_number])
          expect(message.sender_number).to eq(@params[:sender_number])
          expect(message.sent_at).to eq(@params[:sent_at])
          expect(message.delivered_at).to eq(@params[:delivered_at])
          expect(message.read_at).to eq(@params[:read_at])
        end
      end
    end

    context 'when message permission is not available on the entity' do
      let(:auth_data) { build(:auth_data, :lead_without_sms_permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name) }

      context 'and tried to update the message' do
        it 'throws error' do
          expect{ Message::PatchUpdateMessageService.call(@params) }.to raise_error(ExceptionHandler::MessageNotAllowedError).with_message(ErrorCode.message_not_allowed)
        end
      end
    end
  end
end
