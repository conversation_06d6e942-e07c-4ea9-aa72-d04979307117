# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Message::SessionMessage do
  let(:user) { create(:user) }
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, status: ACTIVE) }
  let(:audio_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/audio_1.mp3', 'audio/mp3') }
  let(:text_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/sample_text_file.txt', 'text/plain') }
  let(:video_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/sample_video.mp4', 'video/mp4') }
  let(:image_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
  let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
  let(:token_without_sms) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data_without_sms) { User::TokenParser.parse(token_without_sms.token) }
  let(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
  let(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
  let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 123, entity_type: 'LEAD', actions: { 'sms': true }) }
  let(:admin_user) { create(:user, tenant_id: user.tenant_id) }
  let(:valid_admin_user_auth_token) { build(:auth_token, :with_meesage_and_lead_read_all_permission, user_id: admin_user.id, tenant_id: admin_user.tenant_id, username: admin_user.name) }
  let(:admin_user_auth_data) { User::TokenParser.parse(valid_admin_user_auth_token.token) }

  describe '#send_text' do
    let(:params) do
      ActionController::Parameters.new({
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'text',
        message_body: 'This is sample session message text.'
      }).permit!
    end

    context 'when user is not admin user' do
      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
      end

      context 'when user does not have sufficient whatsapp credits balance' do
        it 'raises error' do
          expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022023||Insufficient whatsapp credits balance')
        end
      end

      context 'when user has sufficient whatsapp credits balance' do
        before(:each) { whatsapp_credit }

        context 'when valid entity and message' do
          before do
            stub_request(:post, 'http://localhost:8083/v1/search/lead')
              .with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

            stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
              .with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'text',
                  text: {
                    body: 'This is sample session message text.'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
          end

          context 'when conversation is not found for given phone number and connected account' do
            it 'raises error' do
              expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
            end
          end

          context 'when conversation is found for given phone number and connected account' do
            before(:each) do
              conversation.update(last_message_received_at: 10.hours.ago)
              sub_conversation
            end

            context 'when user does not have required conversation permission' do
              it 'raises error' do
                expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
              end
            end

            context 'when user has required conversation permission' do
              before(:each) { user_share_rule }

              it 'sends and creates message' do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
                expect { described_class.new(params).send_text }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)

                message = Message.last
                expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
                expect(message.direction).to eq('outgoing')
                expect(message.content).to eq('This is sample session message text.')
                expect(message.recipient_number).to eq('+************')
                expect(message.sender_number).to eq(connected_account.waba_number)
                expect(message.message_type).to eq('whatsapp_business')
                expect(message.tenant_id).to eq(user.tenant_id)
                expect(message.sent_at.present?).to be_truthy
                expect(message.medium).to eq('whatsapp')
                expect(message.conversation_id).to eq(conversation.id)
                expect(message.sub_conversation_id).to eq(sub_conversation.id)

                lookup = message.related_to.first
                expect(lookup.entity_type).to eq('lead')
                expect(lookup.entity_id).to eq(123)
                expect(lookup.phone_number).to eq('************')
                expect(lookup.name).to eq('Lead Name')
                expect(lookup.owner_id).to eq(4010)
              end

              it 'adds connected account id on message' do
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
                described_class.new(params).send_text
                expect(Message.last.connected_account_id).to eq(connected_account.id)
              end
            end
          end
        end

        context 'when invalid request' do
          context 'when invalid entity type' do
            before { params[:entity_type] = 'deal' }

            it 'raises error' do
              expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
            end
          end

          context 'when invalid phone number id' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 123 }] }] }.to_json)
            end

            context 'when missing entity id' do
              before { params.delete(:entity_id) }

              it 'raises error' do
                expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
              end
            end

            context 'when user does not have sms permission on entity' do
              before do
                stub_request(:post, 'http://localhost:8083/v1/search/lead')
                  .with(
                    headers: {
                      Authorization: "Bearer #{@token_without_pid}",
                      content_type: 'application/json'
                    },
                    body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                  ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: false } }] }.to_json)
              end

              context 'when missing phone number id' do
                before { params.delete(:phone_id) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Phone number or conversation id missing.')
                end
              end

              context 'when message body is not present' do
                before { params.delete(:message_body) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Please ensure that message is present and less than 4096 characters.')
                end
              end

              context 'when message body exceeds allowed length' do
                before { params[:message_body] = 4097.times.map { 'a' }.join }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Please ensure that message is present and less than 4096 characters.')
                end
              end

              context 'when connected account not found' do
                before { params[:id] = -1 }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found.')
                end
              end

              context 'when inactive connected account' do
                before { connected_account.update(status: INACTIVE) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Inactive or unverified account. Please reconnect.')
                end
              end

              context 'when unverified account' do
                before { connected_account.update(is_verified: false) }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Inactive or unverified account. Please reconnect.')
                end
              end

              context 'when user is not an agent on connected account' do
                before { AgentUser.where(connected_account_id: connected_account.id).delete_all }

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
                end
              end

              context 'when user does not have access to entity' do
                before do
                  stub_request(:post, 'http://localhost:8083/v1/search/lead')
                    .with(
                      headers: {
                        Authorization: "Bearer #{@token_without_pid}",
                        content_type: 'application/json'
                      },
                      body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                    ).to_return(status: 200, body: { content: [] }.to_json)
                end

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::NotFound, '022006||lead not found')
                end
              end

              context 'when invalid phone number id' do
                before do
                  stub_request(:post, 'http://localhost:8083/v1/search/lead')
                    .with(
                      headers: {
                        Authorization: "Bearer #{@token_without_pid}",
                        content_type: 'application/json'
                      },
                      body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                    ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 123 }] }] }.to_json)
                end

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022027||This number is no longer associated with this entity. To continue the conversation, please use an alternate number.")
                end
              end

              context 'when user does not have sms permission on entity' do
                before do
                  stub_request(:post, 'http://localhost:8083/v1/search/lead')
                    .with(
                      headers: {
                        Authorization: "Bearer #{@token_without_pid}",
                        content_type: 'application/json'
                      },
                      body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                    ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: false } }] }.to_json)
                end

                it 'raises error' do
                  expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::MessageNotAllowedError, "022007")
                end
              end
            end
          end

          context 'when session is inactive' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead')
                .with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              conversation.update(last_message_received_at: 30.hours.ago)
              sub_conversation
              user_share_rule
            end

            it 'raises error' do
              expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Session is inactive.")
            end
          end
        end
      end
    end

    context 'when user is admin user with read_all permission on entity and conversation' do
      context 'and user is not owner of this entity or entity is not shared with user' do
        before do
          create(:agent_user, tenant_id: admin_user.tenant_id, user_id: admin_user.id, connected_account_id: connected_account.id)
          Thread.current[:auth] = admin_user_auth_data
          Thread.current[:token] = valid_admin_user_auth_token.token
          Thread.current[:user] = admin_user

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end

          whatsapp_credit
          conversation.update(last_message_received_at: 10.hours.ago)
          sub_conversation

          @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          allow_any_instance_of(GenerateToken).to receive(:call).and_return(@token_without_pid)

          stub_request(:post, 'http://localhost:8083/v1/search/lead')
            .with(
              headers: {
                Authorization: "Bearer #{@token_without_pid}",
                content_type: 'application/json'
              },
              body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
            ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages")
            .with(
              headers: {
                'x-access-token': 'partner-token',
                'x-waba-id': connected_account.waba_id,
                content_type: 'application/json'
              },
              body: {
                messaging_product: 'whatsapp',
                recipient_type: 'individual',
                to: '+************',
                type: 'text',
                text: {
                  body: 'This is sample session message text.'
                }
              }.to_json
            ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
        end

        context 'when phone_id is present' do
          it 'sends and creates message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
            expect { described_class.new(params).send_text }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)

            message = Message.last
            expect(message.remote_id).to eq('wamid.gBGGSFcCNEOPAgkO_KJ55r4w_ww')
            expect(message.direction).to eq('outgoing')
            expect(message.content).to eq('This is sample session message text.')
            expect(message.recipient_number).to eq('+************')
            expect(message.sender_number).to eq(connected_account.waba_number)
            expect(message.message_type).to eq('whatsapp_business')
            expect(message.tenant_id).to eq(user.tenant_id)
            expect(message.sent_at.present?).to be_truthy
            expect(message.medium).to eq('whatsapp')
            expect(message.conversation_id).to eq(conversation.id)
            expect(message.sub_conversation_id).to eq(sub_conversation.id)

            lookup = message.related_to.first
            expect(lookup.entity_type).to eq('lead')
            expect(lookup.entity_id).to eq(123)
            expect(lookup.phone_number).to eq('************')
            expect(lookup.name).to eq('Lead Name')
            expect(lookup.owner_id).to eq(4010)
          end
        end

        context 'when conversation_id is present' do
          before { params[:conversation_id] = conversation.id, params[:phone_id] = nil }

          it 'sends and creates message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
            expect { described_class.new(params).send_text }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)
          end

          it 'throws error if phone number is not present on entity' do
            conversation.update(phone_number: '+919898988776')
            expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::InvalidDataError, "022027||This number is no longer associated with this entity. To continue the conversation, please use an alternate number.")
          end

          it 'throws error if conversation is not found' do
            params[:conversation_id] = 333
            expect { described_class.new(params).send_text }.to raise_error(ExceptionHandler::ConversationNotFoundError, "022026||Conversation not found")
          end
        end
      end
    end

    context 'when conversation has no lookups and entity_id, entity_type are null' do
      let(:params) do
        ActionController::Parameters.new({
          id: connected_account.id,
          entity_type: nil,
          entity_id: nil,
          conversation_id: conversation.id,
          message_type: 'text',
          message_body: 'This is a test message without entity details.'
        }).permit!
      end

      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        create(:agent_user, tenant_id: admin_user.tenant_id, user_id: admin_user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = admin_user_auth_data
        Thread.current[:token] = valid_admin_user_auth_token.token
        Thread.current[:user] = admin_user

        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        allow_any_instance_of(Interakt::Message).to receive(:send_session_message).and_return(OpenStruct.new(body: { 'messages' => [{ 'id' => 'wamid.test123' }] }))
      end

      it 'sends a text message successfully' do
        expect { described_class.new(params).send_text }
          .to change(Message, :count).by(1)

        message = Message.last
        expect(message.remote_id).to eq('wamid.test123')
        expect(message.direction).to eq('outgoing')
        expect(message.content).to eq('This is a test message without entity details.')
        expect(message.recipient_number).to eq('+************')
        expect(message.sender_number).to eq(connected_account.waba_number)
        expect(message.message_type).to eq('whatsapp_business')
        expect(message.tenant_id).to eq(user.tenant_id)
        expect(message.sent_at.present?).to be_truthy
        expect(message.medium).to eq('whatsapp')
        expect(message.conversation_id).to eq(conversation.id)
        expect(message.sub_conversation_id).to eq(sub_conversation.id)
      end
    end
  end

  describe '#send_media' do
    let(:audio_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: audio_file,
            type: 'audio'
          }
        ]
      }.with_indifferent_access
    end

    let(:text_document_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: text_file,
            type: 'document',
            caption: 'Here is the requeseted document attached.'
          }
        ]
      }.with_indifferent_access
    end

    let(:video_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: video_file,
            type: 'video',
            caption: 'Here is the requeseted video attached.'
          }
        ]
      }.with_indifferent_access
    end

    let(:image_params) do
      {
        id: connected_account.id,
        entity_type: 'lead',
        entity_id: 123,
        phone_id: 111,
        message_type: 'media',
        media: [
          {
            file: image_file,
            type: 'image',
            caption: 'Here is the requeseted image attached.'
          }
        ]
      }.with_indifferent_access
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when user does not have sufficient whatsapp credits balance' do
      it 'raises error' do
        captioned_audio_params = audio_params
        captioned_audio_params[:media][0][:caption] = 'some text'
        expect { described_class.new(captioned_audio_params).send_media }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022023||Insufficient whatsapp credits balance')
      end
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user

      permissions = Thread.current[:auth].permissions.as_json.map do |permission|
        permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
        permission
      end

      @token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

      conversation.update(last_message_received_at: 10.hours.ago)
      sub_conversation
    end

    context 'when user has sufficient whatsapp credits balance' do
      before(:each) { whatsapp_credit }

      context 'when invalid request' do
        context 'when invalid entity is passed' do
          let(:invalid_entity_params) do
            ActionController::Parameters.new({
              entity_type: 'deal'
            })
          end

          it 'raises invalid entity error' do
            expect { described_class.new(invalid_entity_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid entity type or entity id.')
          end
        end

        context 'when phone_id is not present' do
          let(:phone_id_missing_params) do
            ActionController::Parameters.new({
              entity_type: 'lead',
              entity_id: 123
            })
          end

           it 'raises missing phone id error' do
            expect { described_class.new(phone_id_missing_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Phone number or conversation id missing.')
          end
        end

        context 'when message type is not media' do
          let(:invalid_message_type_params) do
            ActionController::Parameters.new({
              entity_type: 'lead',
              entity_id: 123,
              phone_id: 111,
              message_type: 'text',
            }).permit!
          end

          it 'raises invalid message type error' do
            expect { described_class.new(invalid_message_type_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid message type.')
          end
        end

        context 'when invalid media is passed' do
          context 'when file type is invalid' do
            let(:invalid_file_type_params) do
              ActionController::Parameters.new({
                entity_type: 'lead',
                entity_id: 123,
                phone_id: 111,
                message_type: 'media',
                media: [
                  {
                    file: Rack::Test::UploadedFile.new('spec/fixtures/files/invalid_type.text', 'txt'),
                    type: 'audio'
                  }
                ]
              }).permit!
            end

            it 'raises error' do
              expect { described_class.new(invalid_file_type_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid media type.')
            end
          end

          context 'when file size is large' do
            let(:large_file_params) do
              ActionController::Parameters.new({
                entity_type: 'lead',
                entity_id: 123,
                phone_id: 111,
                message_type: 'media',
                media: [
                  {
                    file: audio_file,
                    type: 'audio'
                  }
                ]
              }).permit!
            end

            before { allow(audio_file).to receive(:size).and_return(1_67_77_217) }

            it 'raises error' do
              expect { described_class.new(large_file_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, '022014||File size exceeds limit.')
            end
          end
        end

        context 'when session is inactive' do
          before do
            create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
            Thread.current[:auth] = auth_data
            Thread.current[:token] = valid_auth_token.token
            Thread.current[:user] = user
            stub_request(:post, 'http://localhost:8083/v1/search/lead')
              .with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, firstName: 'Lead', lastName: 'Name', ownerId: 4010, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

            conversation.update(last_message_received_at: 30.hours.ago)
            sub_conversation
            user_share_rule
          end

          it 'raises error' do
            expect { described_class.new(audio_params).send_media }.to raise_error(ExceptionHandler::InvalidDataError, "022014||Session is inactive.")
          end
        end
      end

      context 'with valid request' do
        before do
          create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
          Thread.current[:auth] = auth_data
          Thread.current[:token] = valid_auth_token.token
          Thread.current[:user] = user
        end

        context 'when media type is audio' do
          context 'and caption is passed' do
            it 'raises invalid data error' do
              captioned_audio_params = audio_params
              captioned_audio_params[:media][0][:caption] = 'some text'

              expect {
                described_class.new(captioned_audio_params).send_media
              }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Caption is not allowed for this message.')
            end
          end

          context 'when valid params are passed for audio' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'audio',
                  audio: {
                    id: '<MEDIA_ID>'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

              s3_instance = instance_double(S3::UploadFile)
              allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
              expect(s3_instance).to receive(:call)

              expect(File).to receive(:delete)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              user_share_rule
            end

            it 'uploads media and creates media message' do
              expect { described_class.new(audio_params).send_media }
                .to change(Message, :count).by(1)
                .and change(MessageLookUp, :count).by(1)
                .and change(LookUp, :count).by(1)
                .and change(Attachment, :count).by(1)

              message = Message.last
              expect(message.attachments.count).to eq(1)
              expect(message.conversation_id).to eq(conversation.id)
              expect(message.sub_conversation_id).to eq(sub_conversation.id)
            end

            it 'adds connected account id on message' do
              described_class.new(audio_params).send_media
              expect(Message.last.connected_account_id).to eq(connected_account.id)
            end
          end
        end

        context 'when media type is document' do
          context 'with valid params' do
            context 'when user does not have required conversation permission' do
              before do
                stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', ownerId: 2323, phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

                stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                  headers: {
                    'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                  }
                ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

                stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    recipient_type: 'individual',
                    to: '+************',
                    type: 'document',
                    document: {
                      id: '<MEDIA_ID>',
                      caption: 'Here is the requeseted document attached.'
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))
              end

              it 'raises error' do
                expect { described_class.new(text_document_params).send_media }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
              end
            end

            context 'when user has required conversation permission' do
              before do
                stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                  headers: {
                    Authorization: "Bearer #{@token_without_pid}",
                    content_type: 'application/json'
                  },
                  body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
                ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

                stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                  headers: {
                    'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                  }
                ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

                stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                  headers: {
                    'x-access-token': 'partner-token',
                    'x-waba-id': connected_account.waba_id,
                    content_type: 'application/json'
                  },
                  body: {
                    messaging_product: 'whatsapp',
                    recipient_type: 'individual',
                    to: '+************',
                    type: 'document',
                    document: {
                      id: '<MEDIA_ID>',
                      caption: 'Here is the requeseted document attached.',
                      filename: 'sample_text_file.txt'
                    }
                  }.to_json
                ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

                s3_instance = instance_double(S3::UploadFile)
                allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
                expect(s3_instance).to receive(:call)

                expect(File).to receive(:delete)
                expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
                user_share_rule
              end

              it 'uploads media and creates media message' do
                expect { described_class.new(text_document_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)
                  .and change(Attachment, :count).by(1)

                message = Message.last
                expect(message.content).to eq('Here is the requeseted document attached.')
              end
            end
          end
        end

        context 'when media type is video' do
          context 'with valid params' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'video',
                  video: {
                    id: '<MEDIA_ID>',
                    caption: 'Here is the requeseted video attached.'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

              s3_instance = instance_double(S3::UploadFile)
              allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
              expect(s3_instance).to receive(:call)

              expect(File).to receive(:delete)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              user_share_rule
            end

            it 'uploads media and creates media message' do
              expect { described_class.new(video_params).send_media }
                .to change(Message, :count).by(1)
                .and change(MessageLookUp, :count).by(1)
                .and change(LookUp, :count).by(1)
                .and change(Attachment, :count).by(1)

              message = Message.last
              expect(message.content).to eq('Here is the requeseted video attached.')
            end
          end
        end

        context 'when media type is image' do
          context 'with valid params' do
            before do
              stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
                headers: {
                  Authorization: "Bearer #{@token_without_pid}",
                  content_type: 'application/json'
                },
                body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
              ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

              stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

              stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
                headers: {
                  'x-access-token': 'partner-token',
                  'x-waba-id': connected_account.waba_id,
                  content_type: 'application/json'
                },
                body: {
                  messaging_product: 'whatsapp',
                  recipient_type: 'individual',
                  to: '+************',
                  type: 'image',
                  image: {
                    id: '<MEDIA_ID>',
                    caption: 'Here is the requeseted image attached.'
                  }
                }.to_json
              ).to_return(status: 200, body: file_fixture('facebook/message/session-message-success-response.json'))

              s3_instance = instance_double(S3::UploadFile)
              allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
              expect(s3_instance).to receive(:call)

              expect(File).to receive(:delete)
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              user_share_rule
            end

            context 'when phone_id is present' do
              it 'uploads media and creates media message' do
                expect { described_class.new(image_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)
                  .and change(Attachment, :count).by(1)

                message = Message.last
                expect(message.content).to eq('Here is the requeseted image attached.')
              end
            end

            context 'when conversation_id is present' do
              before { image_params[:conversation_id] = conversation.id, image_params[:phone_id] = nil }

              it 'uploads media and creates media message' do
                expect { described_class.new(image_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(MessageLookUp, :count).by(1)
                  .and change(LookUp, :count).by(1)
                  .and change(Attachment, :count).by(1)
              end
            end
          end
        end
      end
    end

    context 'when message sending fails due to error from meta or interakt'do
      before(:each) { whatsapp_credit }

      before do
        create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)

        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          headers: {
            Authorization: "Bearer #{@token_without_pid}",
            content_type: 'application/json'
          },
          body: "{\"fields\":[\"phoneNumbers\",\"id\",\"firstName\",\"lastName\",\"ownerId\"],\"jsonRule\":{\"rules\":[{\"operator\":\"equal\",\"id\":\"id\",\"field\":\"id\",\"type\":\"double\",\"value\":123}],\"condition\":\"AND\",\"valid\":true}}"
        ).to_return(status: 200, body: { content: [{ id: 123, name: 'Lead Name', phoneNumbers: [{ code: 'IN', dialCode: '+91', type: 'MOBILE', value: '**********', primary: true, id: 111 }], recordActions: { sms: true } }] }.to_json)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
          headers: {
            'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
          }
        ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            content_type: 'application/json'
          },
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'image',
            image: {
              id: '<MEDIA_ID>',
              caption: 'Here is the requeseted image attached.'
            }
          }.to_json
        ).to_return(status: 400, body: file_fixture('facebook/message/session-message-failure-response.json'))

        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        user_share_rule
      end

      it 'should mark message as failed' do
        expect { described_class.new(image_params).send_media }
            .to change(Message, :count).by(1)
            .and change(MessageLookUp, :count).by(1)
            .and change(LookUp, :count).by(1)
            .and change(Attachment, :count).by(1)

          message = Message.last
          expect(message.status).to eq('failed')
      end
    end

    context 'when conversation has no lookups and entity_id, entity_type are null' do
      before do
        expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
        create(:agent_user, tenant_id: admin_user.tenant_id, user_id: admin_user.id, connected_account_id: connected_account.id)
        Thread.current[:auth] = admin_user_auth_data
        Thread.current[:token] = valid_admin_user_auth_token.token
        Thread.current[:user] = admin_user

        image_params['entity_type'] = nil
        image_params['entity_id'] = nil
        image_params['phone_id'] = nil
        image_params['conversation_id'] = conversation.id
        
        whatsapp_credit
        conversation.update(last_message_received_at: 10.hours.ago)
        sub_conversation
        allow_any_instance_of(Interakt::Message).to receive(:send_session_message).and_return(OpenStruct.new(body: { 'messages' => [{ 'id' => 'wamid.test123' }] }))
        s3_instance = instance_double(S3::UploadFile)
        allow(S3::UploadFile).to receive(:new).with(anything, anything, S3_ATTACHMENT_BUCKET, true).and_return(s3_instance)
        expect(s3_instance).to receive(:call)

        expect(File).to receive(:delete)

        stub_request(:post, "https://graph.facebook.com/v19.0/#{connected_account.phone_number_id}/media").with(
                headers: {
                  'Authorization'=> "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/media/upload-response.json'), headers: {})
      end

      it 'sends a media message successfully' do
        expect { described_class.new(image_params).send_media }
                  .to change(Message, :count).by(1)
                  .and change(Attachment, :count).by(1)
      end
    end
  end
end
