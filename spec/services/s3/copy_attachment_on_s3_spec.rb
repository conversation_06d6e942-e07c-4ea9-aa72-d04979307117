require 'rails_helper'

RSpec.describe S3::CopyAttachmentOnS3 do
  let(:src_path) { 'source/path/file.txt' }
  let(:dest_path) { 'destination/path/file.txt' }
  let(:s3_client) { instance_double(Aws::S3::Resource) }
  let(:bucket) { instance_double(Aws::S3::Bucket) }
  let(:object) { instance_double(Aws::S3::Object) }

  before do
    allow(Aws::S3::Resource).to receive(:new).and_return(s3_client)
    allow(s3_client).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(bucket)
    allow(bucket).to receive(:object).with(src_path).and_return(object)
  end

  describe '#call' do
    context 'when copy is successful' do
      before do
        allow(object).to receive(:copy_to).with(bucket: S3_ATTACHMENT_BUCKET, key: dest_path)
      end

      it 'copies the file successfully' do
        expect(object).to receive(:copy_to).with(bucket: S3_ATTACHMENT_BUCKET, key: dest_path)
        described_class.new(src_path, dest_path).call
      end
    end

    context 'when copy fails' do
      before do
        allow(object).to receive(:copy_to).and_raise(StandardError.new('Copy failed'))
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with("Email Service | Copy file to s3: Copy failed | #{src_path}")
        described_class.new(src_path, dest_path).call
      end
    end
  end
end 