# frozen_string_literal: true

require 'rails_helper'

RSpec.describe S3::DownloadFileFromS3 do
  let(:file_name) { 'test_file.txt' }
  let(:bucket_name) { 'qa-message-attachment' }
  let(:s3_resource) { instance_double(Aws::S3::Resource) }
  let(:s3_bucket) { instance_double(Aws::S3::Bucket) }
  let(:s3_object) { instance_double(Aws::S3::Object) }

  before do
    stub_const('S3_ATTACHMENT_BUCKET', bucket_name)
    allow(Aws::S3::Resource).to receive(:new).and_return(s3_resource)
    allow(s3_resource).to receive(:bucket).with(bucket_name).and_return(s3_bucket)
    allow(s3_bucket).to receive(:object).with(file_name).and_return(s3_object)
  end

  describe '#call' do
    context 'when the file is successfully downloaded' do
      before do
        allow(s3_object).to receive(:download_file).with("test_file.txt")
      end

      it 'downloads the file from S3' do
        expect(s3_object).to receive(:download_file).with("test_file.txt")
        described_class.new("test_file.txt").call
      end
    end

    context 'when there is an error downloading the file' do
      before do
        allow(s3_object).to receive(:download_file).and_raise(StandardError.new('Download failed'))
        allow(Rails.logger).to receive(:error)
      end

      it 'logs an error message' do
        expect(Rails.logger).to receive(:error).with(/Error while downloading attachment from s3 for Error: Download failed/)
        subject = described_class.new(file_name)
        subject.call
      end
    end
  end
end
