require 'rails_helper'

RSpec.describe SearchContacts do
  describe "#call" do
    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = User::TokenParser.parse(@token)
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns correct output" do
        data = { "content":[{ "phoneNumbers": [ { "code":"IN", "dialCode":"+91", "type":"MOBILE", "value":"7387577722", "primary":true }],
                             "name":"Contact1 test","id":1, "ownerId": 123 },
                           { "phoneNumbers": [ { "code":"IN", "dialCode":"+91", "type":"MOBILE", "value":"7387577723", "primary":true }],
                            "name":"Contact2 test","id":2, "ownerId": 234 }]
               }

        rules = []
        phone_numbers = ['+917387577722', '+917387577724']
        parsed_phone_numbers = [Phonelib.parse('+917387577722'), Phonelib.parse('+917387577724')]
        parsed_phone_numbers.each do |phone_number|
          rules << {
            "id": "multi_field",
            "field": "multi_field",
            "type": "multi_field",
            "input": "multi_field",
            "operator": "multi_field",
            "value": phone_number.raw_national
          }
        end

        payload = { fields: ["id", "name", "phoneNumbers", "ownerId"], jsonRule: { rules: rules, "condition": "OR", "valid": true } }

        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100").
        with(
          body: payload.to_json,
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: data.to_json, headers: {})

        result = SearchContacts.new(phone_numbers, 1).call

        expect(result[:matched]).to eq([{ entity: "contact", id: 1, phone_number: "+917387577722", name: "Contact1 test", tenant_id: 1, owner_id: 123 }])
        expect(result[:unmatched]).to eq(['+917387577724'])
      end
    end
  end
end
