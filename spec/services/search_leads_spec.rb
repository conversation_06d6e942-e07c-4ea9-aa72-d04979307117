require 'rails_helper'

RSpec.describe SearchLeads do
  describe "#call" do
    let(:permission) { :lead_with_sms_permission }

    before { @user = create(:user, id: 12, tenant_id: 99) }

    before do
      auth_data =  build(:auth_data, permission, user_id: @user.id, tenant_id: @user.tenant_id, username: @user.name)
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:user] = @user
      @token = FactoryBot.build(:auth_token, user_id: 12, tenant_id: 99).token
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns correct output" do
        data = { "content":[{ "phoneNumbers": [ { "code":"IN", "dialCode":"+91", "type":"MOBILE", "value":"7387577722", "primary":true }],
                              "name":"Lead1 test","id":1, "ownerId": 123 },
                              { "phoneNumbers": [ { "code":"IN", "dialCode":"+91", "type":"MOBILE", "value":"7387577723", "primary":true }],
                                "name":"Lead2 test","id":2, "ownerId": 234 }]
        }

        rules = []
        phone_numbers = ['+917387577722', '+917387577724']
        parsed_phone_numbers = [Phonelib.parse('+917387577722'), Phonelib.parse('+917387577724')]
        parsed_phone_numbers.each do |phone_number|
          rules << {
            "id": "multi_field",
            "field": "multi_field",
            "type": "multi_field",
            "input": "multi_field",
            "operator": "multi_field",
            "value": phone_number.raw_national
          }
        end

        payload = { fields: ["id", "name", "phoneNumbers", "ownerId"], jsonRule: { rules: rules, "condition": "OR", "valid": true } }

        stub_request(:post, SERVICE_SEARCH + "/v1/search/lead?sort=updatedAt,desc&page=0&size=100").
          with(
            body: payload.to_json,
            headers: {
              "Authorization" => "Bearer #{ @token }"
            }).
            to_return(status: 200, body: data.to_json, headers: {})

            result = SearchLeads.new(phone_numbers, 1).call

            expect(result[:matched]).to eq([{ entity: "lead", id: 1, phone_number: "+917387577722", name: "Lead1 test", tenant_id: 1, owner_id: 123}])
            expect(result[:unmatched]).to eq(['+917387577724'])
      end
    end
  end
end
