# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Team::GetDetails do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token).token
    end

    context 'for existing team' do
      before do
        @user = create(:user)
        @team = create(:team, tenant_id: @user.tenant_id)
        @result = Team::GetDetails.call(@user.id, @team.id, @user.tenant_id)
      end

      it 'returns the same team' do
        expect(@result).to eq(@team)
      end
    end

    context 'for new team' do
      before do
        @user = create(:user)
        allow(GenerateToken).to receive(:call).and_return(@token)
        stub_request(:post, "http://localhost:8081/v1/teams/search")
          .with(
            body: {
              fields: [
                'name',
                'id'
              ],
              jsonRule: {
                rules: [
                  {
                    operator: 'in',
                    id: 'id',
                    field: 'id',
                    type: 'long',
                    value: '123'
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Authorization'=>"Bearer #{@token}",
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: file_fixture('teams-response.json'), headers: {})

        stub_request(:get, "http://localhost:8081/v1/teams/123/users")
          .with(
            headers: {
              'Authorization'=>"Bearer #{@token}",
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: file_fixture('team-users-response.json'), headers: {})

        @team = Team::GetDetails.call(@user.id, 123, @user.tenant_id)
      end

      it 'returns newly created team' do
        expect(@team.id).to eq(123)
        expect(@team.user_ids).to eq([7638, 7755])
        expect(@team.name).to eq('team 2')
      end
    end
  end
end
