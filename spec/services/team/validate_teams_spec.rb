# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Team::ValidateTeams, type: :service do
  describe '#call' do
    let(:team_users) { file_fixture('team-users-response.json').read }

    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
    end

    context 'when team is invalid' do
      before do
        allow(GenerateToken).to receive(:call).and_return(@token)
        stub_request(:post, "http://localhost:8081/v1/teams/search").with(
          body: {
            fields: [
              'name',
              'id'
            ],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'long',
                  value: '243'
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Authorization'=>"Bearer #{@token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('teams-response.json'), headers: {})
      end

      it 'raises invalid data error' do
        expect { Team::ValidateTeams.new([Team.new(id: 243)], 9, 10).call }.to raise_error(ExceptionHandler::InvalidDataError, '022014||Invalid team.')
      end
    end

    context 'when team is valid' do
      before do
        allow(GenerateToken).to receive(:call).and_return(@token)
        stub_request(:post, "http://localhost:8081/v1/teams/search")
          .with(
            body: {
              fields: [
                'name',
                'id'
              ],
              jsonRule: {
                rules: [
                  {
                    operator: 'in',
                    id: 'id',
                    field: 'id',
                    type: 'long',
                    value: '123'
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Authorization'=>"Bearer #{@token}",
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: file_fixture('teams-response.json'), headers: {})

        stub_request(:get, "http://localhost:8081/v1/teams/123/users")
          .with(
            headers: {
              'Authorization'=>"Bearer #{@token}",
              'Content-Type'=>'application/json'
            }
          ).to_return(status: 200, body: file_fixture('team-users-response.json'), headers: {})
      end

      it 'returns teams by fetching users in the team' do
        response = Team::ValidateTeams.new([Team.new(id: 123)], 9, 10).call
        expect(response.first.id).to eq(123)
        expect(response.first.user_ids).to eq([7638, 7755])
      end
    end
  end
end
