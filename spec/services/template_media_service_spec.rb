# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TemplateMediaService do
  let(:user) { create(:user) }
  let(:valid_auth_token) { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data) { User::TokenParser.parse(invalid_auth_token.token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:sample_png_3mb) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_png_3mb.png', 'image/png') }
  let(:sample_text_file) { Rack::Test::UploadedFile.new('spec/fixtures/files/whatsapp_templates/sample_files/sample_text.txt') }
  let(:template_media) { create(:template_media, file_name: "tenant_#{user.tenant_id}/connetced_account_#{connected_account.id}/file/ab/c.png", tenant_id: user.tenant_id) }

  describe '#upload' do
    context 'when media file is not present' do
      it 'raises invalid data error' do
        expect { described_class.new({}).upload }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
      end
    end

    context 'when user does not have permission to create template' do
      before do
        Thread.current[:auth] = invalid_auth_data
        Thread.current[:user] = user
      end

      it 'raises unauthorized error' do
        expect { described_class.new({ media_file: sample_png_3mb }).upload }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end

    context 'with valid token' do
      before do
        Thread.current[:auth] = auth_data
        Thread.current[:user] = user
      end

      context 'when connected account is not found' do
        it 'raises not found error' do
          expect { described_class.new({ media_file: sample_png_3mb, connected_account_id: -1 }).upload }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
        end
      end

      context 'when connected accout is present' do
        context 'and current user is not an agent in connected account' do
          it 'raises invalid data error' do
            expect { described_class.new({ media_file: sample_png_3mb, connected_account_id: connected_account.id }).upload }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
          end
        end

        context 'and current user is an agent in connected account' do
          before do
            create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
          end

          context 'when file type is invalid' do
            it 'raises invalid data error' do
              expect {
                described_class.new({ media_file: sample_text_file, connected_account_id: connected_account.id }).upload
              }.to raise_error(ExceptionHandler::InvalidDataError, '022021||Invalid file type')
            end
          end

          context 'when file type is valid' do
            before do
              allow(SecureRandom).to receive(:uuid).and_return('48a02e4f-aef4-42e2-a2ad-de97b9004106')
              stub_request(:post, "https://graph.facebook.com/v19.0/#{ENV['FACEBOOK_CLIENT_ID']}/uploads?file_length=3124201&file_name=sample_png_3mb.png&file_type=image/png").with(
                headers: {
                  Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}",
                }
              ).to_return(status: 200, body: file_fixture('facebook/upload_template_media/upload-session-start-response.json').read)

              stub_request(:post, 'https://graph.facebook.com/v19.0/upload:MTphdHRhY2htZW50OjZlMmZhMjhlLTBjMDYtNGRkMS04YzQ0LWI5ZTM5NjEzZGNjZj9maWxlX25hbWU9U2FtcGxlUE5HSW1hZ2VfM21iLnBuZyZmaWxlX2xlbmd0aD0zMTI0MjAxJmZpbGVfdHlwZT1pbWFnZSUyRnBuZw==?sig=ARYJiWLJenUxDYvB6js').with(
                body: sample_png_3mb.path,
                headers: {
                  Authorization: "OAuth #{ConnectedAccount.decrypt(connected_account.access_token)}",
                  'Content-Type'=> 'application/json',
                }
              ).to_return(status: 200, body: file_fixture('facebook/upload_template_media/template-media-upload-response.json').read)

              file_name = "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/sample_png_3mb_#{SecureRandom.uuid}.png"
              resource = instance_double(Aws::S3::Resource)
              bucket = instance_double(Aws::S3::Bucket)
              allow(Aws::S3::Resource).to receive(:new).and_return(resource)
              allow(instance_double(S3::UploadFile)).to receive(:call).and_return(nil)
              allow(resource).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(bucket)
              allow(bucket).to receive(:object).with(file_name).and_return(instance_double(Aws::S3::Object))
              allow(bucket.object(file_name)).to receive(:upload_file).with(sample_png_3mb.path)
            end

            it 'uploads file to meta and s3' do
              described_class.new({ media_file: sample_png_3mb, connected_account_id: connected_account.id }).upload
            end

            it 'creates template_media object' do
              template_media = described_class.new({ media_file: sample_png_3mb, connected_account_id: connected_account.id }).upload
              expect(template_media.whatsapp_file_handle).to eq(nil)
              expect(template_media.file_name).to eq("tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/sample_png_3mb_#{SecureRandom.uuid}.png")
              expect(template_media.file_type).to eq('image/png')
            end
          end
        end
      end
    end
  end

  describe '#get' do
    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when connected account is not found' do
      it 'raises not found error' do
        expect { described_class.new({ connected_account_id: -1 }).get }.to raise_error(ExceptionHandler::NotFound, '022006||Connected Account not found')
      end
    end

    context 'when connected accout is present' do
      context 'and current user is not an agent in connected account' do
        it 'raises invalid data error' do
          expect { described_class.new({ connected_account_id: connected_account.id }).get }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
        end
      end

      context 'and current user is an agent in connected account' do
        before do
          create(:agent_user, connected_account: connected_account, user: user, tenant_id: user.tenant_id)
        end

        context 'when template media is valid' do
          it 'returns template_media' do
            t = described_class.new({ connected_account_id: connected_account.id, id: template_media.id }).get
            expect(t.id).to eq(template_media.id)
          end
        end
      end
    end
  end

  describe '#delete_unused_media' do
    context 'when there is any unused media' do
      before do
        unused_media1 = create(:template_media, file_name: 'file_1.png')
        unused_media2 = create(:template_media, file_name: 'file_2.png')
        connected_account = create(:connected_account)
        template = create(:whatsapp_template, connected_account: connected_account)
        used_media = create(:template_media, tenant_id: connected_account.tenant_id, file_name: 'file_3.png')
        header_component = template.components.find_by(component_type: 'HEADER').update!(component_value: used_media.id, component_format: 'IMAGE', media_type: 'STATIC')

        allow(S3::DeleteFileFromS3).to receive(:call).and_return(nil)
        s3_instance = instance_double(S3::DeleteFileFromS3)
        allow(S3::DeleteFileFromS3).to receive(:new).with(['file_1.png', 'file_2.png'], S3_ATTACHMENT_BUCKET).and_return(s3_instance)
        allow(s3_instance).to receive(:call).and_return(nil)
      end

      it 'deletes them' do
        described_class.new({}).delete_unused_media
        expect(TemplateMedia.count).to eq(1)
        expect(TemplateMedia.last.file_name).to eq('file_3.png')
      end
    end
  end
end
