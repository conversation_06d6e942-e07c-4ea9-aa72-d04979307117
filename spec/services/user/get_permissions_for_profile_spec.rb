require 'rails_helper'

RSpec.describe User::GetPermissionsForProfile do
  describe "#call" do
    let(:profile_id) { 1 }

    before do
      @token = FactoryBot.build(:auth_token, user_id: 9, tenant_id: 10).token
      @auth_data = User::TokenParser.parse(@token)
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context "with valid input - " do
      it "returns profile permissions in output" do
        stub_request(:get, SERVICE_IAM + "/v1/profiles/#{profile_id}/permissions").
        with(
          headers: {
          "Authorization" => "Bearer #{ @token }"
          }).
        to_return(status: 200, body: { "permission": [ { "id": 4, "name": "lead", "displayName": nil, "description": nil, "actions": { "read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": true, "note": true, "meeting": true, "readAll": false, "updateAll": false, "deleteAll": false }}] }.to_json, headers: {})

        result = User::GetPermissionsForProfile.call(profile_id)
        expect(result).to eq({"permission"=>[{"id"=>4,"name"=>"lead","displayName"=>nil,"description"=>nil,"actions"=>{"read"=>true,"write"=>true,"update"=>true,"delete"=>true,"email"=>true,"call"=>true,"sms"=>false,"task"=>true,"note"=>true,"meeting"=>true,"readAll"=>false,"updateAll"=>false,"deleteAll"=>false}}]})
      end
    end

    context "with invalid input - " do
      context "when profile is not available" do
        it "raises 404 not found error" do
          stub_request(:get, SERVICE_IAM + "/v1/profiles/#{profile_id}/permissions").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 404, body: "", headers: {})

          expect { User::GetPermissionsForProfile.call(profile_id) }.to raise_error(ExceptionHandler::InvalidProfileError, ErrorCode.invalid_profile)
        end
      end

      context "when something went wrong while API processing" do
        it "raises 500 error" do
          stub_request(:get, SERVICE_IAM + "/v1/profiles/#{profile_id}/permissions").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 500, body: "", headers: {})

          expect { User::GetPermissionsForProfile.call(profile_id) }.to raise_error(ExceptionHandler::InternalServerError, ErrorCode.internal_error)
        end
      end

      context "when Api request invalid" do
        it "raises 400 error" do
          stub_request(:get, SERVICE_IAM + "/v1/profiles/#{profile_id}/permissions").
          with(
            headers: {
            "Authorization" => "Bearer #{ @token }"
            }).
          to_return(status: 400, body: "", headers: {})

          expect { User::GetPermissionsForProfile.call(profile_id) }.to raise_error(ExceptionHandler::InvalidDataError, ErrorCode.invalid_data)
        end
      end
    end
  end
end