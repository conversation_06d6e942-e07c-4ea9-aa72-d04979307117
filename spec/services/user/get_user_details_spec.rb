require 'rails_helper'

RSpec.describe User::GetUserDetails do
  describe '#call' do
    before do
      @token = FactoryBot.build(:auth_token).token
      @auth_data = User::TokenParser.parse(@token)
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'for existing user' do
      before do
        @user = create(:user)
        @result = User::GetUserDetails.call(@user.id, @user.tenant_id)
      end

      it 'returns the same user' do
        expect(@result).to eq(@user)
      end
    end

    context 'for new user' do
      before do
        @user = build(:user, id:10)
        stub_request(:get, "http://localhost:8081/v1/users/#{@user.id}").
         with(
           headers: {
          'Authorization'=>'Bearer '+ @token
           }).
         to_return(status: 200, body: {"id": @user.id, "firstName": "Jane", "lastName": "Doe", "email": {"primary": true,"value": "<EMAIL>"}, "phoneNumbers":[{"type":"MOBILE","code":"IN","value":"7311111111","dialCode":"+91","primary":true}], "timezone": "Asia/Calcutta", "dateFormat": "MMM D, YYYY [at] h:mm a"}.to_json, headers: {})
        @user = User::GetUserDetails.call(@user.id, @user.tenant_id)
      end

      it 'returns the new user' do
        expect(@user.id).to eq(@user.id)
        expect(@user.tenant_id).to eq(@user.tenant_id)
        expect(@user.name).to eq("Jane Doe")
        expect(@user.timezone).to eq("Asia/Calcutta")
        expect(@user.date_format).to eq("MMM D, YYYY [at] h:mm a")
      end
    end

    context 'when validate user is false' do
      let(:user) { create(:user) }

      context 'when new user' do
        let!(:params) { { id: user.id + 1, name: 'Tony Danza', phone_number: '+919988776655' } }

        it 'creates a new user' do
          expect { described_class.call(user.id + 1, user.tenant_id, false, params) }.to change(User, :count).by(1)

          new_user = User.last
          expect(new_user.id).to eq(user.id + 1)
          expect(new_user.name).to eq('Tony Danza')
          expect(new_user.phone_number).to eq('+919988776655')
        end
      end

      context 'when existing user' do
        context 'when phone number is present in params' do
          let!(:params) { { id: user.id, name: 'Tony Danza', phone_number: '+919988776655' } }

          it 'udpates name and phone number' do
            expect { described_class.call(user.id, user.tenant_id, false, params) }.to change(User, :count).by(0)

            expect(user.reload.id).to eq(user.id)
            expect(user.name).to eq('Tony Danza')
            expect(user.phone_number).to eq('+919988776655')
          end
        end

        context 'when phone number is not present in params' do
          let!(:params) { { id: user.id, name: 'Tony Danza' } }

          it 'udpates name and phone number' do
            expect { described_class.call(user.id, user.tenant_id, false, params) }.to change(User, :count).by(0)

            expect(user.reload.id).to eq(user.id)
            expect(user.name).to eq('Tony Danza')
            expect(user.phone_number).to be_nil
          end
        end
      end
    end
  end
end
