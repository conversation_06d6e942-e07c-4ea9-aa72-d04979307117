require 'rails_helper'

RSpec.describe User::GetUserProfileDetails do
  describe "#call" do
    let(:user_id)   { 1 }
    let(:tenant_id) { 1 }

    before do
      @token = FactoryBot.build(:auth_token).token
      @auth_data = User::TokenParser.parse(@token)
      thread = Thread.current
      thread[:auth] = @auth_data
      thread[:token] = @token
    end

    context 'when user is not already present in DB' do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/me").
          with(
            headers: {
              'Authorization' => "Bearer #{ @token }"
            }).
          to_return(status: 200, body:{ firstName: 'Test', lastName: 'User', phoneNumbers: [{ type: "MOBILE", code: "IN", value: "7311111111", dialCode: "+91", primary: true}] }.to_json, headers: {})
      end

      it 'returns user profile correctly' do
        user = User::GetUserProfileDetails.call(user_id, tenant_id)
        user_in_db = User.find(user_id)

        expect(user.id).to eq(user_in_db.id)
        expect(user.name).to eq(user_in_db.name)
        expect(user.phone_number).to eq(user_in_db.phone_number)
        expect(user.tenant_id).to eq(user_in_db.tenant_id)
      end
    end

    context 'when user is present without phone number in DB' do
      before do
        @user = create(:user, name: "Test User" , tenant_id: tenant_id, phone_number: nil)

        stub_request(:get, "http://localhost:8081/v1/users/me").
        with(
          headers: {
            'Authorization' => "Bearer #{ @token }"
          }).
        to_return(status: 200, body:{ firstName: 'Test', lastName: 'User', phoneNumbers: [{ type: "MOBILE", code: "IN", value: "7311111111", dialCode: "+91", primary: true}] }.to_json, headers: {})
      end

      it 'returns user profile correctly' do
        user = User::GetUserProfileDetails.call(@user.id, tenant_id)
        user_in_db = User.find(@user.id)

        expect(user.id).to eq(user_in_db.id)
        expect(user.name).to eq(user_in_db.name)
        expect(user.phone_number).to eq(user_in_db.phone_number)
        expect(user.tenant_id).to eq(user_in_db.tenant_id)
      end
    end

    context 'when user with phone number is already present' do
      before { @user = create(:user, name: "Test User") }

      it 'returns user profile correctly' do
        user_in_db = User.find(@user.id)

        expect(@user.id).to eq(user_in_db.id)
        expect(@user.name).to eq(user_in_db.name)
        expect(@user.phone_number).to eq(user_in_db.phone_number)
        expect(@user.tenant_id).to eq(user_in_db.tenant_id)
      end
    end
  end
end
