# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User::Summary do
  let(:user)               { create(:user) }
  let(:valid_auth_token)   { build(:auth_token, :with_whatsapp_update_all_read_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)          { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data)  { User::TokenParser.parse(invalid_auth_token.token) }

  describe '#get' do
    context 'when users ids is blank' do
      it 'returns blank response' do
        expect(described_class.new([]).get).to eq([])
      end
    end

    context 'when user ids are present and valid' do
      before do
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=1,2")
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          )
          .to_return(status: 200, body: file_fixture('user-summary-response.json').read)
      end

      context 'when primary phone number is present' do
        it 'returns phone number in hash' do
          summary_response = described_class.new([1, 2], valid_auth_token.token).get

          expect(summary_response.first).to eq({ 'id' => 1, 'name' => 'Tony Stark', 'phone_number' => '+919898989898' })
        end
      end

      context 'when primary phone number is not present' do
        it 'returns does not return phone number in hash' do
          summary_response = described_class.new([1, 2], valid_auth_token.token).get

          expect(summary_response.last).to eq({ 'id' => 2, 'name' => 'Captain America' })
        end
      end
    end

    context 'when token is blank' do
      it 'raises authentication error' do
        expect { described_class.new([1, 2]).get }.to raise_error(ExceptionHandler::AuthenticationError, '022001')
      end
    end

    context 'when summary api gives error' do
      def stub_user_summary_request(user_ids, status)
        stub_request(:get, "http://localhost:8081/v1/users/summary?id=#{user_ids.join(',')}")
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          )
          .to_return(status: status, body: '')
      end

      context 'when status 200 and blank response' do
        before { stub_user_summary_request([1, 2], 200) }

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with("User::Summary invalid response")
          expect(Rails.logger).to receive(:error).with("User::Summary iam 500")
          expect { described_class.new([1, 2], valid_auth_token.token).get }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when bad request' do
        before { stub_user_summary_request([1, 2], 400) }

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with("User::Summary iam 400")
          expect { described_class.new([1, 2], valid_auth_token.token).get }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when unauthorized' do
        before { stub_user_summary_request([1, 2], 401) }

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with("User::Summary iam 401")
          expect { described_class.new([1, 2], valid_auth_token.token).get }.to raise_error(ExceptionHandler::AuthenticationError, '022002')
        end
      end

      context 'when not found' do
        before { stub_user_summary_request([1, 2], 404) }

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with("User::Summary iam 404")
          expect { described_class.new([1, 2], valid_auth_token.token).get }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end

      context 'when internal server error' do
        before { stub_user_summary_request([1, 2], 500) }

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with("User::Summary iam 500")
          expect { described_class.new([1, 2], valid_auth_token.token).get }.to raise_error(ExceptionHandler::InternalServerError, '022004')
        end
      end

      context 'when some different error' do
        before { stub_user_summary_request([1, 2], 503) }

        it 'raises invalid data error' do
          expect(Rails.logger).to receive(:error).with("User::Summary iam 500")
          expect { described_class.new([1, 2], valid_auth_token.token).get }.to raise_error(ExceptionHandler::InvalidDataError, '022003')
        end
      end
    end
  end
end
