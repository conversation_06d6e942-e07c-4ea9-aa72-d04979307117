# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VariableMappingsService do
  let(:user) { create(:user) }
  let(:another_user) { create(:user, tenant_id: user.tenant_id) }
  let(:valid_auth_token) { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data) { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token) { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:invalid_auth_data) { User::TokenParser.parse(invalid_auth_token.token) }
  let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
  let(:user_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user) }
  let(:other_template) { create(:whatsapp_template, connected_account: other_connected_account, created_by: another_user) }
  let(:variable_mapping) { create(:variable_mapping, whatsapp_template: user_template, component_type: 'HEADER', template_variable: 53) }
  let(:other_variable_mapping) { create(:variable_mapping, whatsapp_template: user_template, component_type: 'BODY', template_variable: 33) }
  let(:variable_mapping_for_other_template) { create(:variable_mapping, whatsapp_template: other_template) }

  describe '#get' do
    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when user has read only' do
      before do
        variable_mapping
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
      end

      context 'when user is template creator' do
        it 'returns variable mappings for given template' do
          expect(described_class.new({ id: user_template.id }).get).to eq([variable_mapping])
        end
      end

      context 'when user is connected account agent' do
        before { create(:agent_user, tenant_id: user.tenant_id, connected_account_id: other_connected_account.id, user_id: user.id) }

        it 'returns variable mappings for given template' do
          expect(described_class.new({ id: other_template.id }).get).to eq([variable_mapping_for_other_template])
        end
      end
    end

    context 'when user has read all' do
      it 'returns template' do
        expect(described_class.new({ id: other_template.id }).get).to eq([variable_mapping_for_other_template])
      end
    end

    context 'when template not found' do
      context 'when template does not exist for tenant' do
        it 'raises error' do
          expect{ described_class.new({ id: -1 }).get }.to raise_error(ExceptionHandler::NotFound, '022006')
        end
      end

      context 'when user cannot access template' do
        before do
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
        end

        it 'raises error' do
          expect{ described_class.new({ id: other_template.id }).get }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
        end
      end
    end

    context 'when user does not have read' do
      before { Thread.current[:auth] = invalid_auth_data }

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to read whatsapp template")
        expect{ described_class.new({ id: user_template.id }).get }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe '#save' do
    let(:variable_mapping_params) do
      [
        {
          id: variable_mapping.id,
          component_type: "HEADER",
          template_variable: 53,
          entity: "createdBy",
          internal_name: "firstName",
          fallback_value: "John Updated",
          parent_entity: "lead",
          field_type: "TEXT_FIELD"
        },
        {
          id: other_variable_mapping.id,
          component_type: "BODY",
          template_variable: 33,
          entity: "lead",
          internal_name: "lastName",
          fallback_value: "Doe Updated",
          parent_entity: "lead",
          field_type: "TEXT_FIELD"
        }
      ]
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
      Thread.current[:token] = valid_auth_token.token
    end

    context 'when user has permission' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
          status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
        )
        stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
      end

      it 'updates the variables' do
        variable_mappings = described_class.new({ id: user_template.id, variable_mappings: variable_mapping_params }).save

        expect(variable_mappings.pluck(:fallback_value)).to match_array(["John Updated", "Doe Updated"])
      end
    end

    context 'when extra variables are passed' do
      it 'raises invalid data error' do
        expect {
          described_class.new({ id: user_template.id, variable_mappings: variable_mapping_params << { component_type: 'BODY', template_variable: 43 } }).save
        }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Fewer or  more variables than expected")
      end
    end

    context 'when existing variable is missing' do
      it 'raises invalid data error' do
        expect {
          described_class.new({ id: user_template.id, variable_mappings: [variable_mapping_params[1]] }).save
        }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Fewer or  more variables than expected")
      end
    end

    context 'when existing variable is repeated' do
      it 'raises invalid data error' do
        expect {
          described_class.new({ id: user_template.id, variable_mappings: variable_mapping_params << { component_type: 'BODY', template_variable: 33 } }).save
        }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure template variables are not duplicated")
      end
    end

    context 'when template is does not exist' do
      it 'raises not found error' do
        expect {
          described_class.new({ id: -1 }).save
        }.to raise_error(ExceptionHandler::NotFound, '022006')
      end
    end

    context 'when user does not have permission to update template' do
      before do
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(false)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update_all').and_return(false)
      end

      it 'raises unauthorized error' do
        expect {
          described_class.new({ id: user_template.id, variable_mappings: variable_mapping_params }).save
        }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end

    context 'when invalid variable is added' do
      before do
        stub_request(:get, 'http://localhost:8086/v1/entities/label').to_return(
          status: 200, body: file_fixture('entity-labels-response.json').read, headers: {}
        )
        stub_request(:get, 'http://localhost:8086/v1/entities/lead/fields?entityType=lead&custom-only=false')
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          ).to_return(status: 200, body: file_fixture('fields/lead-fields-response.json').read)
        variable_mapping_params.last[:field_type] = 'URL'
      end

      it 'raises invalid data error' do
        expect {
          described_class.new({ id: user_template.id, variable_mappings: variable_mapping_params }).save
        }.to raise_error(ExceptionHandler::InvalidDataError, '022020||Invalid variable mapping for variable 33 in BODY')
      end
    end
  end

  describe '#prepare_variables_for_template' do
    context 'when variables change' do
      before do
        create(:variable_mapping, whatsapp_template: user_template, component_type: HEADER, template_variable: 1)
        create(:variable_mapping, whatsapp_template: user_template, component_type: BODY, template_variable: 1)
        create(:variable_mapping, whatsapp_template: user_template, component_type: BODY, template_variable: 2)
        create(:variable_mapping, whatsapp_template: user_template, component_type: BODY, template_variable: 3)
        create(:variable_mapping, whatsapp_template: user_template, component_type: BUTTON_URL, template_variable: 1)
        create(:variable_mapping, whatsapp_template: user_template, component_type: BUTTON_COPY_CODE, template_variable: 1)
        body_component = user_template.components.find_by(component_type: BODY)
        body_component.update(component_text: body_component.component_text.gsub('{{1}}', '{{100}}'))
        @variable_to_delete = user_template.variable_mappings.find { |variable_mapping| variable_mapping.template_variable == 1 && variable_mapping.component_type == BODY }
        @existing_variables = user_template.variable_mappings - [@variable_to_delete]
        @prepared_variables = described_class.new(whatsapp_template: user_template).prepare_variables_for_template
      end

      it 'adds a new entry with id null for new variable' do
        new_entries = @prepared_variables.values.select { |variable| variable[:id].nil? }

        expect(new_entries.count).to eq(1)
        expect(new_entries.first).to eq({
          id: nil,
          component_type: 'BODY',
          template_variable: 100,
          entity: nil,
          internal_name: nil,
          fallback_value: nil,
          field_type: nil,
          tenant_id: user_template.tenant_id,
          parent_entity: user_template.entity_type
        })
      end

      it 'returns existng variables hash' do
        existing_entries = @prepared_variables.values.select { |variable| variable[:id].present? && variable[:_destroy].nil? }

        expect(existing_entries.count).to eq(5)
        expect(existing_entries).to match_array(@existing_variables.map { |variable| variable.as_json.except(*%w[whatsapp_template_id created_at updated_at]).symbolize_keys! })
      end

      it 'adds an entry to delete for missing variable' do
        entries_to_delete = @prepared_variables.values.select { |variable| variable[:_destroy].present? }

        expect(entries_to_delete.count).to eq(1)
        expect(entries_to_delete.first).to eq({
          id: @variable_to_delete.id,
          _destroy: true
        })
      end
    end
  end
end
