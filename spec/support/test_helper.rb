module TestHelper

  def get_test_jwt(user_id = 1, tenant_id= 99)
    payload = {
      iss: 'sell',
      data: {
        expiry: (Time.current + 1.hour).to_i,
        userId: 1,
        tenantId: 99,
        expiresIn: (Time.current + 1.hour).to_i,
        tokenType: 'bearer',
        accessToken: SecureRandom.uuid,
        username: '<EMAIL>',
        permissions:[{
          'id' => 12,
          'name' =>  'user',
          'description' =>  'has access to user',
          'limits' =>  -1,
          'units' =>  'count',
          'action' =>  {
            'read' => true,
            'write' => true,
            'update' => true,
            'delete' => true,
            'email' => false,
            'call' => false,
            'sms' => true,
            'task' => false,
            'note' => false,
            'readAll' => false,
            'updateAll' => true,
            'deleteAll' => false
          }
        }]
      }
    }
    JWT.encode payload, nil, 'none'
  end

  def invalid_jwt
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
  end

  def jwt_with_insufficient_data
    payload = { iss: 'sell', data: { expiry: (Time.current + 1.hour).to_i, accessToken: SecureRandom.uuid,}}
    JWT.encode payload, nil, 'none'
  end
end
