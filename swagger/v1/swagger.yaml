---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/v1/messages":
    post:
      summary: Creates message
      tags:
      - Message App
      security:
      - bearerAuth: []
      parameters:
      - name: body
      - in: header
        name: Authorization
        required: true
        description: Client token
        schema:
          type: string
      responses:
        '201':
          description: App created
        '401':
          description: Authentication failed
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - direction
              properties:
                content:
                  type: text
servers:
- url: https://{defaultHost}
  variables:
    defaultHost:
      default: www.example.com
